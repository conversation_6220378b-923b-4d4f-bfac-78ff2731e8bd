#!/usr/bin/env node

// 代码清理脚本
// 用于清理冗余CSS、优化代码结构

const fs = require('fs')
const path = require('path')
const { execSync } = require('child_process')

// 配置
const config = {
  srcDir: path.join(__dirname, '../src'),
  excludeDirs: ['node_modules', '.git', 'dist'],
  cssExtensions: ['.css', '.scss', '.sass', '.less'],
  jsExtensions: ['.js', '.ts', '.vue'],
  cleanupRules: {
    removeEmptyLines: true,
    removeComments: false, // 保留注释用于文档
    removeUnusedImports: true,
    optimizeImages: true
  }
}

// 工具函数
function getAllFiles(dir, extensions = []) {
  const files = []
  
  function traverse(currentDir) {
    const items = fs.readdirSync(currentDir)
    
    for (const item of items) {
      const fullPath = path.join(currentDir, item)
      const stat = fs.statSync(fullPath)
      
      if (stat.isDirectory() && !config.excludeDirs.includes(item)) {
        traverse(fullPath)
      } else if (stat.isFile()) {
        const ext = path.extname(item)
        if (extensions.length === 0 || extensions.includes(ext)) {
          files.push(fullPath)
        }
      }
    }
  }
  
  traverse(dir)
  return files
}

// CSS 清理函数
function cleanupCSS() {
  console.log('🧹 清理 CSS 文件...')
  
  const cssFiles = getAllFiles(config.srcDir, config.cssExtensions)
  let cleanedCount = 0
  
  cssFiles.forEach(file => {
    try {
      let content = fs.readFileSync(file, 'utf8')
      const originalSize = content.length
      
      // 移除多余的空行
      if (config.cleanupRules.removeEmptyLines) {
        content = content.replace(/\n\s*\n\s*\n/g, '\n\n')
      }
      
      // 移除行尾空格
      content = content.replace(/[ \t]+$/gm, '')
      
      // 优化CSS属性顺序（简单版本）
      content = content.replace(/\{([^}]+)\}/g, (match, properties) => {
        const props = properties.split(';').filter(p => p.trim())
        const sorted = props.sort((a, b) => {
          const orderMap = {
            'display': 1, 'position': 2, 'top': 3, 'right': 4, 'bottom': 5, 'left': 6,
            'width': 7, 'height': 8, 'margin': 9, 'padding': 10, 'border': 11,
            'background': 12, 'color': 13, 'font': 14, 'text': 15
          }
          
          const getOrder = (prop) => {
            const key = prop.trim().split(':')[0].split('-')[0]
            return orderMap[key] || 999
          }
          
          return getOrder(a) - getOrder(b)
        })
        
        return `{${sorted.join(';')}}`
      })
      
      if (content.length !== originalSize) {
        fs.writeFileSync(file, content)
        cleanedCount++
        console.log(`  ✅ 清理: ${path.relative(config.srcDir, file)}`)
      }
    } catch (error) {
      console.error(`  ❌ 错误: ${file} - ${error.message}`)
    }
  })
  
  console.log(`📊 CSS 清理完成: ${cleanedCount} 个文件被优化`)
}

// JavaScript/Vue 清理函数
function cleanupJS() {
  console.log('🧹 清理 JavaScript/Vue 文件...')
  
  const jsFiles = getAllFiles(config.srcDir, config.jsExtensions)
  let cleanedCount = 0
  
  jsFiles.forEach(file => {
    try {
      let content = fs.readFileSync(file, 'utf8')
      const originalSize = content.length
      
      // 移除多余的空行
      if (config.cleanupRules.removeEmptyLines) {
        content = content.replace(/\n\s*\n\s*\n/g, '\n\n')
      }
      
      // 移除行尾空格
      content = content.replace(/[ \t]+$/gm, '')
      
      // 移除未使用的导入（简单检测）
      if (config.cleanupRules.removeUnusedImports) {
        const imports = content.match(/import\s+.*?from\s+['"][^'"]+['"]/g) || []
        imports.forEach(importStatement => {
          const match = importStatement.match(/import\s+(?:\{([^}]+)\}|\*\s+as\s+(\w+)|(\w+))\s+from/)
          if (match) {
            const imported = match[1] || match[2] || match[3]
            if (imported && !content.includes(imported.trim())) {
              // 简单检测，实际项目中需要更复杂的AST分析
              console.log(`  ⚠️  可能未使用的导入: ${imported} in ${path.relative(config.srcDir, file)}`)
            }
          }
        })
      }
      
      if (content.length !== originalSize) {
        fs.writeFileSync(file, content)
        cleanedCount++
        console.log(`  ✅ 清理: ${path.relative(config.srcDir, file)}`)
      }
    } catch (error) {
      console.error(`  ❌ 错误: ${file} - ${error.message}`)
    }
  })
  
  console.log(`📊 JavaScript/Vue 清理完成: ${cleanedCount} 个文件被优化`)
}

// 检查重复代码
function checkDuplicates() {
  console.log('🔍 检查重复代码...')
  
  const allFiles = getAllFiles(config.srcDir, [...config.cssExtensions, ...config.jsExtensions])
  const codeBlocks = new Map()
  const duplicates = []
  
  allFiles.forEach(file => {
    try {
      const content = fs.readFileSync(file, 'utf8')
      const lines = content.split('\n')
      
      // 检查5行以上的代码块
      for (let i = 0; i < lines.length - 5; i++) {
        const block = lines.slice(i, i + 5).join('\n').trim()
        if (block.length > 50) { // 忽略太短的块
          if (codeBlocks.has(block)) {
            duplicates.push({
              block: block.substring(0, 100) + '...',
              files: [codeBlocks.get(block), file]
            })
          } else {
            codeBlocks.set(block, file)
          }
        }
      }
    } catch (error) {
      console.error(`  ❌ 错误: ${file} - ${error.message}`)
    }
  })
  
  if (duplicates.length > 0) {
    console.log('⚠️  发现重复代码:')
    duplicates.forEach((dup, index) => {
      console.log(`  ${index + 1}. ${dup.block}`)
      console.log(`     文件: ${dup.files.map(f => path.relative(config.srcDir, f)).join(', ')}`)
    })
  } else {
    console.log('✅ 未发现明显的重复代码')
  }
}

// 分析文件大小
function analyzeFileSize() {
  console.log('📏 分析文件大小...')
  
  const allFiles = getAllFiles(config.srcDir)
  const fileSizes = allFiles.map(file => {
    const stat = fs.statSync(file)
    return {
      file: path.relative(config.srcDir, file),
      size: stat.size,
      ext: path.extname(file)
    }
  }).sort((a, b) => b.size - a.size)
  
  console.log('📊 最大的文件:')
  fileSizes.slice(0, 10).forEach((item, index) => {
    const sizeKB = (item.size / 1024).toFixed(2)
    console.log(`  ${index + 1}. ${item.file} (${sizeKB} KB)`)
  })
  
  // 按类型统计
  const typeStats = {}
  fileSizes.forEach(item => {
    if (!typeStats[item.ext]) {
      typeStats[item.ext] = { count: 0, totalSize: 0 }
    }
    typeStats[item.ext].count++
    typeStats[item.ext].totalSize += item.size
  })
  
  console.log('\n📊 按文件类型统计:')
  Object.entries(typeStats).forEach(([ext, stats]) => {
    const avgSize = (stats.totalSize / stats.count / 1024).toFixed(2)
    const totalSizeKB = (stats.totalSize / 1024).toFixed(2)
    console.log(`  ${ext || '无扩展名'}: ${stats.count} 个文件, 总计 ${totalSizeKB} KB, 平均 ${avgSize} KB`)
  })
}

// 生成优化报告
function generateReport() {
  console.log('📋 生成优化报告...')
  
  const report = {
    timestamp: new Date().toISOString(),
    summary: {
      totalFiles: getAllFiles(config.srcDir).length,
      cssFiles: getAllFiles(config.srcDir, config.cssExtensions).length,
      jsFiles: getAllFiles(config.srcDir, config.jsExtensions).length
    },
    recommendations: [
      '考虑使用 CSS-in-JS 或 CSS Modules 来避免样式冲突',
      '使用 Tree Shaking 来移除未使用的代码',
      '考虑代码分割来减少初始加载时间',
      '使用图片压缩工具优化图片资源',
      '启用 Gzip 压缩来减少传输大小'
    ]
  }
  
  const reportPath = path.join(__dirname, '../optimization-report.json')
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2))
  console.log(`📄 报告已生成: ${reportPath}`)
}

// 主函数
function main() {
  console.log('🚀 开始代码清理和优化...\n')
  
  try {
    cleanupCSS()
    console.log('')
    
    cleanupJS()
    console.log('')
    
    checkDuplicates()
    console.log('')
    
    analyzeFileSize()
    console.log('')
    
    generateReport()
    
    console.log('\n✅ 代码清理和优化完成!')
    console.log('💡 建议运行 npm run lint 来检查代码质量')
    console.log('💡 建议运行 npm run build 来测试构建是否正常')
    
  } catch (error) {
    console.error('❌ 清理过程中出现错误:', error.message)
    process.exit(1)
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main()
}

module.exports = {
  cleanupCSS,
  cleanupJS,
  checkDuplicates,
  analyzeFileSize,
  generateReport
}
