# aiAutoFront

This template should help get you started developing with Vue 3 in Vite.

## Recommended IDE Setup

[VSCode](https://code.visualstudio.com/) + [Volar](https://marketplace.visualstudio.com/items?itemName=Vue.volar) (and disable Vetur).

## Customize configuration

See [Vite Configuration Reference](https://vite.dev/config/).

## Project Setup

```sh
npm install
```

### Compile and Hot-Reload for Development

```sh
npm run dev
```

### Compile and Minify for Production

```sh
npm run build
```

## 服务器部署

由于服务器 centos7 的系统版本不符合 node18 的系统要求。单独升级依赖库又导致系统崩溃的风险，所以前端的编辑在 docker 虚拟机中进行

### 在服务器上 进入 docker 虚拟机

```sh
(ai-venv) [aiats@iZuf63fqhs0pypp5co2w9uZ automatrix]$ docker run -it -v /home/<USER>/automatrixfront/aiAutoFront/:/app node:18.19.1-bullseye-slim bash
root@37b69604861f:/#
```

### 进入 docker 工作目录 安装依赖包和编译

```sh
root@37b69604861f:/# cd app/

# 安装依赖包
root@37b69604861f:/app# npm install
up to date, audited 552 packages in 3s
139 packages are looking for funding
  run `npm fund` for details
6 vulnerabilities (4 moderate, 2 high)
To address issues that do not require attention, run:
  npm audit fix
To address all issues (including breaking changes), run:
  npm audit fix --force
Run `npm audit` for details.
npm notice
npm notice New major version of npm available! 10.2.4 -> 11.4.2
npm notice Changelog: https://github.com/npm/cli/releases/tag/v11.4.2
npm notice Run npm install -g npm@11.4.2 to update!
npm notice

# 编译
root@37b69604861f:/app# npm run build
> aiautofront@0.0.0 build
> vite build
NODE_ENV=production is not supported in the .env file. Only NODE_ENV=development is supported to create a development build of your project. If you need to set process.env.NODE_ENV, you can set it in the Vite config instead.
vite v6.2.2 building for production...
✓ 2103 modules transformed.
dist/index.html                                     0.53 kB │ gzip:   0.33 kB
dist/assets/JetBrainsMono-Light-Bgq2Dg64.woff2     93.86 kB
dist/assets/gd-BLI9lMR7.svg                       130.25 kB │ gzip:  18.69 kB
dist/assets/ProjectList-BrwXU-ll.css                0.06 kB │ gzip:   0.08 kB
dist/assets/DataList-BT1_J8Cw.css                   0.12 kB │ gzip:   0.11 kB
dist/assets/requirementList-Cj_CV-_p.css            0.13 kB │ gzip:   0.10 kB
dist/assets/Layout-CkhDxAiV.css                     0.17 kB │ gzip:   0.15 kB
dist/assets/evaluateTaskList-D_SFJXhu.css           0.91 kB │ gzip:   0.40 kB
dist/assets/testEv-DE34d6YA.css                     1.43 kB │ gzip:   0.37 kB
dist/assets/ssoLogin-BIhsoi7N.css                   2.45 kB │ gzip:   0.76 kB
dist/assets/suiteList-BNSWPvvT.css                  3.17 kB │ gzip:   0.77 kB
dist/assets/manageHome-nAZAgRjr.css                15.82 kB │ gzip:   3.06 kB
dist/assets/index-PEo7XP7t.css                    355.67 kB │ gzip:  50.59 kB
dist/assets/projectApi-CJ9lrNmX.js                  0.31 kB │ gzip:   0.19 kB
dist/assets/rag-Bef5W71V.js                         0.32 kB │ gzip:   0.18 kB
dist/assets/requireApi-BJMXj4C2.js                  0.35 kB │ gzip:   0.19 kB
dist/assets/materialApi-V2bcwgu2.js                 0.41 kB │ gzip:   0.20 kB
dist/assets/suitApi-B_-HzFZx.js                     0.83 kB │ gzip:   0.31 kB
dist/assets/ssoLogin-CnNeuZom.js                    1.89 kB │ gzip:   1.00 kB
dist/assets/Layout-Bt7fk3ZU.js                      2.79 kB │ gzip:   1.25 kB
dist/assets/suiteExecuteList-C0lcuq9m.js            5.26 kB │ gzip:   2.06 kB
dist/assets/ProjectList-CDwPUnzW.js                 6.33 kB │ gzip:   2.31 kB
dist/assets/requirementList-C8Li_iEz.js             7.04 kB │ gzip:   2.20 kB
dist/assets/evaluateTaskList-Qfsyu18A.js            8.78 kB │ gzip:   3.25 kB
dist/assets/DataList-D3clbd42.js                   10.97 kB │ gzip:   2.72 kB
dist/assets/testEv-B_mcJ8oF.js                     25.02 kB │ gzip:   5.54 kB
dist/assets/request-B5N7Sf6F.js                    36.31 kB │ gzip:  14.60 kB
dist/assets/suiteList-moJNUWtJ.js                  76.40 kB │ gzip:  16.17 kB
dist/assets/manageHome-fZG6eO-c.js                833.30 kB │ gzip: 288.73 kB
dist/assets/reports-m8lsqBvD.js                 1,036.21 kB │ gzip: 343.58 kB
dist/assets/index-DIP-lorO.js                   1,228.99 kB │ gzip: 391.87 kB

(!) Some chunks are larger than 500 kB after minification. Consider:
- Using dynamic import() to code-split the application
- Use build.rollupOptions.output.manualChunks to improve chunking: https://rollupjs.org/configuration-options/#output-manualchunks
- Adjust chunk size limit for this warning via build.chunkSizeWarningLimit.
✓ built in 21.95s
root@37b69604861f:/app#

# 退出 ctrl+d
root@37b69604861f:/app#
exit
(ai-venv) [aiats@iZuf63fqhs0pypp5co2w9uZ automatrix]$
```
