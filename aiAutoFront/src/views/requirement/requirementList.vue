<template>
  <el-row>
    <el-col :span="24" style="height:100%">
      <el-row style="margin-left: 0.3%; margin-top: 1%">
        <el-select  filterable v-model="projectId" placeholder="请选择项目" style="width: 10%;">
    <el-option
      v-for="item in projectData"
      :key="item.value"
      :label="item.label"
      :value="item.value"

    />
  </el-select>

        <el-button style="color: #615ced; margin-left: 1%" plain
        @click="RequireList(projectId)"
          >查询</el-button
        >

          <el-button style="color: #615ced" @click="testAdd" plain
            >新增</el-button
          >

          <el-button style="color: #615ced" plain @click="resetQuery">重置</el-button>
      </el-row>

      <el-table
        :data="Case"
        style="width: 98%; margin-top: 1%; margin-left: 0.3%; height: 100%"
      >
        <el-table-column type="selection" min-width="5%"> </el-table-column>
        <el-table-column
          prop="id"
          label="需求ID"
          min-width="10%"
          sortable
          show-overflow-tooltip
        >
        </el-table-column>


        <el-table-column
          prop="name"
          label="需求名称"
          min-width="10%"
          sortable
          show-overflow-tooltip
        >
        </el-table-column>


        <el-table-column
          prop="prd_link"
          label="需求地址"
          min-width="10%"
          sortable
          show-overflow-tooltip
        ></el-table-column>

        <el-table-column
          prop="version"
          label="需求版本"
          min-width="10%"
          sortable
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          prop="project_id"
          label="项目名称"
          min-width="10%"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          prop="description"
          label="描述"
          min-width="10%"
          sortable
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          prop="creator__username"
          label="创建人"
          min-width="10%"
          sortable
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          prop="create_time"
          label="创建时间"
          min-width="10%"
          sortable
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          prop="update_time"
          label="更新时间"
          min-width="10%"
          sortable
          show-overflow-tooltip
        ></el-table-column>

        <el-table-column label="操作" width="140" fixed="right">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-button
                type="primary"
                size="small"
                link
                @click="handlerun(row)"
              >
                <el-icon><Edit /></el-icon>
                编辑
              </el-button>
              <el-button
                type="danger"
                size="small"
                link
                @click="del(row)"
              >
                <el-icon><Delete /></el-icon>
                删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <el-footer style="float: right;background: #fff;width: 100%;" >
          <el-pagination
    :page-size=20
    :pager-count="11"
    layout="prev, pager, next"
    :total=requeireTotal
    @current-change="handleCurrentChange"
   style="float: right;margin-top: 0.8%;"
  />
        </el-footer>
    </el-col>
    <el-dialog
      title="新增需求"
      :model-value="dialogVisible"
      :before-close="handleClose"
      width="50%"
      height="90%"
    >

  <el-form
      ref="ruleFormRef"
      :model="requireData"
      :rules="requireRules"
      status-icon
      label-width="120px"
      class="demo-ruleForm"
    >

    <el-form-item label="需求名称" prop="name">
      <el-input
        v-model="requireData.name"
        autocomplete="off"
        style="width: 50%;"
        placeholder="请输入需求名称"
        maxlength="100"
        show-word-limit
      />
    </el-form-item>
    <el-form-item label="需求版本" prop="version">
      <el-input
        v-model="requireData.version"
        autocomplete="off"
        style="width: 50%;"
        placeholder="请输入需求版本"
        maxlength="50"
        show-word-limit
      />
    </el-form-item>
    <el-form-item label="需求地址" prop="prd_link">
      <el-input
        v-model="requireData.prd_link"
        autocomplete="off"
        style="width: 50%;"
        placeholder="请输入需求地址"
        maxlength="500"
        show-word-limit
      />
    </el-form-item>
    <el-form-item label="所属项目" prop="project">
      <el-select
        v-model="requireData.project"
        placeholder="请选择项目"
        @focus="getProject"
        style="width: 50%;"
        clearable
      >
        <el-option
          v-for="item in projectData"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </el-form-item>
    <el-form-item label="描述" prop="description">
      <el-input
        v-model="requireData.description"
        type="textarea"
        :rows="3"
        autocomplete="off"
        style="width: 50%;"
        placeholder="请输入需求描述"
        maxlength="500"
        show-word-limit
      />
    </el-form-item>

  </el-form>

  <template #footer>
      <span class="dialog-footer">
        <el-button @click="closeDialogVisible()">取消</el-button>
        <el-button @click="saveRequire">
          保存
        </el-button>
      </span>
    </template>
    </el-dialog>

      <!-- 编辑页面 -->
      <el-dialog
        title="编辑"
        :model-value="updateVisible"
        :before-close="updateClose"
        width="50%"
      height="90%"
      >

    <el-form
      ref="updateFormRef"
      :model="updateForm"
      :rules="requireRules"
      status-icon
      label-width="120px"
      class="demo-ruleForm"
    >
      <el-form-item label="需求名称" prop="name">
        <el-input
          v-model="updateForm.name"
          placeholder="请输入需求名称"
          style="width: 50%;"
          maxlength="100"
          show-word-limit
        />
      </el-form-item>
      <el-form-item label="版本" prop="version">
        <el-input
          v-model="updateForm.version"
          placeholder="请输入需求版本"
          style="width: 50%;"
          maxlength="50"
          show-word-limit
        />
      </el-form-item>
      <el-form-item label="需求地址" prop="prd_link">
        <el-input
          v-model="updateForm.prd_link"
          placeholder="请输入需求地址"
          style="width: 50%;"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>
      <el-form-item label="描述" prop="description">
        <el-input
          v-model="updateForm.description"
          type="textarea"
          :rows="3"
          placeholder="请输入需求描述"
          style="width: 50%;"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>
    </el-form>
    <template #footer>
        <span class="dialog-footer">
          <el-button @click="updateClose">取消</el-button>
          <el-button @click="saveBut">
            保存
          </el-button>
        </span>
      </template>
      </el-dialog>
  </el-row>

</template>

<script setup>
import dayjs from 'dayjs';
import { onMounted, ref,reactive,nextTick,onBeforeMount } from "vue";
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Edit, Delete } from '@element-plus/icons-vue'
import { requireList1,addRequire,updateRequire,delRequire } from '@/api/requireApi';
import { serviceList } from '@/api/projectApi';

let defaultProjectId=ref(null);
let requeireTotal=ref(0);
let requeirepage=ref(1);
let updateVisible=ref(false);
let dialogVisible = ref(false);
let requireData =reactive( {
    name: "",
    version: "",
    prd_link: "",
    author: JSON.parse(localStorage.getItem('loginvalue')),
    description: "",
    project: null
  })
let updateForm = ref({

    name: "",
    version: "",
    prd_link: "",
    author: "1",
    description: "",
    project: 8
});

let projectId=ref(null)
let projectData = ref([]);
let Case = ref([]);

// 表单引用
const ruleFormRef = ref();
const updateFormRef = ref();

// 校验规则
const requireRules = reactive({
  name: [
    { required: true, message: '需求名称为必填项', trigger: 'blur' },
    { min: 2, max: 100, message: '需求名称长度需在2-100字符之间', trigger: 'blur' }
  ],
  version: [
    { required: true, message: '需求版本为必填项', trigger: 'blur' },
    { min: 1, max: 50, message: '需求版本长度需在1-50字符之间', trigger: 'blur' }
  ],
  prd_link: [
    {
      pattern: /^(https?:\/\/)?([\da-z\.-]+)\.([a-z\.]{2,6})([\/\w \.-]*)*\/?$/,
      message: '请输入有效的URL地址',
      trigger: 'blur'
    }
  ],
  project: [
    { required: true, message: '请选择所属项目', trigger: 'change' }
  ],
  description: [
    { max: 500, message: '描述长度不能超过500字符', trigger: 'blur' }
  ]
});

function closeDialogVisible(){
  dialogVisible.value = false;
  requireData.name = '';
  requireData.version = '';
  requireData.prd_link = '';
  requireData.description = '';
  requireData.project = null;
  // 清除表单验证状态
  if (ruleFormRef.value) {
    ruleFormRef.value.clearValidate();
  }
}
// 新增：重置查询条件并刷新列表
function resetQuery() {
  // 清空项目选择值
  projectId.value = null;
  // 刷新需求列表（调用现有获取列表函数）
  RequireList();
}
//获取项目
function getProject() {
  let params = {
      page: 1,
      page_size: 100


   }
serviceList(params).then((res) => {

projectData.value=res.resp.map(item => {
    return {
      value: item.id,
      label: item.name,
    }
  })
  defaultProjectId.value = projectData.value[0].value;

  // 在获取到默认项目ID后，调用需求列表
  RequireList(defaultProjectId.value);
});
}


function updateClose() {
  updateVisible.value = false;
  // 清除编辑表单验证状态
  if (updateFormRef.value) {
    updateFormRef.value.clearValidate();
  }
}
function testAdd() {
  dialogVisible.value = true;
  requireData =reactive( {
    name: "",
    version: "",
    prd_link: "",
    author: localStorage.getItem('loginvalue'),
    description: "",
    project: null
  })
}
// 二次确认删除功能
async function del(val) {
  try {
    await ElMessageBox.confirm(
      `确定要删除需求 "${val.name || '此项'}" 吗？此操作不可撤销。`,
      '删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
        confirmButtonClass: 'el-button--danger',
      }
    );

    // 执行删除操作
    const res = await delRequire({req_id: val.id});
    if (res.success) {
      ElMessage.success('删除成功');
      RequireList(val.project_id);
    } else {
      ElMessage.error(res.msg || '删除失败');
    }
  } catch (error) {
    // 用户取消删除或发生错误
    if (error !== 'cancel') {
      ElMessage.error('删除操作失败');
    }
  }
}
let update_project_id=ref(null)
function handlerun(val){
 updateVisible.value=true;
 updateForm.value = Object.assign({}, val);
  update_project_id.value=val.project_id
  }
  function saveBut() {
    if (!updateFormRef.value) return;

    updateFormRef.value.validate((valid) => {
      if (valid) {
        let data = {
          name: updateForm.value.name,
          version: updateForm.value.version,
          prd_link: updateForm.value.prd_link,
          author: localStorage.getItem('loginvalue'),
          description: updateForm.value.description,
          id: updateForm.value.id
        }

        updateRequire(data).then((res) => {
          if(res.success){
            ElMessage.success('需求更新成功');
            updateVisible.value = false;
            RequireList(update_project_id.value);
          } else {
            ElMessage.error(res.msg || '需求更新失败');
          }
        }).catch(() => {
          ElMessage.error('需求更新失败');
        });
      } else {
        ElMessage.warning('请检查表单填写是否正确');
      }
    });
  }
function handleClose() {
  dialogVisible.value = false;
}

function saveRequire(){
  if (!ruleFormRef.value) return;

  ruleFormRef.value.validate((valid) => {
    if (valid) {
      addRequire(requireData).then(() => {
        ElMessage.success('需求添加成功');
        dialogVisible.value = false;
        RequireList(requireData.project);
      }).catch(() => {
        ElMessage.error('需求添加失败');
      });
    } else {
      ElMessage.warning('请检查表单填写是否正确');
    }
  });
}

async function RequireList(val){
  let params = {
    page_size:20,
    page: requeirepage.value,
  }

  // 使用传入的val，如果没有则使用defaultProjectId，最后才使用默认值8
  const projectId = val || defaultProjectId.value || 8;

  await requireList1(projectId,params).then((res) => {

Case.value=res.resp.map(item => {
   requeireTotal.value=res.total
    return {
      ...item,
      create_time:dayjs(item.create_time).format('YYYY-MM-DD HH:mm:ss'),
      update_time:dayjs(item.update_time).format('YYYY-MM-DD HH:mm:ss')
    }
  })


});
}
function handleCurrentChange(val){
  requeirepage.value=val
  RequireList()

}
onBeforeMount(()=>{
getProject()
})
onMounted(() => {
  nextTick(() => {
    // getProject() 已在 onBeforeMount 中调用
    // RequireList() 将在 getProject 完成后自动调用
})
})

</script>

<style scoped>
.projectinput:before {
  content: "项目名称";
}
.requirementinput:before {
  content: "需求名称";
}

/* 操作按钮样式 */
.action-buttons {
  display: flex;
  gap: 8px;
  align-items: center;
}

.action-buttons .el-button {
  padding: 4px 8px;
  font-size: 12px;
  border-radius: 6px;
}
</style>
