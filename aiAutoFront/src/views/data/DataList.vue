<template>
  <el-row>
    <el-col :span="24" style="height: 100%">
      <el-row style="margin-left: 0.3%; margin-top: 1%">
        <el-select
          v-model="store.projectvalue"
          class="m-2"
          filterable
          placeholder="请选择项目"
          style="width: 10%;margin-top: -0.1%;"
        >
          <el-option
            v-for="item in store.projectData"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
        <el-button
          style="color: #615ced; margin-left: 1%"
          plain
          @click="store.handleProject"
          >查询</el-button
        >
        <el-button
          style="color: #615ced; margin-left: 1%"
          plain
          @click="store.resetButton"
          >重置</el-button
        >
        <el-row style="margin-left: 1%">
          <el-button style="color: #615ced" @click="testAdd" plain
            >新增</el-button
          >
        </el-row>
      </el-row>

       <el-table
        :data="store.materialTableData"
        style="width: 100%; margin-top: 1%; margin-left: 0.3%; height: 100%"
      >
       <el-table-column
          prop="id"
          label="ID"
          min-width="10%"
          sortable
          show-overflow-tooltip
        />
        <el-table-column
          prop="project__name"
          label="项目名称"
          min-width="15%"
          sortable
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          prop="content_question"
          label="问题"
          min-width="15%"
          show-overflow-tooltip
        >
        </el-table-column>

        <el-table-column
          prop="content_answer"
          label="预期答案"
          min-width="15%"
          sortable
          show-overflow-tooltip
        ></el-table-column>

        <el-table-column
          prop="type__name"
          label="物料类型"
          min-width="15%"
          sortable
          show-overflow-tooltip
        ></el-table-column>

        <el-table-column
          prop="source__name"
          label="物料来源"
          min-width="15%"
          sortable
          show-overflow-tooltip
        ></el-table-column>

        <el-table-column
          prop="creator__username"
          label="创建人"
          min-width="15%"
          sortable
          show-overflow-tooltip
        ></el-table-column>

        <el-table-column
          prop="share"
          label="共享等级"
          min-width="15%"
          sortable
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          prop="create_time"
          label="创建时间"
          min-width="30%"
          sortable
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          prop="update_time"
          label="更新时间"
          min-width="30%"
          sortable
          show-overflow-tooltip
        ></el-table-column>



              <el-table-column label="操作" min-width="50%">


          <template #default="{ row }">
            <div class="action-buttons">
              <el-button
                type="primary"
                size="small"
                link
                @click="store.handlerun(row)"
              >
                <el-icon><Edit /></el-icon>
                编辑
              </el-button>
              <el-button
                type="danger"
                size="small"
                link
                @click="store.funDelMaterial(row)"
              >
                <el-icon><Delete /></el-icon>
                删除
              </el-button>
              <el-button
                type="info"
                size="small"
                link
                @click="store.copyMateriaData(row)"
              >
                <el-icon><CopyDocument /></el-icon>
                复制
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <el-footer style="float: right; background: #fff; width: 100%">
        <!-- @current-change="handleCurrentChange" -->
        <el-pagination
          :page-size="20"
          :pager-count="11"
          layout="prev, pager, next"
          :total=store.dataTotal
          @current-change="store.handleCurrentChange"
          style="float: right; margin-top: 0.8%"
        />
      </el-footer>
    </el-col>
    <el-dialog
      title="新增物料"
      :model-value="store.dialogMaterialVisible"
      :before-close="handleClose"
      width="50%"
      height="90%"
    >
      <el-form
        ref="addFormRef"
        :model="store.addRuleForm"
        :rules="materialRules"
        label-width="120px"
        class="demo-ruleForm"
        :size="formSize"
        status-icon
      >
        <el-form-item label="问题" prop="content_question">
          <el-input
            v-model="store.addRuleForm.content_question"
            style="width: 50%"
            placeholder="请输入问题内容"
            type="textarea"
            :rows="3"
            maxlength="1000"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="预期答案" prop="content_answer">
          <el-input
            v-model="store.addRuleForm.content_answer"
            style="width: 50%"
            placeholder="请输入预期答案"
            type="textarea"
            :rows="3"
            maxlength="2000"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="上下文内容" prop="content">
          <el-input
            v-model="store.addRuleForm.content"
            style="width: 50%"
            placeholder="请输入上下文内容"
            type="textarea"
            :rows="4"
            maxlength="5000"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="所属项目" prop="project">
          <el-select
            v-model="store.addRuleForm.project"
            class="m-2"
            placeholder="请选择所属项目"
            style="width: 50%"
            clearable
          >
            <el-option
              v-for="item in store.projectData"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="数据类型" prop="type">
          <el-select
            v-model="store.addRuleForm.type"
            class="m-2"
            placeholder="请选择数据类型"
            style="width: 50%"
            clearable
          >
            <el-option
              v-for="item in store.materialtypeData"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="数据来源" prop="source">
          <el-select
            v-model="store.addRuleForm.source"
            class="m-2"
            placeholder="请选择数据来源"
            style="width: 50%"
            clearable
          >
            <el-option
              v-for="item in store.materialsourceData"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="共享等级" prop="share">
          <el-select
            v-model="store.addRuleForm.share"
            placeholder="请选择共享等级"
            style="width: 50%"
            clearable
          >
            <el-option label="私有" :value="1" />
            <el-option label="团队共享" :value="2" />
            <el-option label="公开" :value="3" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleAddMaterial">
            保存
          </el-button>
          <el-button @click="closeForm">取消</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>

    <el-dialog
      title="编辑00"
      :model-value="store.updateVisible"
      :before-close="store.updateClose"
      width="50%"
      height="90%"
    >
      <el-form
        ref="updateFormRef"
        :model="store.updateForm"
        :rules="materialRules"
        label-width="120px"
        class="demo-ruleForm"
        :size="formSize"
        status-icon
      >
        <el-form-item label="问题" prop="content_question">
          <el-input
            v-model="store.updateForm.content_question"
            style="width: 50%"
            placeholder="请输入问题内容"
            type="textarea"
            :rows="3"
            maxlength="1000"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="预期答案" prop="content_answer">
          <el-input
            v-model="store.updateForm.content_answer"
            style="width: 50%"
            placeholder="请输入预期答案"
            type="textarea"
            :rows="3"
            maxlength="2000"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="上下文内容" prop="content">
          <el-input
            v-model="store.updateForm.content"
            style="width: 50%"
            placeholder="请输入上下文内容"
            type="textarea"
            :rows="4"
            maxlength="5000"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="所属项目" prop="project_id">
          <el-select
            v-model="store.updateForm.project_id"
            class="updateFormProject"
            placeholder="请选择所属项目"
            style="width: 50%"
            clearable
          >
            <el-option
              v-for="item in store.projectData"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="数据类型" prop="type_id">
          <el-select
            v-model="store.updateForm.type_id"
            class="updateFormProject"
            placeholder="请选择数据类型"
            style="width: 50%"
            clearable
          >
            <el-option
              v-for="item in store.materialtypeData"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="数据来源" prop="count">
          <el-select
            v-model="store.updateForm.source_id"
            class="updateFormProject"
            :placeholder="store.updateForm.source__name"
            style="width: 30%"
          >
            <el-option
              v-for="item in store.materialsourceData"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="共享等级" prop="share">
          <el-select
            v-model="store.updateForm.share"
            placeholder="请选择共享等级"
            style="width: 50%"
            clearable
          >
            <el-option label="私有" :value="1" />
            <el-option label="团队共享" :value="2" />
            <el-option label="公开" :value="3" />
          </el-select>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleUpdateClose">取消</el-button>
          <el-button @click="handleUpdateMaterial"> 保存 </el-button>
        </span>
      </template>
    </el-dialog>
  </el-row>
</template>

<script setup>
import dayjs from "dayjs";

import { materialStore } from "@/pinia/materialstore";
import { materialList } from "@/api/materialApi";

import { ref, onMounted, reactive } from "vue";
import { ElMessage } from "element-plus";
import { Edit, Delete, CopyDocument } from '@element-plus/icons-vue'
const store = materialStore();

const formSize = ref("default");
const addFormRef = ref();
const updateFormRef = ref();

let dialogVisible = ref(false);

// 物料校验规则
const materialRules = reactive({
  content_question: [
    { required: true, message: '问题内容为必填项', trigger: 'blur' },
    { min: 5, max: 1000, message: '问题内容长度需在5-1000字符之间', trigger: 'blur' }
  ],
  content_answer: [
    { required: true, message: '预期答案为必填项', trigger: 'blur' },
    { min: 1, max: 2000, message: '预期答案长度需在1-2000字符之间', trigger: 'blur' }
  ],
  content: [
    { max: 5000, message: '上下文内容长度不能超过5000字符', trigger: 'blur' }
  ],
  project: [
    { required: true, message: '请选择所属项目', trigger: 'change' }
  ],
  project_id: [
    { required: true, message: '请选择所属项目', trigger: 'change' }
  ],
  type: [
    { required: true, message: '请选择数据类型', trigger: 'change' }
  ],
  type_id: [
    { required: true, message: '请选择数据类型', trigger: 'change' }
  ],
  source: [
    { required: true, message: '请选择数据来源', trigger: 'change' }
  ],
  source_id: [
    { required: true, message: '请选择数据来源', trigger: 'change' }
  ],
  share: [
    { required: true, message: '请选择共享等级', trigger: 'change' }
  ]
});

function testAdd() {
  store.dialogMaterialVisible = true;
  store.addRuleForm = {
    content_question: '',
    content_answer: '',
    content: '',
    project: null,
    type: null,
    source: null,
    share: null
  };
  // 清除表单验证状态
  setTimeout(() => {
    if (addFormRef.value) {
      addFormRef.value.clearValidate();
    }
  }, 0);
}

function handleClose() {
  store.dialogMaterialVisible = false;
  // 清除表单验证状态
  if (addFormRef.value) {
    addFormRef.value.clearValidate();
  }
}

function closeForm(){
  store.dialogMaterialVisible = false;
  store.addRuleForm = {};
  // 清除表单验证状态
  if (addFormRef.value) {
    addFormRef.value.clearValidate();
  }
}

// 新增物料表单验证
function handleAddMaterial() {
  if (!addFormRef.value) return;

  addFormRef.value.validate((valid) => {
    if (valid) {
      store.addMaterialForm()
      ElMessage.success('物料添加成功')

    } else {
      ElMessage.warning('请检查表单填写是否正确');
    }
  });
}

// 编辑物料表单验证
function handleUpdateMaterial() {
  if (!updateFormRef.value) return;

  updateFormRef.value.validate((valid) => {
    if (valid) {
      store.putUpdateMateria().then(() => {
        ElMessage.success('物料更新成功');
      }).catch(() => {
        ElMessage.error('物料更新失败');
      });
    } else {
      ElMessage.warning('请检查表单填写是否正确');
    }
  });
}

// 关闭编辑对话框
function handleUpdateClose() {
  store.updateClose();
  // 清除表单验证状态
  if (updateFormRef.value) {
    updateFormRef.value.clearValidate();
  }
}
onMounted(async () => {
  // await store.getMaterialList();
  store.getserviceList();
  store.getmaterialtype();
  store.getmaterialsource();
});
</script>

<style scoped>
/* ... existing code ... */
.updateFormProject :deep(.el-select__placeholder.is-transparent) {
  color: #606266 !important;
}

/* 操作按钮样式 */
.action-buttons {
  display: flex;
  gap: 8px;
  align-items: center;
}

.action-buttons .el-button {
  padding: 4px 8px;
  font-size: 12px;
  border-radius: 6px;
}
</style>
