<!-- src/views/model/ModelDetail.vue -->
<template>
    <el-dialog
      title="模型详情"
      v-model="dialogVisible"
      :before-close="handleClose"
      width="50%"
      style="margin-top: 10vh;"
      custom-class="model-detail-dialog"
    >
      <el-form :model="model" label-width="120px" label-position="left">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="模型编号:">
              <el-tag type="info">{{ model.id }}</el-tag>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="模型名称:">
              <el-tag>{{ model.model_name }}</el-tag>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="模型类型:">
              <el-tag>{{ model.model_type }}</el-tag>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="URL:">
              <el-link type="primary" :href="model.api_base_url" target="_blank">{{ model.api_base_url }}</el-link>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="KEY:">
              <el-input v-model="model.api_key" readonly></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="Temperature:">
              <el-input v-model="model.temperature" readonly></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="Top N:">
              <el-input v-model="model.top_p" readonly></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="Token:">
              <el-input v-model="model.max_tokens" readonly></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="流式输出:">
              <el-switch v-model="model.stream" active-text="True" inactive-text="False" disabled></el-switch>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="创建时间:">
              <el-input v-model="model.created_time" readonly></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="更新时间:">
              <el-input v-model="model.updated_time" readonly></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="备注:">
              <el-input v-model="model.remark" type="textarea" :rows="3" readonly></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleClose">关闭</el-button>
        </span>
      </template>
    </el-dialog>
  </template>
  
  <script setup>
  import { ref } from 'vue';
  
  const dialogVisible = ref(false);
  const model = ref({});
  
  // 方法：打开详情对话框
  function openDetail(detailModel) {
    model.value = detailModel;
    dialogVisible.value = true;
  }
  
  // 方法：关闭详情对话框
  function handleClose() {
    dialogVisible.value = false;
  }
  
  // 暴露方法给父组件
  defineExpose({
    openDetail
  });
  </script>
  
  <style scoped>
  .model-detail-dialog {
    border-radius: 10px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  }
  
  .el-form-item {
    margin-bottom: 15px;
  }
  
  .el-form-item__label {
    font-weight: bold;
    color: #333;
  }
  
  .el-tag {
    margin-top: 5px;
  }
  
  .el-input__inner {
    background-color: #f5f7fa;
    border: 1px solid #e4e7ed;
    color: #606266;
  }
  
  .el-input__inner[readonly] {
    cursor: not-allowed;
  }
  
  .el-switch {
    margin-top: 5px;
  }
  
  .el-link {
    margin-top: 5px;
  }
  
  .el-textarea__inner {
    background-color: #f5f7fa;
    border: 1px solid #e4e7ed;
    color: #606266;
  }
  
  .el-dialog__title {
    font-size: 18px;
    font-weight: bold;
    color: #333;
  }
  
  .el-dialog__header {
    background-color: #f5f7fa;
    border-bottom: 1px solid #e4e7ed;
    padding: 20px;
  }
  
  .el-dialog__body {
    padding: 20px;
  }
  
  .el-dialog__footer {
    background-color: #f5f7fa;
    border-top: 1px solid #e4e7ed;
    padding: 10px 20px;
  }
  </style>