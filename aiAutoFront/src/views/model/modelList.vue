<template>
  <el-row style="margin-left: 0.3%; margin-top: 1%">
    <el-input
      class="modelnameinput"
      placeholder="请输入内容"
      style="width: 15%"
      v-model="searchQuery"
    ></el-input>
    <el-button style="color: #615ced; width: 3%; margin-left: 1%;" @click="modelquery" plain
      >查询
    </el-button>
    <el-button style="color: #615ced; width: 3%; margin-left: 1%;" @click="clearFilters" plain
      >重置
    </el-button>
  </el-row>
  <el-row style="margin-left: 0.3%; margin-top: 1%">
    <el-button style="color: #615ced; width: 4%;" @click="modelAdd" plain
      >新增
    </el-button>
    <el-button style="color: #615ced; width: 4%;" @click="modelDelAll" plain
      >删除
    </el-button>
  </el-row>

  <el-table
    :data="modelListData"
    style="width: 90%; margin-top: 1%; margin-left: 0.3%; height: 90%"
    @expand-change="handleExpandChange"
    @selection-change="handleSelectionChange"
  >
      <el-table-column type="selection" min-width="5%"> </el-table-column>
      <el-table-column
        prop="id"
        label="模型编号"
        min-width="10%"
        sortable
        show-overflow-tooltip
      >
  </el-table-column>
  
      <el-table-column
        prop="model_name"
        label="模型名称"
        min-width="10%"
        sortable
        show-overflow-tooltip
      >
    </el-table-column>
    <el-table-column
        prop="model_type"
        label="模型类型"
        min-width="10%"
        sortable
        show-overflow-tooltip
      >
    </el-table-column>
      <el-table-column
        prop="api_base_url"
        label="URL"
        min-width="10%"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="api_key"
        label="KEY"
        min-width="10%"
        sortable
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="temperature"
        label="Temperature"
        min-width="10%"
        sortable
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="top_p"
        label="Top N"
        min-width="10%"
        sortable
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="max_tokens"
        label="Token"
        min-width="12%"
        sortable
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="stream"
        label="流式输出"
        min-width="12%"
        sortable
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="created_time"
        label="创建时间"
        min-width="12%"
        sortable
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="updated_time"
        label="更新时间"
        min-width="12%"
        sortable
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="remark"
        label="备注"
        min-width="12%"
        sortable
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column label="操作" min-width="25%">
      <template #default="scope">
        <el-button color="#626aef" plain @click="modelDetail(scope.row)">详情</el-button>
        <el-button color="#626aef" plain @click="modelEdit(scope.row)">编辑</el-button>
        <el-button color="#626aef" plain @click="confirmDelete(scope.row)">删除</el-button>
      </template>
    </el-table-column>
    </el-table>


  <el-dialog
  title="配置大模型"
  v-model="dialogVisible"
  :before-close="handleClose1"
  width="50%"
  style="margin-top: 10vh;" >

  <el-form :model="form" :rules="rules" ref="formRef" label-width="100px">
      <el-form-item label="模型名称: " prop="model_name">
        <el-input v-model="form.modelName" placeholder="请输入内容" style="width: 80%;"></el-input>
      </el-form-item>
      <el-form-item label="模型类型: " prop="model_type">
        <el-select v-model="form.modelType" placeholder="请输入内容" style="width: 80%;">
          <el-option label="openai" value="openai"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="URL: " prop="api_base_url">
        <el-input v-model="form.apiUrl" placeholder="请输入内容" style="width: 80%;"></el-input>
      </el-form-item>
      <el-form-item label="KEY: " prop="api_key">
        <el-input v-model="form.apiKey" placeholder="请输入内容" style="width: 80%;"></el-input>
      </el-form-item>
      <el-form-item label="Temperature: " prop="temperature">
        <el-slider v-model="form.temperature" :min="0" :max="1" :step="0.1" show-input></el-slider>
      </el-form-item>
      <el-form-item label="Top N: " prop="top_p">
        <el-slider v-model="form.topN" :min="0" :max="1" :step="0.1" show-input></el-slider>
      </el-form-item>
      <el-form-item label="Token: " prop="max_tokens">
        <el-input v-model="form.token" placeholder="请输入内容" style="width: 80%;"></el-input>
      </el-form-item>
      <el-form-item label="Stream: " prop="stream">
        <el-select v-model="form.stream" placeholder="请选择" style="width: 80%;">
          <el-option label="True" value="True"></el-option>
          <el-option label="False" value="False"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="备注: " prop="remark">
        <el-input v-model="form.remark" placeholder="请输入内容" style="width: 80%;"></el-input>
      </el-form-item>
    </el-form>
  <template #footer>
    <span class="dialog-footer">
      <el-button @click="dialogVisible = false">取消</el-button>
      <el-button type="primary" @click="saveModel">保存</el-button>
    </span>
    
  </template>
</el-dialog>
<el-dialog
    title="确认删除"
    v-model="deleteDialogConfirm"
    width="30%"
    class="delete-dialog"
  >
  <span v-if="selectedModel">确定要删除该模型吗？</span>
  <span v-else>确定要删除选中的模型吗？</span>
  <template #footer>
      <span class="dialog-footer">
        <el-button @click="deleteDialogConfirm = false">取消</el-button>
        <el-button type="danger" @click="handleDelete">删除</el-button>
      </span>
    </template>
  </el-dialog>

  <ModelDetail ref="modelDetailComponent" />

</template>



<script setup>

import { modelConfig, modelList, modelUpdate, modelDelete } from '@/api/modelApi';
import { onMounted, ref } from 'vue';
import dayjs from 'dayjs';
import ModelDetail from './modelDetail.vue';

let searchQuery = ref(''); //搜索框
let dialogVisible = ref(false); //新增模型弹窗
let deleteDialogConfirm = ref(false); //取消删除
let selectedModel = ref(null); //单个模型勾选
let selectedModels = ref([]); //多个模型勾选
let formRef = ref(null); //新增模型表单
let modelDetailComponent = ref(null); //详情

let currentPage = ref(1); // 当前页码
let pageSize = ref(10); // 每页显示的条数
let total = ref(0); // 总条数



let form = ref({
  id: '',
  modelName: '',
  modelType: '',
  apiUrl: '',
  apiKey: '',
  temperature: 0.3,
  topN: 0.5,
  token: '',
  stream: '',
  remark:''
});

// 配置模型表单验证规则
let rules = ref({
  modelName: [
    { required: true, message: '请输入模型名称', trigger: 'blur' }
  ],
  modelType: [
    { required: true, message: '请输入模型类型', trigger: 'blur' }
  ],
  apiUrl: [
    { required: true, message: '请输入URL', trigger: 'blur' }
  ],
  apiKey: [
    { required: true, message: '请输入KEY', trigger: 'blur' }
  ],
  temperature: [
    { required: false, message: '请输入Temperature', trigger: 'blur' },
    { type: 'number', message: 'Temperature 必须是数字', trigger: 'blur' }
  ],
  topN: [
    { required: false, message: '请输入Top N', trigger: 'blur' },
    { type: 'number', message: 'Top N 必须是数字', trigger: 'blur' }
  ],
  token: [
    { required: false, message: '请输入Token', trigger: 'blur' },
  ],
  stream: [
    { required: false, message: '请选择Stream', trigger: 'change' },
  ],
  remark: [
    { required: false, message: '请输入备注', trigger: 'blur' },
  ]
});

let modelListData = ref([]); // 定义表格数据

let params = {
  model_name: '',
  page: '1',
  page_size: '10'
};


//查询
function modelquery() {
  let params = {
    model_name: searchQuery.value,
    page: '1',
    page_size: '10'
};
  modelList({...params}).then((res) => {
    modelListData.value = res.resp.map(item => {
      return {
        ...item,
        created_time: dayjs(item.created_time).format('YYYY-MM-DD HH:mm:ss'),
        updated_time: dayjs(item.updated_time).format('YYYY-MM-DD HH:mm:ss')
      };
    });
    total.value = res.total; // 设置总条数
  }).catch((error) => {
    console.error('Error fetching model list:', error);
  });
}

onMounted(() => {
  fetchModelList();
});

function clearFilters(){
  searchQuery.value = '';
  modelquery();
}
//刷新模型列表数据
function fetchModelList(){
  modelquery(); // 使用 modelquery 函数来获取数据
}


//清空新建模型参数
function modelAdd() {
  dialogVisible.value = true;
  // Clear form fields when adding a new model
  form.value = {
    modelName: '',
    modelType:'',
    apiUrl: '',
    apiKey: '',
    temperature: 0.3,
    topN: 0.5,
    token: '',
    stream: '',
    remark: '' 
  };
}
//编辑模型
function modelEdit(row) {
  dialogVisible.value = true;

  form.value = {
    id: parseInt(row.id),
    modelName: row.model_name,
    modelType: row.model_type,
    apiUrl: row.api_base_url,
    apiKey: row.api_key,
    temperature: row.temperature,
    topN: row.top_p,
    token: row.max_tokens,
    stream: row.stream,
    remark: row.remark 
  };
}
//新增模型保存
function saveModel() {
  
  formRef.value.validate((valid) => {
    if (valid) {
      const modelData = {
        model_name: form.value.modelName,
        model_type: form.value.modelType,
        api_base_url: form.value.apiUrl,
        api_key: form.value.apiKey,
        temperature: parseFloat(form.value.temperature),
        top_p: parseFloat(form.value.topN),
        max_tokens: parseInt(form.value.token),
        stream: form.value.stream,
        remark: form.value.remark
      };
      if (form.value.id) {
        // 编辑模型
        modelUpdate(modelData, form.value.id).then((res) => {
          console.log('Model edited successfully:', res);
          // 刷新模型列表
          fetchModelList().catch((error) => {
            console.error('Error fetching model list:', error);
          });
          dialogVisible.value = false;
        }).catch((error) => {
          console.error('Error editing model:', error);
          if (error.response && error.response.data) {
            console.error('Server Error:', error.response.data);
            alert('编辑失败: ' + error.response.data.msg);
          }
        });dialogVisible.value=false;
      } else {
        // 新增模型
        modelConfig(modelData).then((res) => {
          console.log('Model added successfully:', res);
          // 刷新模型列表
          fetchModelList().catch((error) => {
            console.error('Error fetching model list:', error);
          });
        }).catch((error) => {
          console.error('Error adding model:', error);
          if (error.response && error.response.data) {
            console.error('Server Error:', error.response.data);
            alert('保存失败: ' + error.response.data.msg);
          }
        });
        dialogVisible.value=false;
      }
  }
  });
}
// 关闭对话框
function handleClose1() {
  dialogVisible.value = false;
}

// 删除单个模型
function confirmDelete(row) {
  selectedModel.value = row;
  deleteDialogConfirm.value = true;
}
//获取多个模型ID
function handleSelectionChange(selection) {
  selectedModels.value = selection;
}

//删除多个模型
function modelDelAll() {
  if (selectedModels.value.length === 0) {
    alert('请选择要删除的模型');
    return;
  }
   // 过滤掉无效记录（例如 id 为空）
   const validModels = selectedModels.value.filter(model => model.id);

if (validModels.length === 0) {
  alert('所有选中的模型无效，请重新选择');
  return;
}

deleteDialogConfirm.value = true;
}

// 删除操作
async function handleDelete() {
  try {
    if (selectedModel.value) {
      // 删除单个模型
      await modelDelete(selectedModel.value.id);
      console.log('Model deleted successfully');
    } else if (selectedModels.value.length > 0) {
      // 删除多个模型
      for (const model of selectedModels.value) {
        try {
          await modelDelete(model.id);
          console.log(`Model with ID ${model.id} deleted successfully`);
        } catch (error) {
          console.error(`Error deleting model with ID ${model.id}:`, error);
          if (error.response && error.response.data) {
            console.error('Server Error:', error.response.data);
            alert(`删除失败: ${error.response.data.msg}`);
          }
        }
      }
    }
    // 刷新模型列表
    fetchModelList();
  } catch (error) {
    console.error('Error during deletion process:', error);
  } finally {
    deleteDialogConfirm.value = false;
  }
}


// 详情
function modelDetail(row) {
  modelDetailComponent.value.openDetail(row); 
}

</script>

<style>
.model-row {
  margin-left: 0.3%;
  margin-top: 1%;
}

.modelnameinput:before {
  content: "模型名称: ";
  margin-right: 3%;
}

.query-button {
  color: #615ced;
  width: 3%;
  margin-left: 1%;
}

.add-button {
  color: #615ced;
  width: 4%;
}

.model-table {
  width: 90%;
  margin-top: 1%;
  margin-left: 0.3%;
  height: 90%;
}

.model-dialog {
  margin-top: 10vh;
}

.dialog-footer button:first-child {
  margin-right: 10px;
}

.el-dialog__body {
  padding: 20px 20px 0;
}

.el-form-item {
  margin-bottom: 15px;
}


</style>    