<template>
  <div class="common-layout">
    <el-container>
      <el-header
        style="
          background-image: linear-gradient(315deg, #6772ff 0, #73f7ec 100%);
        "
      >
        <div style="float: left; color: #fff; margin-top: -5px">
          <h2>logo</h2>
        </div>
        <el-dropdown trigger="click" style="float: right">
          <div class="userinfo" style="color: #fff">
            <h3>{{loginlabel}}</h3>
          </div>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item @click="testloginout"
                >退出登录</el-dropdown-item
              >
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </el-header>
      <el-container>
        <el-aside width="200px">
   <!-- 使用el-menu替换原ul列表 -->
   <el-menu
      default-active="1"
        class="el-menu-vertical-demo"
        @open="handleOpen"
        @close="handleClose"
    >
      <!-- 原列表项（示例） -->
      <el-menu-item index="1" @click="activeTab = 'resources'">
        <el-icon><HomeFilled /></el-icon>
        <template #title>项目管理</template>
      </el-menu-item>
      <el-menu-item index="2" @click="activeTab = 'requires'">
                <el-icon><Connection /></el-icon>
                <template #title>需求管理</template>
      </el-menu-item>
      <el-menu-item index="3" @click="activeTab = 'board'">
                <el-icon><MessageBox /></el-icon>
                <template #title>物料管理</template>
      </el-menu-item>
      <el-menu-item index="4" @click="activeTab = 'suite'">
                <el-icon><Grid /></el-icon>
                <template #title>用例集</template>
      </el-menu-item>

      <!-- 新增任务列表（带两个子项） -->
      <el-sub-menu index="5" >
        <template #title>
         <el-icon><MessageBox /></el-icon>
          <span>任务列表</span>
        </template>
        <!-- 子项1：执行列表 -->
        <el-menu-item index="5-1" @click="activeTab = 'suitesexecute'">执行列表</el-menu-item>
        <!-- 子项2：评估任务 -->
        <el-menu-item index="5-2" @click="activeTab = 'report'">评估任务</el-menu-item>
      </el-sub-menu>

      <el-menu-item index="6" @click="activeTab = 'teste'">
        <el-icon><PieChart/></el-icon>
        <template #title>数据总览 </template>
      </el-menu-item>

<<<<<<< HEAD
              <li
                :class="{ active: activeTab === 'requires' }"
                class="sc-jIyAiq cXDkWL"
                @click="activeTab = 'requires'"
              >
                <i class="sc-eWPXlR ixlxbr nav-icon">
                  <img src="@/assets/svg/requirement.svg" style="width: 80px" />
                </i>

                <span style="color: #6772ff">需求管理</span>
              </li>

              <li
                :class="{ active: activeTab === 'board' }"
                class="sc-jIyAiq cXDkWL active local-link"
                @click="activeTab = 'board'"
              >
                <div class="sc-lnsxGb YmUSM"></div>
                <i class="sc-eWPXlR ixlxbr nav-icon">
                  <img src="@/assets/svg/material.svg" style="width: 80px" />
                </i>

            <span style="color: #6772FF;">用例评估</span>
          </li>

          <li
                :class="{ active: activeTab === 'model' }"
                class="sc-jIyAiq cXDkWL"
                @click="activeTab = 'model'"
              >
                <i class="sc-eWPXlR ixlxbr nav-icon">
                  <img src="@/assets/svg/modelManage.svg" style="width: 40px" />
                </i>

                <span style="color: #6772ff">模型管理</span>
              </li>

          <li
                :class="{ active: activeTab === 'prompt' }"
                class="sc-jIyAiq cXDkWL"
                @click="activeTab = 'prompt'"
              >
                <i class="sc-eWPXlR ixlxbr nav-icon">
                  <img src="@/assets/svg/dialog.svg" style="width: 40px" />
                </i>

                <span style="color: #6772ff">对话管理</span>
              </li>

          <li
                :class="{ active: activeTab === 'template' }"
                class="sc-jIyAiq cXDkWL"
                @click="activeTab = 'template'"
              >
                <i class="sc-eWPXlR ixlxbr nav-icon">
                  <img src="@/assets/svg/promptManage.svg" style="width: 40px" />
                </i>

                <span style="color: #6772ff">提示词管理</span>
              </li>
        </ul>
=======
          <el-menu-item v-if="isAdmin"  @click="openNewTab">
            <el-icon><Avatar /></el-icon>
            <template #title>后台管理</template>
          </el-menu-item>
>>>>>>> 268fdeb0d7f547dfa25b4b62a67ee987ce883c98

    </el-menu>
        </el-aside>
        <el-main style="padding: 0px;height: 100%;">
            <component :is="currentTabComponent"/>
        </el-main>
      </el-container>
    </el-container>



  </div>

</template>
<script setup>
import suiteList from "../suite/suiteList.vue";
import requirement from "../requirement/requirementList.vue";
import DataManagement from "../data/DataList.vue";
import projectManagement from "../project/ProjectList.vue";
import TaskManagement from "../evaluation/evaluationList.vue";
<<<<<<< HEAD
import ModelManagement from "../model/modelList.vue"
import PromptManagement from "../prompt/prompt.vue"
import DialogView from '../prompt/dialog.vue';
import PromptTemplate from "../template/TemplateList.vue"
import PromptComparison from "../template/PromptComparison.vue";
import { useRouter } from 'vue-router';
import { ref, computed } from 'vue';
=======
import evaluateTaskList from "../evaluation/evaluateTaskList.vue";
import teste from "../evaluation/testEv.vue";
import suitesexecute from "../suite/suiteExecuteList.vue";

// import reportManagement from "../../components/ChartComponent.vue";
import reportOverview from "../../components/reportOverview.vue";
import { useRouter } from "vue-router";
import { ref, computed,reactive } from "vue";
import { suiteCase} from '@/api/suitApi';

>>>>>>> 268fdeb0d7f547dfa25b4b62a67ee987ce883c98

const router = useRouter();
const value90 = ref(false)
let rengongval=ref(false)
let optionspg1=ref(true)
let loginlabel=localStorage.getItem('loginlabel')
let formLabelAlign3Options = ref([])

let formLabelAlign3= reactive({
    user_input: "",
    response: "",
    reference: "",
    remark: "",
    score: '',
    version: "",
    suit_id: '',
    test_case_id: ''
})

const formLabelAlign = reactive({

suit_id: '',
test_case_id: '',
version: '',
type: '',
user_input: '',
retrieved_contexts: '',
response: '',
reference: '',
})
function remoteMethod(query) {
  if (query) {

    setTimeout(() => {

      formLabelAlign3Options.value = list333.value.filter((item) => {
        return item.label.toLowerCase().includes(query.toLowerCase())
      })
    }, 200)

  } else {
    formLabelAlign3Options.value = []
  }
}
let activeTab = ref("resources"); // 默认显示小组管理
const currentTabComponent = computed(() => {
  switch (activeTab.value) {
    case "suite":
      return suiteList;
    case "task":
      return TaskManagement;
    case "board":
      return DataManagement;
    case "resources":
      return projectManagement;
    case "requires":
      return requirement;
      case "report":
<<<<<<< HEAD
      return reportManagement;
    case "model":
      return ModelManagement;
    case "prompt":
      return PromptManagement;
    case "dialog":
      return DialogView;
    case "template":
      return PromptTemplate;
=======
      return evaluateTaskList;
      case "testE":
      return requirement;
      case "teste":
      return teste;
      case "suitesexecute":
      return suitesexecute;

      case "overview":
      return reportOverview;
>>>>>>> 268fdeb0d7f547dfa25b4b62a67ee987ce883c98
    default:
      return GroupManagement;
  }
});


<<<<<<< HEAD

=======
>>>>>>> 268fdeb0d7f547dfa25b4b62a67ee987ce883c98
function testloginout() {
  localStorage.removeItem("token");
  router.push("/ssoLogin");
}
  const isAdmin =localStorage.getItem('isAdmin')==='1'
  // const isAdmin1 =ref(localStorage.getItem('isAdmin')==='1')
  // const isAdmin =isAdmin1.value
  // console.log('isAdmin00',isAdmin);
  // console.log('isAdmin11',isAdmin1);

  function openNewTab(){
    window.open(router.resolve({ name: 'manage' }).href, '_blank')
  }

</script>
<style>
.common-layout{
  align-items: center;
  justify-content: center;
  height: 100%;
  right: 20px;
}

</style>
