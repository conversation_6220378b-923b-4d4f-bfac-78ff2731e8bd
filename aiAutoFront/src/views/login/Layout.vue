<!-- src/views/Layout.vue -->
<template>
  <div class="common-layout">
    <el-header
      style="background-image: linear-gradient(315deg, #6772ff 0, #73f7ec 100%)"
    >

      <div style="float: left; color: #fff; ">
        <img src="../../assets/svg/ai_icon1.svg" style="width:27% ; margin-top: 1%;"/>
        <h5 style="color: #fff; margin-top: -20%;margin-left: 30%;">AutoMatrix</h5>
      </div>


      <el-dropdown trigger="click" style="float: right">
        <div class="userinfo" style="color: #fff">
          <h3>{{ loginlabel }}</h3>
        </div>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item @click="testloginout">退出登录</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </el-header>
    <el-container style="height: 100vh">
      <!-- 左侧菜单 -->
      <el-aside width="200px">
        <el-menu
          default-active="$route.path"
          :unique-opened="true"
          router
          class="el-menu-vertical-demo"

        >
          <el-menu-item index="/home">

            <el-icon><HomeFilled /></el-icon>
            <span>项目管理</span>
          </el-menu-item>
          <el-menu-item index="/requires">
            <el-icon><Connection /></el-icon>
            <span>需求管理</span>
          </el-menu-item>
          <el-menu-item index="/material">
            <el-icon><MessageBox /></el-icon>
            <span>物料管理</span>
          </el-menu-item>
          <el-menu-item index="/suite">
            <el-icon><Grid /></el-icon>
            <span>用例集</span>
          </el-menu-item>

          <el-sub-menu index="/task">
            <template #title>
              <el-icon><List /></el-icon>
              <span>任务列表</span>
            </template>
            <el-menu-item index="/execute">执行列表</el-menu-item>
            <el-menu-item index="/task">评估任务</el-menu-item>
          </el-sub-menu>

          <el-menu-item index="/report">
            <el-icon><PieChart /></el-icon>
            <span>数据总览</span>
          </el-menu-item>

          <el-sub-menu index="/smart-tools">
            <template #title>
              <el-icon><Tools /></el-icon>
              <span>智能工具</span>
            </template>
            <el-menu-item index="/image-to-testcase">图生用例</el-menu-item>
          </el-sub-menu>

          <el-menu-item v-if="isAdmin" index="/home" @click="openNewTab">
            <el-icon><Avatar /></el-icon>
            <span>后台管理</span>
          </el-menu-item>
          <el-menu-item index="/model">
            <el-icon><ZoomIn /></el-icon>
            <span>模型管理</span>
          </el-menu-item>
          <el-menu-item index="/prompt">
            <el-icon><ChatDotSquare /></el-icon>
            <span>对话管理</span>
          </el-menu-item>
          <el-menu-item index="/template">
            <el-icon><MagicStick /></el-icon>
            <span>提示词管理</span>
          </el-menu-item>
        </el-menu>
      </el-aside>

      <!-- 右侧内容 -->
      <el-main>
        <router-view />
      </el-main>
    </el-container>
  </div>
</template>

<script setup>
import {  watch } from "vue";
import { useRouter, useRoute } from "vue-router";

const router = useRouter();
const route = useRoute();
// watch(
//   () => route.path,
//   (newPath, oldPath) => {
// 	// 路由变化，执行相应操作
//     location.reload()

//   }
// )
// const activeIndex = ref('/home')
// 登录用户信息

const loginlabel = localStorage.getItem("loginlabel") || "用户";
const isAdmin = localStorage.getItem("isAdmin") === "1";
// const isAdmin1 =ref(localStorage.getItem('isAdmin')==='1')
// const isAdmin =isAdmin1.value
// console.log('isAdmin00',isAdmin);
// console.log('isAdmin11',isAdmin1);

function openNewTab() {
  window.open(router.resolve({ name: "manage" }).href, "_blank");
}

// 退出登录
function testloginout() {
  localStorage.removeItem("token");
  localStorage.removeItem("loginlabel");
  localStorage.removeItem("isAdmin");
  router.push("/login");
}
</script>

<style scoped>
.common-layout {
  height: 100vh;
  display: flex;
  flex-direction: column;
}
.el-menu-vertical-demo:not(.el-menu--collapse) {
  width: 100%;
  min-height: 400px;
}
</style>
