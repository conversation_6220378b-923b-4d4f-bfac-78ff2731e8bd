<template>
    <div class="login_bg" >
        <div class="login_adv" style="background-image: url('/static/aibg.jpg')">
            <div class="login_adv__title">

            </div>
        </div>
        <div class="login_main">
            <div class="login_animate">
                <img src="@/assets/login/gd.svg" />
            </div>
            <div class="login_form" id="login-container"></div>
        </div>
    </div>
</template>


<script setup>
import dayjs from 'dayjs';
import { onMounted, ref } from "vue";
import { ticketLogin } from '@/api/loginApi';
 import { ElMessage } from 'element-plus'




 let username=ref('');
 let password= ref('');
 const ticket = ref(null);

// 赛力斯SSO第三方登录登录sdk
async function loadSSO() {
    if (!window.createLoginComp) {
      const script = document.createElement("script");
      const envUrl = 'https://exsso-test.seres.cn/seres-sso-sdk-1.0.0.js' // 此处以开发环境的sdk文件为例，不同环境需要根据环境变量换成不同文件
      script.src = envUrl + '?t=' + Date.now(); // 在 URL 后添加时间戳
      document.head.appendChild(script);
      await new Promise((resolve) => {
        script.onload = resolve;
      });
    }
    // sdk加载成功后调用初始化方法
    window.createLoginComp({
      info: {
        // 配置参数
        boxId: "login-container", // 必填，登录框外层div标签的id
        appId: "739276", // 必填，该应用的appId
        redirect: 'http://localhost:4173/home', // 必填，该应用的授权回调地址（登录成功后跳转的地址，一般为home页，和接口无关）
        type: 2, // 必填 登录类型，2代表web登录页
        lang: '', // 非必填 语言标识 目前只支持zh en ru ar，默认zh
      },
      loginSuccessCallback: (e) => {
       // 登录成功后的回调函数，返回的e是一个数据对象。
       // e.message为用于获取用户信息的ticket，用于获取用户信息。
        console.log('ticket：', e.message)



        ticketLogin({

                            ticket: e.message
                        })
                            .then((res) => {

                                //state.loading = true;
                                if (res.success) {
                                    localStorage.setItem('token', JSON.stringify(res.resp.token))
                                    localStorage.setItem('isAdmin', JSON.stringify(res.resp.admin))
                                    // localStorage.setItem('id', JSON.stringify(res.resp.id))
                                    // localStorage.setItem('username', JSON.stringify(res.resp.username))


                                let loginname=Object.entries(res.resp).map(([key, value]) => ({
                                        label: key,
                                        value: value
                                 })) .filter(item => item.label !== 'token');

                                 console.log('loginname', loginname);

                                 localStorage.setItem('loginlabel', loginname[2].value)
                                 localStorage.setItem('loginvalue', loginname[1].value)

                                 window.location.replace(window.location.origin + `/home`)




                                    // let Dt = res.resp[0];
                                    // Dt.env = 'dev';
                                    // Dt.ticket = e.message;
                                    // store.dispatch('app/setToken', Dt);
                                    // store.dispatch('account/getUserinfo').then((userinfo) => {
                                    //     store.dispatch('menu/generateMenus', userinfo).then(() => {
                                    //         const generateUrl = () => {
                                    //             let params = [],
                                    //                 obj = queryURLParams();
                                    //             for (let key in obj) {
                                    //                 if (!['HASH', 'redirect'].includes(key)) {
                                    //                     params.push(`${decodeURIComponent(key)}=${decodeURIComponent(obj[key])}`);
                                    //                 }
                                    //             }
                                    //             return params.join('&');
                                    //         };

                                    //         if (queryURLParams(`redirect`)) {
                                    //             window.location.replace(window.location.origin + `/#${queryURLParams('redirect')}${generateUrl() ? '?' + generateUrl() : ''}`);
                                    //         } else {
                                    //             window.location.replace(window.location.origin + `/#/home`);
                                    //         }
                                    //     });
                                    // });
                                }
                            })
                            .catch((err) => {
                                return true;
                            })
                            .finally(() => {});

      },
      messageCallback: (e) => {
        // 信息提示回调函数，将登录过程中的成功或者错误信息（e.message）提示到全局。需要使用应用自身的message提示组件，非必填
        if (e.type === "success") {
          message.success(e.message);
        }
        if (e.type === "error") {
            ElMessage.error('请确认账号、密码、验证码是否正确！')
            message.error(e.message);

        }
        if (e.type === "businessAgreement") {
          // 业务系统协议点击回调函数，返回的e.message为用户点击协议标识，用于业务系统方自定义打开协议
        }
      },
    }).then(({ destroy }) => {
        // 存储 loginCompDestroy 函数，以便在组件卸载时调用
        window.destroyLoginComp = destroy;
      })
      .catch((error) => {
        // 处理错误
        console.error("Login Component Error:", error);
      });
  }


onMounted(() => {
    loadSSO()

    const searchParams = new URLSearchParams(window.location.search);
    const ticket = searchParams.get('ticket'); // 获取ticket参数的值
})



</script>


<style lang="scss" scoped>
.login_bg {
    width: 100%;
    height: 100%;
    background: #fff;
    display: flex;
}

.login_adv {
    width: 33.33333%;
    background-color: #555;
    background-size: cover;
    background-position: center center;
    background-repeat: no-repeat;
    position: relative;
}

.login_adv__title {
    color: #fff;
    padding: 40px;
    position: absolute;
    top: 0px;
    left: 0px;
    right: 0px;
    z-index: 2;
}

.login_adv__title h2 {
    font-size: 40px;
}

.login_adv__title h4 {
    font-size: 18px;
    margin-top: 10px;
    font-weight: normal;
}

.login_adv__title p {
    font-size: 14px;
    margin-top: 10px;
    line-height: 1.8;
    color: rgba(255, 255, 255, 0.6);
}

.login_adv__title div {
    margin-top: 10px;
    display: flex;
    align-items: center;
}

.login_adv__title div span {
    margin-right: 15px;
}

.login_adv__title div i {
    font-size: 40px;
}

.login_adv__title div i.add {
    font-size: 20px;
    color: rgba(255, 255, 255, 0.6);
}

.login_adv__bottom {
    position: absolute;
    left: 0px;
    right: 0px;
    bottom: 0px;
    color: #fff;
    padding: 40px;
    background-image: linear-gradient(transparent, #000);
    z-index: 3;
    font-size: 12px;
}

.login_adv__mask {
    position: absolute;
    top: 0px;
    left: 0px;
    right: 0px;
    bottom: 0px;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1;
}

.login_main {
    flex: 1;
    overflow: auto;
    display: flex;
    align-items: center;
    justify-content: flex-start;
}

.login_animate {
    width: 30.3333%;
    height: 400px;

    img {
        width: 100%;
        height: 100%;
    }
}

.login_form {
    width: 600px;
    height: 600px;

    // padding: 20px 20px;
    // box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    // // border: 1px solid #000;
    // border-radius: 10px;
}

.login_form {
    .title {
        font-size: 24px;
        font-weight: 600;
        padding: 15px 0;
    }

    .text,
    .btn {
        width: 100%;
        // margin-bottom: 16px;
    }

    .reflesh {
        display: flex;
        align-items: flex-start;
        justify-content: center;
        padding-top: 10px;
    }
}

@media (max-width: 1200px) {
    .login_form {
        width: 340px;
    }
}

@media (max-width: 1000px) {
    .login_main {
        display: block;
    }

    .login_main .login_config {
        position: static;
        padding: 20px 20px 0 20px;
        text-align: right;
    }

    // .login_main {
    //   flex: 1;
    //   overflow: auto;
    //   display: flex;
    //   align-items: center;
    // }

    .login_form {
        width: 100%;
        box-sizing: border-box;
        padding: 20px 40px;
        transform: translateY(20%);
    }

    .login_adv,
    .login_animate {
        display: none;
    }
}
</style>

<style>
input:-webkit-autofill,
input:-webkit-autofill:hover,
textarea:-webkit-autofill,
textarea:-webkit-autofill:hover,
textarea:-webkit-autofill:focus,
select:-webkit-autofill,
select:-webkit-autofill:hover,
select:-webkit-autofill:focus {
    /* border: 1px solid #000; */
    /* -webkit-text-fill-color: #000; */
    -webkit-box-shadow: 0 0 0px 1000px #fff inset;
    transition: background-color 5000s ease-in-out 0s;
}
</style>