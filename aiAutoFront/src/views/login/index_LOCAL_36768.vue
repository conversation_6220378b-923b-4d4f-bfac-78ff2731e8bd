<template>
  <div class="common-layout">
    <el-container>
      <el-header
        style="
          background-image: linear-gradient(315deg, #6772ff 0, #73f7ec 100%);
        "
      >
        <div style="float: left; color: #fff; margin-top: -5px">
          <h2>logo</h2>
        </div>
        <el-dropdown trigger="click" style="float: right">
          <div class="userinfo" style="color: #fff">
            <h3>NAME</h3>
          </div>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item @click="testloginout"
                >退出登录</el-dropdown-item
              >
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </el-header>
      <el-container>
        <el-aside width="100px">

            <ul class="sc-jSSkKI dWvysQ">
              <li
                :class="{ active: activeTab === 'resources' }"
                class="sc-jIyAiq cXDkWL"
                @click="activeTab = 'resources'"
              >
                <i class="sc-eWPXlR ixlxbr nav-icon">
                  <img src="@/assets/svg/projectdata.svg" style="width: 70px" />
                </i>

                <span style="color: #6772ff">项目管理</span>
              </li>

              <li
                :class="{ active: activeTab === 'requires' }"
                class="sc-jIyAiq cXDkWL"
                @click="activeTab = 'requires'"
              >
                <i class="sc-eWPXlR ixlxbr nav-icon">
                  <img src="@/assets/svg/requirement.svg" style="width: 80px" />
                </i>

                <span style="color: #6772ff">需求管理</span>
              </li>

              <li
                :class="{ active: activeTab === 'board' }"
                class="sc-jIyAiq cXDkWL active local-link"
                @click="activeTab = 'board'"
              >
                <div class="sc-lnsxGb YmUSM"></div>
                <i class="sc-eWPXlR ixlxbr nav-icon">
                  <img src="@/assets/svg/material.svg" style="width: 80px" />
                </i>

            <span style="color: #6772FF;">用例评估</span>
          </li>

          <li
                :class="{ active: activeTab === 'model' }"
                class="sc-jIyAiq cXDkWL"
                @click="activeTab = 'model'"
              >
                <i class="sc-eWPXlR ixlxbr nav-icon">
                  <img src="@/assets/svg/modelManage.svg" style="width: 40px" />
                </i>

                <span style="color: #6772ff">模型管理</span>
              </li>

          <li
                :class="{ active: activeTab === 'prompt' }"
                class="sc-jIyAiq cXDkWL"
                @click="activeTab = 'prompt'"
              >
                <i class="sc-eWPXlR ixlxbr nav-icon">
                  <img src="@/assets/svg/dialog.svg" style="width: 40px" />
                </i>

                <span style="color: #6772ff">对话管理</span>
              </li>

          <li
                :class="{ active: activeTab === 'template' }"
                class="sc-jIyAiq cXDkWL"
                @click="activeTab = 'template'"
              >
                <i class="sc-eWPXlR ixlxbr nav-icon">
                  <img src="@/assets/svg/promptManage.svg" style="width: 40px" />
                </i>

                <span style="color: #6772ff">提示词管理</span>
              </li>
        </ul>

        </el-aside>
        <el-main style="padding: 0px;height: 100%;">
            <component :is="currentTabComponent"/>
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>
<script setup>
import suiteList from "../suite/suiteList.vue";
import requirement from "../requirement/requirementList.vue";
import DataManagement from "../data/DataList.vue";
import projectManagement from "../project/ProjectList.vue";
import TaskManagement from "../evaluation/evaluationList.vue";
import ModelManagement from "../model/modelList.vue"
import PromptManagement from "../prompt/prompt.vue"
import DialogView from '../prompt/dialog.vue';
import PromptTemplate from "../template/TemplateList.vue"
import PromptComparison from "../template/PromptComparison.vue";
import { useRouter } from 'vue-router';
import { ref, computed } from 'vue';

const router = useRouter();
let activeTab = ref("resources"); // 默认显示小组管理
const currentTabComponent = computed(() => {
  switch (activeTab.value) {
    case "suite":
      return suiteList;
    case "task":
      return TaskManagement;
    case "board":
      return DataManagement;
    case "resources":
      return projectManagement;
    case "requires":
      return requirement;
      case "report":
      return reportManagement;
    case "model":
      return ModelManagement;
    case "prompt":
      return PromptManagement;
    case "dialog":
      return DialogView;
    case "template":
      return PromptTemplate;
    default:
      return GroupManagement;
  }
});



function testloginout() {
  localStorage.removeItem("token");
  router.push("/ssoLogin");
}
</script>
<style>
.common-layout{
  align-items: center;
  justify-content: center;
  height: 100%;
  right: 20px;
}
.dWvysQ {
  flex: 1 1 0%;
  display: flex;
  flex-direction: column;
  padding: 0px;
  margin: 0px;
}
li,
ul {
  margin-block-end: 0;
  margin-block-start: 0;
  margin-inline-end: 0;
  margin-inline-start: 0;
  padding-inline-start: 0;
}
.cXDkWL {
  font-weight: normal;
  margin-top: 30px;
  cursor: pointer;
  color: rgb(224, 223, 255);
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: center;
  position: relative;
}
.ixlxbr {
  position: relative;
  display: flex;
  width: 48px;
  height: 48px;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  color: rgb(198, 196, 255);
}
li,
ul {
  margin-block-end: 0;
  margin-block-start: 0;
  margin-inline-end: 0;
  margin-inline-start: 0;
  padding-inline-start: 0;
}
.cXDkWL {
  font-weight: normal;
  margin-top: 30px;
  cursor: pointer;
  color: rgb(224, 223, 255);
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: center;
  position: relative;
}
.ixlxbr {
  position: relative;
  display: flex;
  width: 48px;
  height: 48px;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  color: rgb(198, 196, 255);
}

.icon {
  width: 2em;
  height: 2em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
  fill: currentcolor;
  color: #f7f8fc;
}

.hYmrzv {
  position: absolute;
  top: 5.5%;
  left: 0px;
  width: 4%;
  height: 100%;
  background: #f7f8fc;
  /* z-index: -1; */
  /* background: #615ced; */
}
.exEpVG {
  flex: 1 1 0%;
  height: 100%;
  /* padding: 8px 8px 8px 0px; */
  overflow: hidden;
  position: absolute;
  width: 96%;
  background: #eeedff;
  /* opacity: 0.5; */
  margin-left: -7.1%;
  margin-top: -1%;
}
</style>
