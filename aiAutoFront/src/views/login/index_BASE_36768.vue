<template>
  <div class="common-layout">
    <el-container>
      <el-header
        style="
          background-image: linear-gradient(315deg, #6772ff 0, #73f7ec 100%);
        "
      >
        <div style="float: left; color: #fff; margin-top: -5px">
          <h2>logo</h2>
        </div>
        <el-dropdown trigger="click" style="float: right">
          <div class="userinfo" style="color: #fff">
            <h3>NAME</h3>
          </div>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item @click="testloginout"
                >退出登录</el-dropdown-item
              >
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </el-header>
      <el-container>
        <el-aside width="100px">

            <ul class="sc-jSSkKI dWvysQ">
              <li
                :class="{ active: activeTab === 'resources' }"
                class="sc-jIyAiq cXDkWL"
                @click="activeTab = 'resources'"
              >
                <i class="sc-eWPXlR ixlxbr nav-icon">
                  <img src="@/assets/svg/projectdata.svg" style="width: 70px" />
                </i>

                <span style="color: #6772ff">项目管理</span>
              </li>

              <li
                :class="{ active: activeTab === 'requires' }"
                class="sc-jIyAiq cXDkWL"
                @click="activeTab = 'requires'"
              >
                <i class="sc-eWPXlR ixlxbr nav-icon">
                  <img src="@/assets/svg/requirement.svg" style="width: 80px" />
                </i>

                <span style="color: #6772ff">需求管理</span>
              </li>

              <li
                :class="{ active: activeTab === 'board' }"
                class="sc-jIyAiq cXDkWL active local-link"
                @click="activeTab = 'board'"
              >
                <div class="sc-lnsxGb YmUSM"></div>
                <i class="sc-eWPXlR ixlxbr nav-icon">
                  <img src="@/assets/svg/material.svg" style="width: 80px" />
                </i>

            <span style="color: #6772FF;">用例评估</span>
          </li>

<li :class="{ active: activeTab === 'model'}" class="sc-jIyAiq cXDkWL local-link" @click="activeTab = 'model'">
                <i class="sc-eWPXlR ixlxbr nav-icon"
              ><svg
                viewBox="0 0 1024 1024"
                version="1.1"
                xmlns="http://www.w3.org/2000/svg"
                width="1.3em"
                height="1.3em"
                color="inherit"
                fill="currentColor"
              >
                <path
                  d="M970.9376 193.264A17.6384 17.6384 0 0 1 982.4 209.8048a17.6384 17.6384 0 0 1-11.4624 16.544l-54.7328 20.3456a17.6032 17.6032 0 0 0-10.3712 10.4192l-20.2528 54.976a17.5616 17.5616 0 0 1-16.464 11.52 17.5552 17.5552 0 0 1-16.4672-11.52l-20.256-54.976a17.6032 17.6032 0 0 0-10.3712-10.4192l-54.7296-20.3488a17.6384 17.6384 0 0 1-11.4656-16.5408 17.6384 17.6384 0 0 1 11.4656-16.544l54.7296-20.3456a17.6064 17.6064 0 0 0 10.3712-10.4192l20.256-54.9792A17.5552 17.5552 0 0 1 869.1168 96a17.5616 17.5616 0 0 1 16.464 11.5168l20.2528 54.976a17.6032 17.6032 0 0 0 10.3712 10.4224l54.7328 20.3456zM812.9792 841.504c26.5376 38.2816-1.9936 89.696-49.7792 89.696H242.3424c-47.7856 0-76.32-51.312-49.7792-89.696l182.2336-263.3792c-63.7568-42.9024-102.016-114.9152-102.016-192.0032 0-127.6352 102.9952-231.104 230.048-231.104s230.0512 103.4688 230.0512 231.104c0 77.12-38.2976 149.1616-102.0992 192.0544l182.1984 263.328z m-231.3376-373.3984c0-19.2416-15.664-35.0752-34.9184-35.0752h-87.8912c-19.152 0-34.912 15.8336-34.912 35.0752 0 19.344 15.76 35.0752 34.912 35.0752h87.8912c19.2544 0 34.9184-15.8336 34.9184-35.0752z"
                ></path></svg></i
            >
            <span style="color: #6772FF;">模型管理</span>
          </li>

        </ul>

        </el-aside>
        <el-main style="padding: 0px;height: 100%;">
            <component :is="currentTabComponent"/>
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>
<script setup>
import suiteList from "../suite/suiteList.vue";
import requirement from "../requirement/requirementList.vue";
import DataManagement from "../data/DataList.vue";
import projectManagement from "../project/ProjectList.vue";
import TaskManagement from "../evaluation/evaluationList.vue";
import ModelManagement from "../model/modelList.vue"
import { useRouter } from 'vue-router';
import { ref, computed } from 'vue';

const router = useRouter();
let activeTab = ref("resources"); // 默认显示小组管理
const currentTabComponent = computed(() => {
  switch (activeTab.value) {
    case "suite":
      return suiteList;
    case "task":
      return TaskManagement;
    case "board":
      return DataManagement;
    case "resources":
      return projectManagement;
    case "requires":
      return requirement;
      case "report":
      return reportManagement;
    case "model":
      return ModelManagement;
    default:
      return GroupManagement;
  }
});

function testloginout() {
  localStorage.removeItem("token");
  router.push("/ssoLogin");
}
</script>
<style>
.common-layout{
  align-items: center;
  justify-content: center;
  height: 100%;
  right: 20px;
}
.dWvysQ {
  flex: 1 1 0%;
  display: flex;
  flex-direction: column;
  padding: 0px;
  margin: 0px;
}
li,
ul {
  margin-block-end: 0;
  margin-block-start: 0;
  margin-inline-end: 0;
  margin-inline-start: 0;
  padding-inline-start: 0;
}
.cXDkWL {
  font-weight: normal;
  margin-top: 30px;
  cursor: pointer;
  color: rgb(224, 223, 255);
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: center;
  position: relative;
}
.ixlxbr {
  position: relative;
  display: flex;
  width: 48px;
  height: 48px;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  color: rgb(198, 196, 255);
}
li,
ul {
  margin-block-end: 0;
  margin-block-start: 0;
  margin-inline-end: 0;
  margin-inline-start: 0;
  padding-inline-start: 0;
}
.cXDkWL {
  font-weight: normal;
  margin-top: 30px;
  cursor: pointer;
  color: rgb(224, 223, 255);
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: center;
  position: relative;
}
.ixlxbr {
  position: relative;
  display: flex;
  width: 48px;
  height: 48px;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  color: rgb(198, 196, 255);
}

.icon {
  width: 2em;
  height: 2em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
  fill: currentcolor;
  color: #f7f8fc;
}

.hYmrzv {
  position: absolute;
  top: 5.5%;
  left: 0px;
  width: 4%;
  height: 100%;
  background: #f7f8fc;
  /* z-index: -1; */
  /* background: #615ced; */
}
.exEpVG {
  flex: 1 1 0%;
  height: 100%;
  /* padding: 8px 8px 8px 0px; */
  overflow: hidden;
  position: absolute;
  width: 96%;
  background: #eeedff;
  /* opacity: 0.5; */
  margin-left: -7.1%;
  margin-top: -1%;
}
</style>
