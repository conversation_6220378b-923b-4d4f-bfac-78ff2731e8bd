<template>
  <div class="dialog-container">
    <!-- 输入区域 -->
    <div class="dialog-input">
      <!-- 聊天记录展示区域 -->
      <div class="chat-history" ref="chatHistory">
        <div
          v-for="(msg, index) in chatMessages"
          :key="index"
          class="chat-message"
          :class="{ 'user-message': msg.role === 'user' }" 
        >
          <div>
            {{ msg.role === "user" ? "" : "助手: " }}
            <!-- 原有内容渲染器 -->
            <generic-content-renderer
              v-if="shouldRenderContent(msg)"
              :content="getMsgContent(msg)"
              :is-chat-message="true"
            />
            <!-- 在流式处理期间只对最后一条助手消息显示提示 -->
            <span
              v-else-if="
                msg.role === 'assistant' &&
                !msg.content &&
                isStreaming &&
                isCurrentAssistantMessageIndex(index)
              "
            >
              正在努力查询中...
              <span class="loading-spinner"></span>
            </span>
          </div>
        </div>
      </div>

      <!-- 输入区域容器 -->
      <div class="input-container">
        <div class="preview-input-wrapper">
          <!-- 输入区域 -->
          <div class="input-area">
            <!-- 清除按钮 -->
            <i class="delete-icon" @click="clearContent" aria-label="清除对话">
              <img src="@/assets/svg/clear.svg" style="width: 20px" />
              <span class="tooltip">清除对话</span>
            </i>

            <!-- 输入框容器 -->
            <div class="input-with-icons">
              <!-- 添加图片预览区域 -->
              <div v-if="previewImage" class="image-preview">
                <img :src="previewImage" class="preview-img" />
                <button @click="removeImage" class="remove-image-btn">×</button>
              </div>

              <textarea
                rows="4"
                placeholder="请输入想要查询的内容，按回车键发送～"
                class="chat-input"
                v-model="inputMessage"
                @keyup.enter="sendMessage"
                :disabled="isStreaming"
              ></textarea>
              <!-- 选择模型下拉框定位在搜索框底部 -->
              <div class="model-select-container">
                <el-select
                  v-model="selectedModel"
                  placeholder="请选择模型"
                  size="small"
                  class="model-select"
                >
                  <template #prefix>
                    <i class="el-icon-s-operation"></i>
                  </template>
                  <el-option
                    v-for="model in modelOptions"
                    :key="model.value"
                    :label="model.label"
                    :value="model.value"
                  />
                </el-select>
              </div>

              <!-- 修改上传按钮逻辑 -->
              <button
                class="icon-button upload-btn"
                title="上传图片"
                aria-label="上传图片"
                @click="triggerFileInput"
              >
                <img src="@/assets/svg/upload.svg" style="width: 20px" />
                <span class="tooltip">上传图片</span>
              </button>
              <input
                type="file"
                ref="fileInput"
                @change="handleFileUpload"
                accept="image/*"
                style="display: none"
              />
              <button
                class="icon-button voice-btn"
                title="语音输入"
                aria-label="语音输入"
              >
                <img src="@/assets/svg/voice.svg" style="width: 20px" />
                <span class="tooltip">语音输入</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch, nextTick } from "vue";
import { useRoute } from "vue-router";
import { useChat } from "@/hooks/useChat";
import GenericContentRenderer from "@/components/GenericContentRenderer.vue";
import { modelList } from "@/api/modelApi";

const route = useRoute();
const modelId = ref(""); // 用于聊天的模型 ID
const selectedModelLabel = ref("");

// 选择模型
const selectedModel = ref("");

onMounted(async () => {
  await loadModels(); // 确保模型数据已加载

  if (route.query.prompt) {
    inputMessage.value = route.query.prompt;
    modelId.value = route.query.modelId;

    const foundModel = modelListData.value.find(m => m.id === Number(route.query.modelId));
    if (foundModel) {
      selectedModel.value = foundModel.id;
      selectedModelLabel.value = foundModel.model_name;
    } else {
      console.warn("未找到对应的模型", route.query.modelId);
    }

    sendMessage();
  }
});

// ✨ 新增 watch 来监听 selectedModel 的变化并同步到 modelId
watch(selectedModel, (newId) => {
  modelId.value = newId; // 同步 modelId，触发 useChat 使用新模型
});

async function loadModels() {
  try {
    const res = await modelList({ page: 1, page_size: 10 });
    modelListData.value = res.resp || [];

    // ✅ 在加载完成后设置默认选中模型标签
    if (route.query.modelId) {
      const foundModel = modelListData.value.find(m => m.id === route.query.modelId);
      selectedModel.value = route.query.modelId; // 设置 v-model 的值
      selectedModelLabel.value = foundModel ? foundModel.model_name : "";
    }
  } catch (error) {
    console.error("加载模型数据失败:", error);
    modelListData.value = [];
  }
}
// 监听 modelId 变化，更新 selectedModelLabel
watch(
  () => selectedModel.value,
  (newId) => {
    const foundModel = modelListData.value.find(m => m.id === newId);
    selectedModelLabel.value = foundModel ? foundModel.model_name : "";
  }
);

const { chatMessages, inputMessage, isStreaming, sendMessage, clearContent } =
  useChat(modelId);

// 监听 selectedModel 变化，更新 selectedModelLabel
watch(selectedModel, (newId) => {
  const foundModel = modelListData.value.find(model => model.id === newId);
  selectedModelLabel.value = foundModel ? foundModel.model_name : "";
});

const chatHistory = ref(null);

// 添加调试信息来验证 isStreaming 的状态变化
watch(isStreaming, (newVal) => {
  console.log("isStreaming changed to:", newVal);
});

const fileInput = ref(null);
const previewImage = ref(null);
const uploadedFile = ref(null);

const triggerFileInput = () => {
  fileInput.value.click();
};

const handleFileUpload = (event) => {
  const file = event.target.files[0];
  if (file) {
    uploadedFile.value = file;
    previewImage.value = URL.createObjectURL(file);
  }
};

const removeImage = () => {
  previewImage.value = null;
  uploadedFile.value = null;
};

// 修改模型选项为从 modelList 获取
const modelListData = ref([]);
const modelOptions = computed(() => {
  return modelListData.value.map(model => ({
    label: model.model_name,
    value: model.id // 确保使用 model.id 作为 value
  }));
});
// 检查是否是最后一个助手消息
const isCurrentAssistantMessageIndex = (index) => {
  const lastAssistantIndex = chatMessages.value
    .map((m, i) => (m.role === "assistant" ? i : -1))
    .filter((i) => i !== -1)
    .pop();
  return index === lastAssistantIndex;
};

// 在 useChat 返回的 chatMessages 更新后同步到 DOM
watch(
  () => chatMessages.value,
  () => {
    scrollToBottom(); // 自动滚动到底部
  }
);
// 🔽 自动滚动到底部
function scrollToBottom() {
  nextTick(() => {
    if (chatHistory.value) {
      chatHistory.value.scrollTop = chatHistory.value.scrollHeight;
    }
  });
}

// 在 getMsgContent 中返回原始 msg.content（已流式更新）
const getMsgContent = (msg) => {
  return msg.content;
};

// 新增：判断是否应渲染内容（暴露给模板）
const shouldRenderContent = (msg) => {
  return msg.role === "assistant" ? !!msg.content : true;
};

// 将函数暴露给模板使用
defineExpose({ shouldRenderContent });

// 修改监听器更新 selectedModelLabel
watch(() => route.query.modelName, (newModelName) => {
  if (newModelName) {
    selectedModelLabel.value = newModelName;
  }
});
</script>

<style scoped>
.dialog-container {
  height: 100vh; /* 占满整个屏幕 */
  display: flex;
  justify-content: center; /* 水平居中 */
  align-items: center; /* 垂直居中 */
  background-color: #fff; /* 可选背景色 */
}

.dialog-input {
  width: 50%; /* 宽度为屏幕的 50% */
  height: 90vh; /* 高度为屏幕的 90% */
  display: flex;
  flex-direction: column; /* 子元素垂直排列 */
  border-radius: 10px; /* 圆角 */
  overflow: hidden; /* 防止内容溢出 */
  background-color: #fff; /* 背景色 */
}

.chat-history {
  flex: 1;
  width: 95%;
  padding: 15px;
  margin-left: 50px;
  overflow-y: auto;
  border-bottom: 1px solid #eee;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  background-color: #fff;
  border: 1px solid #eee;
  border-radius: 6px;
  min-height: 0; /* 关键：允许 flex 收缩 */
}

.chat-message {
  margin-bottom: 16px; /* 增加消息间距 */
  line-height: 1.6;
  padding: 12px 16px;
  border-radius: 8px;
  background-color: #f5f5f5; /* 添加背景色 */
}

/* 新增：用户消息样式 */
.user-message {
  background-color: #eee;
  align-self: flex-end; /* 靠右对齐 */
  text-align: right; /* 文字也靠右 */
}

.chat-message h3 {
  color: #2c3e50;
  margin-top: 0;
  margin-bottom: 12px;
  font-size: 18px;
  font-weight: 600;
}

.chat-message ul {
  padding-left: 20px;
  margin: 8px 0;
}

.chat-message li {
  margin: 4px 0;
  list-style-type: disc;
}

.chat-message p {
  margin: 8px 0;
}

.chat-message .user {
  color: #333;
  background-color: #f8fafc;
}

.chat-message .assistant {
  color: #2d3748;
  background-color: #f8fafc; /* 助手消息浅灰色背景 */
}

.chat-message .tip {
  color: #718096;
  font-style: italic;
}

.preview-input-wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.loading-indicator {
  opacity: 0.7;
}

.loading-spinner {
  display: inline-block;
  width: 16px; /* 调整为合适的大小 */
  height: 16px; /* 调整为合适的大小 */
  margin-left: 5px;
  border: 4px solid #bab8b8;
  border-top-color: transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.input-container {
  margin-top: 10px;
}
.input-area {
  display: flex;
  align-items: center;
  margin-top: auto; /* 推到最底部 */
}

.input-with-icons {
  position: relative;
  flex: 1;
}

.chat-input {
  width: 100%;
  height: 120px; /* 设置固定高度 */
  padding: 12px 44px 12px 12px; /* 调整内边距 */
  border: 1px solid #ccc;
  border-radius: 30px;
  outline: none;
  font-size: 18px;
  background-color: #fff;
  resize: none; /* 禁止手动调整大小 */
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); /* 添加阴影 */
  transition: all 0.3s ease-in-out; /* 添加过渡动画 */
}
.chat-input:focus {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(103, 114, 255, 0.2);
}

.icon-button {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background-color: transparent;
  border: none;
  cursor: pointer;
  transition: transform 0.2s;
  padding: 6px;
}

.icon-button:hover .tooltip {
  opacity: 1;
  visibility: visible;
}

.tooltip {
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: #333;
  color: #fff;
  padding: 4px 8px;
  font-size: 12px;
  border-radius: 4px;
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.2s ease, visibility 0.2s ease;
  z-index: 10;
  font-family: sans-serif;
}
.delete-icon {
  position: relative;
  cursor: pointer;
  padding: 6px;
  display: inline-block;
  background-color: transparent; /* 去掉背景色 */
  border: none; /* 去掉边框 */
  margin-right: 10px;
  margin-left: 10px;
}
.delete-icon:hover .tooltip {
  opacity: 1;
  visibility: visible;
}

.upload-btn {
  right: 36px;
  color: #4a90e2;
}

.voice-btn {
  right: 8px;
  color: #67c23a;
}

.image-preview {
  position: relative;
  margin-bottom: 10px;
  max-width: 200px;
}

.preview-img {
  max-width: 50px; /* 设置最大宽度 */
  max-height: 50px; /* 设置最大高度 */
  border-radius: 8px;
}

.remove-image-btn {
  position: absolute;
  top: -8px;
  right: -8px;
  width: 24px;
  height: 24px;
  background: #ff4d4f;
  color: white;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.model-select-container {
  position: absolute;
  bottom: 15px;
  right: 12px;
  left: 12px;
  z-index: 10;
  background: white;
}

/* 模型选择器样式 */
.model-select {
  width: 20%;
  border-radius: 20px;
  margin-left: 8px;
  height: 25px;
  font-size: 14px;
  padding: 0 12px;
  transition: all 0.3s ease;
}

/* 深度选择器：修改 el-select 的外层包裹元素 */
.model-select ::v-deep(.el-select__wrapper) {
  border: none !important; /* 移除边框 */
  border-radius: 20px;
  font-size: 14px;
  height: 30px;
  background-color: transparent !important; /* 透明背景 */
}

/* 深度选择器：修改内部输入框的样式（保留交互效果） */
.model-select ::v-deep(.el-input__inner) {
  border: 1px solid #dcdfe6; /* 手动添加内部输入框的边框 */
  background-color: #f9f9f9;
}

.model-select ::v-deep(.el-input__inner:hover) {
  border-color: #409eff;
  background-color: #ecf5ff;
}

.model-select ::v-deep(.el-input__inner:focus) {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2); /* 聚焦时的阴影 */
}
</style>
