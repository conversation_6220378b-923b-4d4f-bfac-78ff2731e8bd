<template>
  <h1 class="title">下午好，{{ username }}</h1>
  <div class="prompt-container">
    <!-- 输入框 -->
    <div class="search-area flex justify-center relative">
      <div class="search-box relative">
        <el-input
          v-model="searchQuery"
          placeholder="请输入关键词搜索"
          clearable
          @input="handleSearch"
          @keyup.enter="handleEnter"
          class="search-input rounded-full border-0 m-0"
          ref="searchInput"
          type="textarea"
          :rows="3"
        >
        </el-input>

        <!-- 选择模型下拉框定位在搜索框底部 -->
        <div class="model-select-container">
          <el-select
            v-model="selectedModel"
            placeholder="请选择模型"
            size="small"
            class="model-select"
          >
            <template #prefix>
              <i class="el-icon-s-operation"></i>
              <!-- 添加图标 -->
            </template>
            <el-option
              v-for="model in modelOptions"
              :key="model.value"
              :label="model.label"
              :value="model.value"
            />
          </el-select>
        </div>
      </div>
    </div>

    <!-- 提示词卡片区域 -->
    <div class="templates-area">
      <el-row :gutter="20">
        <el-col
          :span="6"
          v-for="(template, index) in promptTemplates"
          :key="index"
        >
          <el-card class="template-card">
            <div class="template-content">
              <h3>{{ template.title }}</h3>
              <p>{{ template.description }}</p>
              <div class="template-link">
                <a :href="template.link">{{ template.linkLabel }}</a>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 其他内容 -->
    <div class="other-content">
      <!-- 其他内容区域 -->
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from "vue";
import { useRouter } from "vue-router";
import { modelList } from "@/api/modelApi";

// 引入路由
const router = useRouter();

// 新增：从 localStorage 获取用户名
const username = ref(localStorage.getItem("loginlabel") || "用户");


// 选择模型
const selectedModel = ref("");
const selectedModelLabel = ref(""); // ✅ 新增：声明 selectedModelLabel

onMounted(async () => {
  await loadModels(); // 新增模型加载
});

// 回车键触发跳转
const handleEnter = () => {
  const input = searchQuery.value.trim();
  if (input) {
    router.push({
      path: "./dialog",
      query: {
        prompt: input,
        modelId: selectedModel.value,
      }
    });
  }
};

// 模版数据
const promptTemplates = ref([
  {
    title: "医生的职责界限在哪",
    description: "旅途中医生有没有救人的义务？",
    link: "#",
    linkLabel: "热议！乘客机上昏迷离世.pdf",
  },
  {
    title: "别再死磕基础教育了！",
    description: "基础教育时间过长竟是人才浪费？听听校长怎么说！",
    link: "#",
    linkLabel: "别再死磕基础教育了！",
  },
  {
    title: "网文应该这样写",
    description: "如何靠写小说养活自己？",
    link: "#",
    linkLabel: "写小说喂饭级攻略！.pdf",
  },
  {
    title: "蒙娜丽莎笑不出来",
    description: "画【蒙娜丽莎写作业】",
    link: "#",
    linkLabel: "蒙娜丽莎笑不出来",
  },
]);

// 搜索查询
const searchQuery = ref("");

// 处理搜索输入
const handleSearch = () => {
  // 可以在这里添加更多搜索逻辑
};


// 新增模型加载函数
async function loadModels() {
  try {
    const res = await modelList({ page: 1, page_size: 10 });  // 修复：使用明确的参数代替未定义的params
    modelListData.value = res.resp || [];
    // 设置第一个模型为默认选择
    if (modelListData.value.length > 0) {
      selectedModel.value = modelListData.value[0].id;
    }
  } catch (error) {
    console.error("加载模型数据失败:", error);
    modelListData.value = [];
  }
}


// 修改模型选项为从 modelList 获取
const modelListData = ref([]);
const modelOptions = computed(() => {
  return modelListData.value.map(model => ({
    label: model.model_name,
    value: model.id // 确保使用 model.id 作为 value
  }));
});

</script>

<style scoped>
/* h1 标题样式 */
.title {
  text-align: center; /* 水平居中 */
  margin-top: 150px;
  margin-bottom: 20px; /* 调整与下方内容的间距 */
  font-size: 24px; /* 字体大小（可选） */
  color: #333; /* 字体颜色（可选） */
}

.prompt-container {
  padding: 20px;
  animation: fadeIn 0.5s ease-in-out;
}

.search-area {
  position: relative;
  width: 100%;
  max-width: 925px;
  margin: 0 auto;
  margin-bottom: 40px;
}

/* 搜索框容器 */
.search-box {
  position: relative;
}

/* 搜索框样式 */
.search-input {
  width: 100%;
  height: 150px;
  font-size: 16px;
  border-radius: 30px; /* 增大圆角 */
  padding: 10px 10px; /* 调整内边距 */
  padding-bottom: 20px; /* 为模型选择器留出空间 */
  border: 2px solid #e4e4e4;
  transition: all 0.3s ease-in-out;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

/* 移除 el-textarea__wrapper 的边框 */
.search-input ::v-deep(.el-textarea__inner) {
  border: none !important;
  box-shadow: none !important;
  outline: none !important;
  resize: none; /* 禁止手动调整大小 */
}

.search-input:focus-within {
  border-color: #409eff;
  box-shadow: 0 4px 16px rgba(64, 158, 255, 0.3);
}
.el-input__inner:hover {
  border-color: #409eff;
  background-color: #f0f8ff;
}

.el-input__inner:focus {
  border-color: #409eff;
  outline: none;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.model-select-container {
  position: absolute;
  bottom: 15px;
  right: 12px;
  left: 12px;
  z-index: 10;
  background: white;
}

/* 模型选择器样式 */
.model-select {
  width: 15%;
  border-radius: 20px;
  margin-left: 8px;
  height: 25px;
  font-size: 14px;
  padding: 0 12px;
  transition: all 0.3s ease;
}

/* 深度选择器：修改 el-select 的外层包裹元素 */
.model-select ::v-deep(.el-select__wrapper) {
  border: none !important; /* 移除边框 */
  border-radius: 20px;
  font-size: 14px;
  height: 30px;
  background-color: transparent !important; /* 透明背景 */
}

/* 深度选择器：修改内部输入框的样式（保留交互效果） */
.model-select ::v-deep(.el-input__inner) {
  border: 1px solid #dcdfe6; /* 手动添加内部输入框的边框 */
  background-color: #f9f9f9;
}

.model-select ::v-deep(.el-input__inner:hover) {
  border-color: #409eff;
  background-color: #ecf5ff;
}

.model-select ::v-deep(.el-input__inner:focus) {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2); /* 聚焦时的阴影 */
}

.templates-area {
  margin-bottom: 20px;
  animation: fadeIn 0.5s ease-in-out;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 20px; /* 卡片间距 */
}

.template-card {
  width: clamp(240px, 30%, 350px); /* 响应式宽度，最小280px，最大350px */
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
  animation: fadeIn 0.5s ease-in-out;
}

.template-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.template-content {
  text-align: center;
  padding: 10px;
  animation: fadeIn 0.5s ease-in-out;
}

.template-content h3 {
  font-size: 16px;
  margin-bottom: 10px;
  color: #333;
}

.template-content p {
  font-size: 14px;
  color: #666;
  margin-bottom: 15px;
}

.template-link {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: #409eff;
  animation: fadeIn 0.5s ease-in-out;
}

.template-link i {
  margin-right: 5px;
}

.other-content {
  /* 其他内容区域样式 */
  animation: fadeIn 0.5s ease-in-out;
}

/* 如果有需要添加或修改 .preview-img 的样式 */
.preview-img {
  max-width: 200px; /* 设置最大宽度 */
  max-height: 150px; /* 设置最大高度 */
  border-radius: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .template-card {
    width: 100%; /* 小屏幕时每行显示一个卡片 */
    margin-bottom: 20px; /* 添加底部间距 */
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>