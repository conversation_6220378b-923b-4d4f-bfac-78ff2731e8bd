<template>
  <el-dialog
    v-model="counter.executeRagTestShow"
    :beforeClose="handleClose"
    title="执行RAG评估"
    style="height: 50%; width: 40%"
  >
    <el-row :gutter="20">
      <el-col :span="10">
        <el-form
          :label-position="labelPosition"
          label-width="100px"
          :model="counter.executeRagFormdata"
          style="max-width: 460px"
          v-if="counter.executeRagOptionspg1"
        >
          <el-form-item label="用例集名称">
            <el-input
              v-model="counter.executeRagSuiteName"
              disabled
              placeholder="自动获取用例集名称"
            />
          </el-form-item>

          <el-form-item label="执行用例">
            <el-select
              v-model="counter.executeRagFormdata.suite_sequence_id"
              filterable
              reserve-keyword
              @focus="counter.getSuiteExecute(counter.executeRagFormdata.suite_id)"
            >
              <el-option
                v-for="item in counter.SequenceData"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              <span>{{"执行ID："}}{{item.num}}_{{item.label}}</span>
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="模型名称">
            <el-select
              v-model="counter.executeRagFormdata.model_id"
              filterable
              reserve-keyword
              @focus="counter.getllmConfig()"
            >
              <el-option
                v-for="item in counter.modelData"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="模型向量">
            <el-input v-model="counter.executeRagFormdata.embedding_model_id" />
          </el-form-item>

          <el-form-item>
            <el-button type="primary" @click="counter.executeRagEvaluate()">
              开始评估002
            </el-button>
            <el-button @click="handleClose">取消</el-button>
          </el-form-item>
        </el-form>

        <!-- 人工评估表单 -->
        <el-form
          :label-position="labelPosition"
          label-width="100px"
          :model="counter.executeManualForm"
          style="max-width: 460px"
          v-if="counter.executeRagRengongval"
        >
          <el-form-item label="用例集">
            <el-input
              v-model="counter.executeRagSuiteName"
              disabled
              placeholder="自动获取用例集名称"
            />
          </el-form-item>
          <el-form-item label="用例执行">
            <el-select
              v-model="counter.executeManualForm.test_case_id"
              filterable
              reserve-keyword
              multiple
              @focus="counter.getSuiteExecute(counter.executeRagFormdata.suite_id)"
            >
              <el-option
                v-for="item in counter.SequenceData"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              <span>{{"执行ID："}}{{item.num}}_{{item.label}}</span>
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="模型回答">
            <el-input v-model="counter.executeManualForm.response" />
          </el-form-item>
          <el-form-item label="得分">
            <el-input v-model="counter.executeManualForm.score" />
          </el-form-item>
          <el-form-item label="备注">
            <el-input v-model="counter.executeManualForm.remark" />
          </el-form-item>
          <el-form-item>
            <el-button @click="counter.executePostManualDate(counter.executeManualForm)">
              提交评估009
            </el-button>
            <el-button @click="cancelExecuteRagHuman()">取消</el-button>
          </el-form-item>
        </el-form>
      </el-col>

      <el-col :span="5" :offset="6">
        <el-switch
          v-model="counter.executeRagValue90"
          size="small"
          active-text="是否开启人工评估"
          @change="counter.executeRagValue90change"
        />
      </el-col>
    </el-row>
  </el-dialog>
</template>

<script setup>
import { suiteStore } from "@/pinia/suitstore";
import { ref } from "vue";

const counter = suiteStore();
const labelPosition = ref("right");

function handleClose() {
  counter.executeRagTestShow = false;
  counter.executeRagOptionspg1 = true;
  counter.executeRagRengongval = false;
  counter.executeRagValue90 = false;
}

function cancelExecuteRagHuman() {
  counter.executeRagRengongval = false;
  counter.executeRagValue90 = false;
  counter.executeRagOptionspg1 = true;
}
</script>

<style scoped>
.el-form-item {
  margin-bottom: 18px;
}
</style>
