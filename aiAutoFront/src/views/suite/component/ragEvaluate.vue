<template>
  <el-dialog
    v-model="counter.ragTestShow"
    :beforeClose="counter.handleClose111"
    title="RAG评估"
    style="height: 50%; width: 40%"
  >
    <el-row :gutter="20">
      <el-col :span="10">
        <el-form
          ref="ragFormRef"
          :label-position="labelPosition"
          label-width="100px"
          :model="counter.ragFormdata"
          :rules="ragRules"
          style="max-width: 460px"
          v-if="counter.optionspg1"
        >
          <el-form-item label="项目" prop="project_id">
            <el-select
              v-model="counter.get_project_id"
              filterable
              reserve-keyword
              placeholder="请选择项目"
              @focus="counter.getProjectId()"
              @change="onProjectChange"
              clearable
            >
              <el-option
                v-for="item in counter.projectData"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="用例集名称" prop="suite_id">
            <el-select
              v-model="counter.get_rag_suite_id"
              filterable
              reserve-keyword
              :placeholder="counter.suitePlaceholder || '请选择用例集'"
              :disabled="counter.suiteDisabled"
              @focus="counter.getSuiteid(counter.get_project_id)"
              @change="onSuiteChange"
              clearable
            >
              <el-option
                v-for="item in counter.formLabelAlign3Options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>


          <el-form-item label="执行用例" prop="suite_sequence_id">
            <el-select
              v-model="counter.ragFormdata.suite_sequence_id"
              filterable
              reserve-keyword
              placeholder="请选择执行用例"
              @focus="counter.getSuiteExecute(counter.get_rag_suite_id)"
              clearable
            >
              <el-option
                v-for="item in counter.SequenceData"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
                <span style="float: left"
                  >{{ "序列号" }}{{ item.num }}{{ "：" }}{{ item.label }}</span
                >
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="模型名称" prop="model_id">
            <el-select
              v-model="counter.ragFormdata.model_id"
              filterable
              reserve-keyword
              placeholder="请选择模型"
              @focus="counter.getllmConfig()"
              clearable
            >
              <el-option
                v-for="item in counter.modelData"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="向量模型" prop="embedding_model_id">
            <el-input
              v-model="counter.ragFormdata.embedding_model_id"
              placeholder="请输入向量模型"
              maxlength="100"
              show-word-limit
            />
          </el-form-item>
          <el-form-item>
            <el-button @click="handleRagEvaluate" type="primary">评估</el-button>
            <el-button @click="cancelRag()">取消</el-button>
          </el-form-item>
        </el-form>

        <el-form
          ref="manualFormRef"
          :label-position="labelPosition"
          label-width="100px"
          :model="counter.manualForm"
          :rules="manualRules"
          style="max-width: 460px"
          v-if="counter.rengongval"
        >
          <el-form-item label="项目" prop="project_id">
            <el-select
              v-model="counter.manualForm.project_id"
              filterable
              reserve-keyword
              placeholder="请选择项目"
              @focus="counter.getProjectId()"
              @change="onManualProjectChange"
              clearable
            >
              <el-option
                v-for="item in counter.projectData"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="用例集" prop="suite_id">
            <el-select
              v-model="counter.manualForm.suite_id"
              filterable
              reserve-keyword
              @focus="counter.getSuiteid(counter.manualForm.project_id)"
              @change="onManualSuiteChange"
              :placeholder="counter.suiteNamePlaceholder || '请选择用例集'"
              clearable
            >
              <el-option
                v-for="item in counter.formLabelAlign3Options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="用例名称" prop="test_case_id">
            <el-select
              @focus="counter.materialDialog(counter.manualForm.suite_id)"
              v-model="counter.manualForm.test_case_id"
              filterable
              multiple
              :placeholder="counter.casePlaceholder || '请选择用例'"
              clearable
            >
              <el-option
                v-for="item in counter.caseOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <!-- <el-form-item label="版本">
            <el-input v-model="counter.manualForm.version"   :placeholder="counter.caseVersionPlaceholder"/>
          </el-form-item> -->

          <el-form-item label="模型回答" prop="response">
            <el-input
              v-model="counter.manualForm.response"
              type="textarea"
              :rows="3"
              placeholder="请输入模型回答"
            />
          </el-form-item>
          <el-form-item label="得分" prop="score">
            <el-input-number
              v-model="counter.manualForm.score"
              :min="0"
              :max="100"
              :precision="2"
              placeholder="请输入得分"
              style="width: 100%"
            />
          </el-form-item>
          <el-form-item label="备注">
            <el-input
              v-model="counter.manualForm.remark"
              type="textarea"
              :rows="2"
              placeholder="请输入备注（可选）"
            />
          </el-form-item>
          <el-form-item>
            <el-button @click="handleManualEvaluate" type="primary">提交评估</el-button>
            <el-button @click="cancelRagHuman()">取消</el-button>
          </el-form-item>
        </el-form>
        <!-- </div> -->
      </el-col>
      <el-col :span="5" :offset="6">
        <el-switch
          v-model="counter.value90"
          size="small"
          active-text="是否开启人工评估"
          @change="counter.value90change"
        />
      </el-col>
    </el-row>
  </el-dialog>

  <el-dialog
    v-model="counter.pgcard"
    title="RAG评估结果"
    :before-close="() => (counter.pgcard = false)"
    style="width: 20%; background-color: #ebf6fe"
  >
    <div
      style="
        border: 5px solid #96bcd9;
        border-top-width: 0.5em;
        border-bottom-width: 0;
      "
    >
      <el-divider border-style="dashed" />
    </div>
    <el-card
      class="box-card"
      style="
        width: 97%;
        background-color: #f3f9fe;
        margin-top: -11%;
        margin-left: 1.5%;
      "
    >
      <div
        style="
          background-color: #f3f9fe;
          display: flex;
          justify-content: space-between;
          align-items: center;
        "
      >
        <span>任务id</span>
        <p>{{ counter.rag_task_id }}</p>
      </div>

      <div
        style="
          background-color: #f3f9fe;
          display: flex;
          justify-content: space-between;
          align-items: center;
        "
      >
        <span>评估状态</span>
        <el-tag :type="counter.getRagStatusType(counter.rag_status)">
          {{ counter.getRagStatusText(counter.rag_status) }}
        </el-tag>
      </div>
      <div
        style="
          background-color: #f3f9fe;
          display: flex;
          justify-content: space-between;
          align-items: center;
        "
      >
        <span>开始时间</span>
        <p>{{ counter.rag_start_time }}</p>
      </div>
    </el-card>

    <template #footer>
      <span style="margin-right: 40%">
        <el-button
          style="margin-top: 10%"
          plain
          type="primary"
          round
          @click="counter.EvaluateStatus(counter.rag_task_id)"
        >
          查看详情</el-button
        >
      </span>
    </template>
  </el-dialog>

  <el-dialog
    v-model="counter.test001dialog"
    title="RAG评估详情页面"
    style="width: 80%; max-height: 90vh; overflow-y: auto"
  >
    <!-- 基础信息卡片 -->

    <el-descriptions border :column="4" title="基础信息1">
      <el-descriptions-item label="评估状态11">
        <el-tag :type="counter.getTagType(counter.ragStatus.status)">{{
          counter.getStatusText(counter.ragStatus.status)
        }}</el-tag>
      </el-descriptions-item>
      <el-descriptions-item label="执行耗时"
        >{{ counter.ragStatus.duration_seconds }}/s</el-descriptions-item
      >
      <el-descriptions-item label="开始时间">{{
        counter.formatTime(counter.ragStatus.start_time)
      }}</el-descriptions-item>
      <el-descriptions-item label="结束时间">{{
        counter.formatTime(counter.ragStatus.end_time)
      }}</el-descriptions-item>
       <el-descriptions-item v-if='counter.ragStatus.status===4' label="错误信息">{{
        counter.ragStatus.error
      }}{{ counter.ragStatus.retry_suggestion }}

    </el-descriptions-item>
    </el-descriptions>

    <template #header>
      <div class="card-header">评估状态332</div>
    </template>
    <el-table
      :data="counter.ragStatus.results"
      border
      style="width: 100%; margin-top: 16px"
      v-if="counter.ragStatus.results?.length"
    >
      <el-table-column label="用户输入" prop="label" min-width="30%">
        <template #default="{ row }">
          {{ row.user_input }}
        </template>
      </el-table-column>
      <el-table-column label="指标值" min-width="20%">
        <template #default="{ row }">
          <span v-for="i in row.material"
            >{{ i.label || "无" }}:{{ i.value || "无" }}</span
          >
        </template>
      </el-table-column>
      <el-table-column label="回答" prop="response" min-width="50%">
        <template #default="{ row }">
          <span style="white-space: pre-wrap">{{ row.response || "无" }}</span>
        </template>
      </el-table-column>
      <el-table-column label="正确答案" prop="reference" min-width="50%">
        <template #default="{ row }">
          <span style="white-space: pre-wrap">{{ row.reference || "无" }}</span>
        </template>
      </el-table-column>
    </el-table>
    <div v-else style="text-align: center; padding: 20px">
      无核心评估指标数据
    </div>
    <!-- </el-card> -->

    <!-- 对话记录表格 -->
    <el-card v-if="counter.ragStatus.conversations?.length">
      <template #header>
        <div class="card-header">对话记录详情</div>
      </template>
      <el-table
        :data="counter.ragStatus.conversations"
        border
        style="width: 100%"
      >
        <el-table-column
          label="角色"
          prop="role"
          min-width="15%"
        ></el-table-column>
        <el-table-column label="对话内容" min-width="60%">
          <template #default="{ row }">
            <pre style="white-space: pre-wrap">{{ row.content }}</pre>
          </template>
        </el-table-column>
        <el-table-column label="时间" prop="timestamp" min-width="25%">
          <template #default="{ row }">
            {{ formatTime(row.timestamp) }}
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 错误信息提示（仅失败时显示） -->
    <el-alert
      v-if="counter.ragStatus.status === 'failed'"
      title="错误详情"
      type="error"
      :description="counter.ragStatus.error_message"
      show-icon
      style="margin-top: 20px"
    />
  </el-dialog>
</template>

<script setup>
import { suiteStore } from "@/pinia/suitstore";
import { ref, reactive } from "vue";
import { ElMessage } from "element-plus";

const counter = suiteStore();
const labelPosition = ref("left");
const ragFormRef = ref();
const manualFormRef = ref();

const props = defineProps({
  pgcard: {
    type: Boolean,
  },
});

// RAG评估表单校验规则
const ragRules = reactive({
  project_id: [
    { required: true, message: '请选择项目', trigger: 'change' }
  ],
  suite_id: [
    { required: true, message: '请选择用例集', trigger: 'change' }
  ],
  suite_sequence_id: [
    { required: true, message: '请选择执行用例', trigger: 'change' }
  ],
  model_id: [
    { required: true, message: '请选择模型名称', trigger: 'change' }
  ],
  embedding_model_id: [
    { required: true, message: '请输入向量模型', trigger: 'blur' },
    { min: 1, max: 100, message: '向量模型长度需在1-100字符之间', trigger: 'blur' }
  ]
});

// 人工评估表单校验规则
const manualRules = reactive({
  project_id: [
    { required: true, message: '请选择项目', trigger: 'change' }
  ],
  suite_id: [
    { required: true, message: '请选择用例集', trigger: 'change' }
  ],
  test_case_id: [
    { required: true, message: '请选择用例名称', trigger: 'change' }
  ],
  response: [
    { required: true, message: '请输入模型回答', trigger: 'blur' },
    { min: 1, max: 2000, message: '模型回答长度需在1-2000字符之间', trigger: 'blur' }
  ],
  score: [
    { required: true, message: '请输入得分', trigger: 'change' },
    { type: 'number', min: 0, max: 100, message: '得分范围为0-100', trigger: 'change' }
  ]
});

// 项目选择变化处理
function onProjectChange(projectId) {
  // 清空用例集选择
  counter.get_rag_suite_id = null;
  counter.ragFormdata.suite_sequence_id = null;
  // 重置用例集相关数据
  counter.formLabelAlign3Options = [];
  counter.SequenceData = [];
}

// 用例集选择变化处理
function onSuiteChange(suiteId) {
  // 清空执行用例选择
  counter.ragFormdata.suite_sequence_id = null;
  // 重置执行用例数据
  counter.SequenceData = [];
}

// 人工评估项目选择变化处理
function onManualProjectChange(projectId) {
  // 清空用例集选择
  counter.manualForm.suite_id = null;
  counter.manualForm.test_case_id = null;
  // 重置用例集相关数据
  counter.formLabelAlign3Options = [];
  counter.caseOptions = [];
}

// 人工评估用例集选择变化处理
function onManualSuiteChange(suiteId) {
  // 清空用例名称选择
  counter.manualForm.test_case_id = null;
  // 重置用例数据
  counter.caseOptions = [];
}

// RAG评估提交处理
function handleRagEvaluate() {
  if (!ragFormRef.value) return;

  ragFormRef.value.validate((valid) => {
    if (valid) {
      // 同步表单数据到 ragFormdata
      counter.ragFormdata.project_id = counter.get_project_id;
      counter.ragFormdata.suite_id = counter.get_rag_suite_id;

      // 调用评估接口
      counter.requestRagEvaluate();
    } else {
      ElMessage.error('请完善表单信息');
      return false;
    }
  });
}

// 人工评估提交处理
function handleManualEvaluate() {
  if (!manualFormRef.value) return;

  manualFormRef.value.validate((valid) => {
    if (valid) {
      // 调用评估接口
      counter.postManualDate(counter.manualForm);
    } else {
      ElMessage.error('请完善表单信息');
      return false;
    }
  });
}

function cancelRag(){
  // 清除表单验证状态
  if (ragFormRef.value) {
    ragFormRef.value.clearValidate();
  }
  counter.ragTestShow = false;
}

function cancelRagHuman(){
  // 清除表单验证状态
  if (manualFormRef.value) {
    manualFormRef.value.clearValidate();
  }
  counter.ragTestShow = false;
}


</script>
<style scoped>
.rag-result-dialog {
  --el-dialog-bg-color: #fafbfc; /* 弹窗背景色 */
  .el-dialog__body {
    padding: 24px 32px; /* 增加内边距 */
  }

  .rag-content {
    background: #ffffff; /* 内容区域背景 */
    border-radius: 8px;
    padding: 16px;
  }

  .cell-item {
    display: flex;
    align-items: center;
  }

  .text {
    font-size: 14px;
  }

  .item {
    padding: 18px 0;
  }

  .box-card {
    width: 480px;
  }
  .cards-section {
    display: flex;
    justify-content: space-around;
    gap: 1rem;
    flex-wrap: wrap;
    margin-bottom: 2rem;
  }

  .card {
    background-color: #f8f9fa;
    border-radius: 8px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
    padding: 1.5rem;
    flex: 1;
    min-width: 200px;
    text-align: center;
    transition: transform 0.3s ease;
  }

  .card:hover {
    transform: translateY(-5px);
  }

  .card h3 {
    font-size: 1.1rem;
    color: #555;
    margin-bottom: 0.5rem;
  }

  .card .count {
    font-size: 1.8rem;
    color: #333;
    font-weight: bold;
  }
}
</style>
