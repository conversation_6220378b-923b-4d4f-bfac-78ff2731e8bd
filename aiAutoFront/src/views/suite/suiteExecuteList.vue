<template>
  <el-row>
    <el-col :span="24" style="height: 100%">
      <el-row style="margin-left: 0%; margin-top: 1%">
        <el-cascader 
          :options="store.suiteSelectOptions" 
          :props="store.props"
          @clear="store.suiteSelectClear"
          v-model="store.suiteSelected" 
          collapse-tags clearable filterable
        />

        <el-button type="primary" @click="store.filterSequence" style="margin-left: 1%" >查询</el-button>
        <el-button @click="store.handleReset">重置</el-button>
      </el-row>

      <el-table :data="store.tableData" stripe style="width: 100%; margin-top: 1%">
        <el-table-column prop="id" label="ID" min-width="50" />
        <el-table-column prop="project_name" label="项目" min-width="120" show-overflow-tooltip />
        <el-table-column prop="requirement_name" label="需求" min-width="120" show-overflow-tooltip />
        <el-table-column prop="suite__name" label="用例集" min-width="120" show-overflow-tooltip />
        <el-table-column label="用例集类型" min-width="100">
          <template #default="scope">
            <el-tag v-if="scope.row.suite__suite_type===0" type="success">
              RAG
            </el-tag>
            <el-tag v-else type="primary">
              LLM
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="exec_start_time" label="执行开始时间" min-width="120" :formatter="store.formatTime"/>
        <el-table-column prop="exec_end_time" label="执行结束时间" min-width="120" :formatter="store.formatTime"/>
        <el-table-column label="执行状态" min-width="80">
          <template #default="scope">
            <el-tag :type="store.getStatusType(scope.row.exec_state)">
              {{ store.getStatusText(scope.row.exec_state) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="executor__username" label="执行人" min-width="60"/>
        <el-table-column label="操作" min-width="200">
          <template #default="scope">
            <el-button type="primary" size="small" plain @click="store.openSequenceDetail(scope.row)" >详情</el-button>
            <el-button type="primary" size="small" plain @click="handleRecord(scope.row.id)" >录入多轮对话</el-button>
            <el-button v-if="scope.row.suite__suite_type===0" type="primary" size="small" plain @click="handleEvaluation(scope.row)" :disabled="scope.row.exec_state===0" >RAG评估</el-button>
            <el-button v-else type="primary" size="small" plain @click="handleEvaluation(scope.row)" :disabled="scope.row.exec_state===0">LLM评估</el-button>
          </template>
        </el-table-column>
      </el-table>

      <el-footer style="background: #fff; width: 100%">
        <el-pagination
          :page-size=store.sequencePageSize
          layout="total, prev, pager, next, sizes"
          :total=store.sequenceTotal
          :page-count=store.sequencePageCount
          :page-sizes="[20,50,100]"
          @size-change="store.handleSequenceSizeChange"
          @current-change="store.handleSequenceCurrentChange"
          style="float: right"
        />
      </el-footer>

      <!-- detail弹窗 组件 -->
      <el-dialog 
        v-if="store.detailDialogVisible"
        v-model="store.detailDialogVisible" 
        width="80%" 
        top="5vh"
        :title="store.detailTitle"
        @close="store.detailHandleClose"
      >
        <div v-for="(item) in store.detailData" :key="item.id" >
          <el-descriptions class="margin-top" :column="2" border label-width="100">
            <el-descriptions-item label="类型" :class-name="getDetailClass(item.is_multiple)">
              {{ item.is_multiple?"多轮对话":"单条问答" }}
            </el-descriptions-item>

            <el-descriptions-item label="执行时间">
              {{ store.timeFormat(item.create_time) }}
            </el-descriptions-item>

            <div v-if="item.is_multiple">
              <el-descriptions-item label="topics" :span='2'>
                {{ item.topics }}
              </el-descriptions-item>
              <el-descriptions-item label="reference" :span='2'>
                {{ item.answer_expect }}
              </el-descriptions-item>
              <el-descriptions-item label="对话内容" :span='2'>
                <div style="max-height: 250px; overflow: auto;">
                  <p v-for="kv in item.answer_llm_multiple" :key="kv.id">
                    <strong>{{kv.role}} : </strong>  {{kv.content}}
                  </p>
                </div>
              </el-descriptions-item>
            </div>

            <div v-else>
              <el-descriptions-item label="问题" :span='2'>
                {{ item.question }}
              </el-descriptions-item>

              <el-descriptions-item label="预期答案" :span='2'>
                {{ item.answer_expect }}
              </el-descriptions-item>

              <el-descriptions-item label="模型答案" :span='2'>
                {{ item.answer_llm }}
              </el-descriptions-item>
            </div>
          </el-descriptions>
          <hr/> <br/>
        </div>
        
        <!-- 分页器 -->
        <el-pagination
          :page-size=store.detailPageSize
          :page-count=store.detailPageCount
          :page-sizes="[10,20,50]"
          layout="total, prev, pager, next, sizes"          
          :total=store.detailTotal
          @size-change=store.handleDetailSizeChange
          @current-change=store.handleDetailCurrentChange
        />
      </el-dialog>

      <!-- 多轮多话录入弹窗 -->
      <el-dialog 
        v-if="recordDialogVisible"
        v-model="recordDialogVisible" 
        width="60%" 
        title="录入多轮对话"
        @close="recordHandleClose"
      >
        <el-form :model="recordForm" :rules="rules" ref="recordFormRef" style="max-width: 1000px">
          <el-form-item label="录入方式" label-width="100">
            <el-radio-group v-model="recordType">
              <el-radio-button value="page">页面输入</el-radio-button>
              <el-radio-button value="json">JSON</el-radio-button>
            </el-radio-group>
          </el-form-item>
          
          <div v-if="recordType==='page'">
            <el-form-item label="reference" label-width="100" prop="reference">
              <el-input v-model="recordForm.reference" style="width: 850px" placeholder="输入参考内容"/>
            </el-form-item>
            <el-form-item label="topics" label-width="100" prop="topics">
              <el-input-tag v-model="recordForm.topics" style="width: 850px" placeholder="按Enter回车键添加输入内容为topic"/>
            </el-form-item>
            <el-form-item label-width="100" label="conversation" prop="conversation">
              <div v-for="(item,index) in recordForm.conversation" :key="index">
                <el-input v-model="item.content0" style="width: 850px" maxlength="120" show-word-limit placeholder="user"/>
                <el-input v-model="item.content1" style="width: 850px" maxlength="120" show-word-limit placeholder="assistant"/>
                <el-button v-if="index===recordForm.conversation.length-1" type="success" @click="addConversation" :icon="Plus" circle/>
                <el-button v-else @click="delConversation(index)" type="danger" :icon="Delete" circle />
              </div>
            </el-form-item>
          </div>

          <div v-else>
            <el-form-item label-width="100" label="数据json" prop="recordDataJson">
            <el-input 
              v-model="recordForm.recordDataJson" 
              type="textarea" 
              :rows="20"
              maxlength="2000" 
              show-word-limit />
            </el-form-item>
            <el-form-item label-width="100">
              <el-alert title="数据示例:" type="success">
                [{<br />
                  "conversation": [<br />&nbsp;&nbsp;
                    {"role": "user","content": "推荐一部科幻电影吧"},<br />&nbsp;&nbsp;
                    {"role": "assistant","content": "《星际穿越》如何？涉及黑洞和五维空间，诺兰导演的作品。"},<br />&nbsp;&nbsp;
                    {"role": "user","content": "看过了，有没有类似但冷门一点的？"},<br />&nbsp;&nbsp;
                    {"role": "assistant","content": "可以试试《降临》，讲述外星语言与时间非线性感知的故事。"}<br />&nbsp;
                  ],<br />&nbsp;
                  "reference": "电影推荐",<br />&nbsp;
                  "topics": ["娱乐"]<br />&nbsp;
                },<br />
                {<br />
                  "conversation": [<br />&nbsp;&nbsp;
                    {"role": "user","content": "我想订一家今晚6点的餐厅，适合2人用餐。"},<br />&nbsp;&nbsp;
                    {"role": "assistant","content": "好的，您偏好什么菜系？比如中餐、日料或西餐？"},<br />&nbsp;&nbsp;
                    {"role": "user","content": "想要安静的西餐厅，最好有靠窗座位。"},<br />&nbsp;&nbsp;
                    {"role": "assistant","content": "已为您找到「花园西餐厅」，评分4.8分，提供靠窗位。需要现在预订吗？"}<br />&nbsp;
                  ],<br />&nbsp;
                  "reference": "订餐厅场景",<br />&nbsp;
                  "topics": ["任务型对话"]<br />
                }]<br />
              </el-alert>
            </el-form-item>
          </div>
        </el-form>
        <template #footer>
          <div class="dialog-footer">
            <el-button @click="recordCancel">取消</el-button>
            <el-button type="primary" @click="recordSave(recordFormRef)">保存</el-button>
          </div>
        </template>
      </el-dialog>

      <!-- 评估弹窗 -->
      <el-dialog 
        v-if="evaluationDialogVisible"
        v-model="evaluationDialogVisible" 
        width="60%" 
        :title="evaluationTitle"
        @close="evaluationHandleClose"
      >
        <el-form :model="evaluationForm" :rules="rules" ref="evaluationFormRef" style="max-width: 1000px">        
            <el-form-item label="type_id" label-width="150" prop="type_id">
              <el-input v-model="evaluationForm.type_id" style="width: 850px"/>
            </el-form-item>

            <el-form-item label="model_id" label-width="150" prop="model_id">
              <el-input v-model="evaluationForm.model_id" style="width: 850px" />
            </el-form-item>

            <el-form-item v-if="evaluationType===0" label="embedding_model_id" label-width="150"  prop="embedding_model_id">
              <el-input v-model="evaluationForm.embedding_model_id" style="width: 850px" />
            </el-form-item>
        </el-form>
        <template #footer>
          <div class="dialog-footer">
            <el-button @click="evaluationCancel">取消</el-button>
            <el-button type="primary" @click="evaluationSave(evaluationFormRef)">保存</el-button>
          </div>
        </template>
      </el-dialog>

    </el-col>
  </el-row>
</template>

<script lang="ts" setup>
  import { onMounted ,onUnmounted ,ref , reactive} from "vue";
  import type { FormInstance, FormRules } from 'element-plus';
  import { ElMessage, ElMessageBox } from 'element-plus'
  import { suiteExecuteStore } from "@/pinia/suiteexecute";
  import { Delete, Plus} from '@element-plus/icons-vue';
  import { recordMultipleConversation } from '@/api/executeApi'
  const store = suiteExecuteStore();
  onMounted(async () => {
    await store.getSequenceList();
    await store.getAllHierarchicalStructure();
  });

  onUnmounted(() => {
    store.$reset()
  });


  // 录入多轮对话相关 开始
  const recordFormRef = ref<FormInstance>()
  
  const checkRecordDataJson = (rule: any, value: any, callback: any) => {
    try {
      JSON.parse(value);
    callback()
    } catch (error) {
      callback(new Error('JSON格式错误'+error));
    }
  }

  const rules = reactive<FormRules>({
      reference: [
          { required: true, message: '请填写参考内容', trigger: 'blur' },
      ],
      topics: [
          { required: true, message: '请填写主题', trigger: 'blur' },
      ],
      recordDataJson: [
          { required: true, message: '请输入', trigger: 'blur' },
          { validator: checkRecordDataJson, trigger: 'blur' },
      ],
      type_id: [
          { required: true, message: '请填写 type_id', trigger: 'blur' },
      ],
      model_id: [
          { required: true, message: '请填写 model_id', trigger: 'blur' },
      ],
      embedding_model_id: [
          { required: true, message: '请填写 embedding_model_id', trigger: 'blur' },
      ]
  })

  const recordType = ref("page")

  const recordForm = reactive({
    reference: "",
    topics: [],
    conversation: [
      {role0:"user", content0:"",role1:"assistant", content1:""},
    ],
    recordDataJson:null
  })

  const recordDialogVisible=ref(false)
  
  const recordSequenceId = ref(0)

  const handleRecord = (sequenceId) => {
    console.log(sequenceId)
    recordSequenceId.value=sequenceId
    recordDialogVisible.value=true
  }

  const recordHandleClose = () =>{
    recordDialogVisible.value = false
    recordForm.reference=""
    recordForm.topics=[]
    recordForm.conversation=[
      {role0:"user", content0:"",role1:"assistant", content1:""}
    ]
    recordType.value="page"
    recordForm.recordDataJson=null
  }

  const recordCancel = () =>{
    recordHandleClose()
  }

  const addConversation=()=>{
    recordForm.conversation.push(
      {role0:"user", content0:"",role1:"assistant", content1:""}
    )
  }

  const delConversation = (index) => {
    recordForm.conversation.splice(index,1)
  }

  const recordDataJson = ref('')

  const recordSave = async (formEl: FormInstance | undefined) =>{
    if (!formEl) return
    await formEl.validate((valid, fields) => {
      if (valid) {
        let data = {
          "sequence_id":recordSequenceId.value,
          "multi_data":null,
        }

        if(recordType.value==="page"){
          let conversation = []
          recordForm.conversation.forEach(element=>{
            conversation.push({role:element.role0, content:element.content0})
            conversation.push({role:element.role1, content:element.content1})
          })

          data.multi_data = [
            {
              "conversation": conversation,
              "answer_expect": recordForm.reference,
              "topics": recordForm.topics,
            }
          ]
        }else{
          let jsonObject = JSON.parse(recordForm.recordDataJson);
          data.multi_data = jsonObject.map(element=>{
            let newElement = {
              "answer_expect":"",
              "conversation":[],
              "topics":[],
            }
            newElement.answer_expect = element.reference
            newElement.conversation = element.conversation
            newElement.topics = element.topics
            return newElement
          })
        }
        console.log(data)      
        recordMultipleConversation(data).then((res)=>{
          recordDialogVisible.value = false
        })
        ElMessage({type: 'success',message: '添加成功',})
      } else {
        ElMessage({type: 'info',message: '请检查参数',})
      }
    })
  }

  const getDetailClass= is_multiple=>{
    console.log(is_multiple)
    return is_multiple?'question-card-blue':'question-card-green'
  }
  // 录入多轮对话相关 结束

  // 评估相关 开始
  const evaluationTitle = ref('LLM评估')

  const evaluationFormRef = ref<FormInstance>()

  const evaluationForm = reactive({
    "type_id": null,
    "model_id": null,
    "embedding_model_id": null
  })

  const evaluationDialogVisible=ref(false)
  const evaluationType = ref(1)
  const evaluationSequenceId = ref(0)

  const handleEvaluation = (row) => {
    evaluationSequenceId.value=row.id
    evaluationType.value=row.suite__suite_type
    evaluationTitle.value = row.suite__suite_type===1?"LLM评估":"RAG评估"
    evaluationDialogVisible.value=true
  }

  const evaluationHandleClose = () =>{
    evaluationForm.type_id=null,
    evaluationForm.model_id=null,
    evaluationForm.embedding_model_id=null
    evaluationDialogVisible.value = false
  }

  const evaluationCancel = () =>{
    evaluationHandleClose()
  }

  const evaluationSave = async (formEl: FormInstance | undefined) =>{
    if (!formEl) return
    await formEl.validate((valid, fields) => {
      if (valid) {
        let data = {
          "type_id": evaluationForm.type_id,
          "suite_sequence_id": evaluationSequenceId.value,
          "model_id": evaluationForm.model_id,
          "embedding_model_id": evaluationForm.embedding_model_id
        }
        ElMessage({type: 'error',message: '待后续接口确定再执行'+JSON.stringify(data),})
      } else {
        ElMessage({type: 'error',message: '请检查参数',})
      }
    })
  }


  // 评估相关 结束

</script>

<style scoped>
:deep(.question-card-blue) {
  color: rgb(5, 73, 243) !important;
}

:deep(.question-card-green) {
  color: rgb(29, 195, 98) !important;
}
</style>
