<template>
  <el-row style="width: 95%">
    <el-col :span="24" >
      <el-row :gutter="20" style="margin-left: 0%; margin-top: 1%">
        <el-col :span="16">

            <el-tree-select
            class="treeSelect1"
        v-model="counter.RequireId"
        :props="counter.treeProps"
        :load="counter.loadNode"
        lazy
        style="width: 300px"

        placeholder="请选择层级数据"

      />

          <el-button
            style="color: #615ced; width: 5%; margin-left: 1%"
            plain
            @click="counter.requestSuiteInProject(counter.RequireId)"
            >查询</el-button
          >
          <el-button
            style="color: #615ced; width: 5%; margin-left: 1%"
            plain
            @click="resetHandler"
            >重置</el-button
          >
          <el-button style="color: #615ced; width: 5%" @click="testAdd" plain
            >新增</el-button
          >
        </el-col>
        <el-col :span="8">
          <div class="mt-4">
            <span style="font-size: 14px">数据评估</span>
            <el-checkbox
              class="q1"
              v-model="counter.checked3"
              @change="counter.ragchecked3"
              label="RAG"
            />
            <el-checkbox
              v-model="counter.checked4"
              @change="counter.ragchecked4"
              label="LLM"
            />
          </div>
        </el-col>
      </el-row>
      <el-table
        :data="counter.Case"
        style="width: 100%; margin-top: 1%; margin-left: 0%; height: 100%"
      >
        <el-table-column type="selection" min-width="5%"> </el-table-column>

        <el-table-column
          v-for="(column, index) in counter.suiteColumns"
          :key="index"
          :prop="column.prop"
          :label="column.label"
          min-width="15%"
          show-overflow-tooltip
        >
        </el-table-column>


       <el-table-column label="操作" min-width="50%">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-button
                type="primary"
                size="small"
                link
                @click="counter.handleEdit(row)"
              >
                <el-icon><Edit /></el-icon>
                编辑
              </el-button>

              <el-tooltip
                content="执行用例集后会给出模型回答及上下文内容为下一步测试评估提供完整数据"
                placement="top"
              >
                <el-button
                  type="success"
                  size="small"
                  link
                  @click="counter.suiteExecute(row)"
                >
                  <el-icon><VideoPlay /></el-icon>
                  执行
                </el-button>
              </el-tooltip>

              <el-button
                type="danger"
                size="small"
                link
                @click="counter.delSuiteDate(row)"
              >
                <el-icon><Delete /></el-icon>
                删除
              </el-button>

              <el-button
                type="info"
                size="small"
                link
                @click="counter.materialDialog(row)"
              >
                <el-icon><Document /></el-icon>
                物料
              </el-button>

              <el-button
                type="warning"
                size="small"
                link
                @click="counter.reportDialog(row)"
              >
                <el-icon><DataAnalysis /></el-icon>
                结果
              </el-button>

              <el-button
                type="primary"
                size="small"
                link
                @click="chartreportbutton(row)"
              >
                <el-icon><Histogram /></el-icon>
                报告
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <el-footer style="float: right; background: #fff; width: 100%">
        <el-pagination
          layout="prev, pager, next"
          :page-size="20"
          :pager-count="11"
          :total="counter.suite_total"
          @current-change="counter.handleCurrentChange"
          style="float: right; margin-top: 0.8%"
        />
      </el-footer>
    </el-col>

    <!-- 执行完成评估弹窗 -->
    <el-dialog
      v-model="counter.suiteEvaluateDialog"
      title="数据评估"
      width="30%"
      destroy-on-close
      center
      style="height: 20%"
    >
      <div style="margin-top: 8%">
        <span style="margin-left: 30%">
          <el-button @click="counter.suiteRagTestShow">RAG评估001</el-button>
        </span>

        <span>
          <el-button
            @click="counter.suiteLlmTestShow"
            type="primary"
            style="margin-left: 15%"
          >
            LLM评估
          </el-button>
        </span>
      </div>
    </el-dialog>
    <!-- 创建用例 -->
    <el-dialog
      title="创建用例集"
      :model-value="counter.addDialogVisible"
      :before-close="handleClose"
      width="30%"
      height="90%"
    >
      <el-form
        ref="ruleFormRef"
        :model="counter.ruleForm"
        :rules="suiteRules"
        label-width="100px"
        class="demo-ruleForm"
        :size="formSize"
        status-icon
      >
        <el-form-item
          v-for="item in counter.addForm"
          :key="item.prop"
          :label="item.label"
          :prop="item.prop"
        >
          <!-- 处理 requirement 选择器 -->

          <template v-if="item.prop === 'requirement'">

            <el-tree-select
            class="treeSelect"
        v-model="counter.ruleForm.requirement"
        :props="counter.treeProps"
        :load="counter.loadNode"
        lazy
        style="width: 50%"

        placeholder="请选择层级数据"

      />
          </template>
          <template v-else-if="item.prop === 'material'">
            <el-select
              v-model="counter.ruleForm.material"
              @focus="counter.getmaterialList(counter.projectnode)"
              style="width: 50%"
              multiple
            >
              <el-option
                v-for="req in counter.materialListData"
                :key="req.value"
                :label="req.label"
                :value="req.value"
              ></el-option>
            </el-select>
          </template>
          <!-- 处理 suite_type 选择器（使用 suitstore.js 中的 optionspg 数据） -->
          <template v-else-if="item.prop === 'suite_type'">
            <el-select v-model="counter.ruleForm[item.prop]" style="width: 50%">
              <el-option
                v-for="type in counter.optionspg"
                :key="type.value"
                :label="type.label"
                :value="type.value">
              </el-option>
            </el-select>
          </template>

          <template v-else>
            <el-input  v-model="counter.ruleForm[item.prop]"
              style="width: 50%"
            />
          </template>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleSubmitForm">
            保存
          </el-button>
          <el-button @click="handleResetForm">取消</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>

    <!-- RAG评估弹窗（点击checkbox使用） -->
    <ragEvaluate :pgcard="counter.pgcard"></ragEvaluate>
    <!-- llm评估弹窗（点击checkbox使用） -->
    <llmEvaluate></llmEvaluate>

    <!-- 执行RAG评估弹窗（执行>评估>RAG评估使用） -->
    <executeRagEvaluate></executeRagEvaluate>
    <!-- 执行LLM评估弹窗（执行>评估>LLM评估使用） -->
    <executeLlmEvaluate></executeLlmEvaluate>

    <el-dialog
      v-model="counter.dialoghumanEvaluation"
      style="width: 100%; height: 80%"
    >
      <humanEvaluation :count="num"></humanEvaluation>
    </el-dialog>

    <el-dialog
      v-model="counter.dialogreport"
      title="评估结果列表11"
      style="width: 100%"
    >
      <evaluation :suite_id="counter.props_suite_id"></evaluation>
    </el-dialog>
    <el-dialog v-model="chartreport" @opened="handleOpen" style="width: 100%">
      <!-- 添加标签页容器 -->
      <el-tabs v-model="activeName" @tab-click="handleClick">
        <!-- RAG报告标签页 -->
        <el-tab-pane name="first" label="RAG报告">
          <!-- 原有RAG报告内容 -->
          <section class="cards-section">
            <div v-for="(card, index) in reportCards" :key="index" class="card">
              <h3>{{ card.label }}</h3>
              <p class="count">{{ card.value }}</p>
            </div>
          </section>
          <el-row :gutter="20">
            <el-col :span="16">

                <el-skeleton v-if="!counter.elementline" :rows="6" style="margin-top:5%;" animated />
                <div id="my-element" style="width: 100%; height: 400px"></div>

            </el-col>
            <el-col :span="8">
              <div style="margin-top: 0%">
                <label for="category">指标类别：</label>
                <el-select
                  v-model="counter.selectedAnalysisMetric"
                  placeholder="请选择"
                  @change="counter.getRagReportStats(suitedvalue,'my-element-pie',counter.selectedAnalysisMetric)"
                  style="width: 20%"
                >

                  <el-option
                    v-for="item in counter.analysisMetrics"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </div>

             <!-- <el-empty v-if="counter.myElementPie" :image-size="200" /> -->
                <div
                  id="my-element-pie"
                  style="width: 100%; height: 400px"
                ></div>

            </el-col>
          </el-row>
        </el-tab-pane>

        <!-- LLM报告标签页（示例内容，可根据实际需求调整） -->
        <el-tab-pane label="LLM报告" name="second">
          <section class="cards-section">
            <!-- LLM相关卡片数据 -->
            <div v-for="(card, index) in reportCards" :key="index" class="card">
              <h3>{{ card.label }}</h3>
              <p class="count">{{ card.value }}</p>
            </div>
          </section>
          <el-row :gutter="20">
            <el-col :span="16">

              <el-skeleton v-if="!counter.llmelementline" :rows="6" style="margin-top:5%;" animated />
                <div id="llm-element" style="width: 100%; height: 400px"></div>

            </el-col>
            <el-col :span="8">
              <div style="margin-top: 0%">
                <label for="category">指标类别：</label>

                <el-select
                  v-model="counter.llmAnalysisMetric"
                  placeholder="请选择"
                  @change="counter.getllmReportStats(suitedvalue,'llm-element-pie',counter.llmAnalysisMetric)"
                  style="width: 20%"
                >

                  <el-option
                    v-for="item in counter.llmAnalysisMetrics"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </div>


                <div
                  id="llm-element-pie"
                  style="width: 100%; height: 400px"
                ></div>

            </el-col>
          </el-row>
        </el-tab-pane>
      </el-tabs>
    </el-dialog>
    <!-- 物料列表 -->
    <el-dialog v-model="counter.dialogMaterialVisible" title="物料列表">
      <el-table :data="counter.materialTableData">
        <el-table-column
          v-for="(column, index) in counter.materialColumns"
          :key="index"
          :prop="column.prop"
          :label="column.label"
          min-width="15%"
          show-overflow-tooltip
        >
        </el-table-column>
      </el-table>
    </el-dialog>

    <!-- 编辑用例集弹窗 -->
    <el-dialog
      v-model="counter.editDialogVisible"
      title="编辑用例集"
      width="30%"
      height="90%"
    >
      <el-form
        ref="editFormRef"
        :model="counter.ruleFormEdit"
        :rules="suiteEditRules"
        label-width="100px"
        class="demo-ruleForm"
        :size="formSize"
        status-icon
      >
        <!-- 遍历addForm生成表单项（与创建逻辑一致） -->
        <el-form-item
          v-for="item in counter.updateForm"
          :key="item.prop"
          :label="item.label"
          :prop="item.prop"
        >
          <!-- 需求选择器 -->
          <template v-if="item.prop === 'requirement__name'">
            <el-tree-select
            class="custom-placeholder"
        v-model="counter.editOptionsId"
        :props="counter.treeProps"
        :load="counter.loadNode"
        lazy
        style="width: 50%;"
        :placeholder="counter.ruleFormEdit.requirement__name"
      />
          </template>
          <!-- 框架类型选择器 -->
          <template v-else-if="item.prop === 'material'">
            <el-select
              v-model="counter.materialListDataId"
              @focus="counter.getmaterialList(counter.editOptionsId)"
              style="width: 50%"
            >
              <el-option
                v-for="req in counter.materialListData"
                :key="req.value"
                :label="req.label"
                :value="req.value"
              ></el-option>
            </el-select>
          </template>
          <!-- 处理 suite_type 选择器（使用 suitstore.js 中的 optionspg 数据） -->
          <template v-else-if="item.prop === 'suite_type'">
            <el-select v-model="counter.ruleFormEdit.suite_type" style="width: 50%">
              <el-option
                v-for="type in counter.optionspg"
                :key="type.value"
                :label="type.label"
                :value="type.value"
              >
              </el-option>
            </el-select>
          </template>

          <template v-else>
            <el-input
              v-model="counter.ruleFormEdit[item.prop]"
              style="width: 50%"
            ></el-input>
          </template>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleSubmitEditForm"
            >保存</el-button
          >
          <el-button @click="resetEditForm(editFormRef)">取消</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>

    <!-- 执行成功弹窗 -->
    <el-dialog
    v-model="counter.ExecuteSuccess"
    title="执行"
    width="30%"
  >
    <el-result
        icon="success"
        sub-title="用例集执行完成，是否继续评估当前用例集？"
      >
      </el-result>
    <template #footer>
      <span class="dialog-footer">
        <!-- <el-button @click="counter.ExecuteSuccess = false">取消</el-button> -->

        <!-- <router-link to="/execute" class="btn-style"> -->
          <el-button @click="goToPage">查看执行结果</el-button>
  <!-- </router-link> -->

        <!-- <el-button @click="counter.ExecuteSuccess = false">取消</el-button> -->
        <el-button type="primary" @click="counter.suiteEvaluate()">
          评估
        </el-button>
      </span>
    </template>
  </el-dialog>
  </el-row>
</template>

<script setup>
import { suiteStore } from "@/pinia/suitstore";
import { Edit, Delete, VideoPlay, Document, DataAnalysis, Histogram } from '@element-plus/icons-vue';
import {
  onMounted,
  ref,
  reactive,
  nextTick,
  onBeforeMount
} from "vue";
import { ElMessage } from 'element-plus'
import humanEvaluation from "../evaluation/humanEvaluation.vue";
import { requireList1 } from "@/api/requireApi";
import { serviceList } from "@/api/projectApi";
import { getHierarchicalStructure } from "@/api/suitApi";
import evaluation from "../evaluation/evaluationList.vue";
import ragEvaluate from "./component/ragEvaluate.vue";
import llmEvaluate from "./component/llmEvaluate.vue";
import executeRagEvaluate from "./component/executeRagEvaluate.vue";
import executeLlmEvaluate from "./component/executeLlmEvaluate.vue";
import { useRouter } from 'vue-router'
let projectId01=ref(null)
const router = useRouter()
const checked3 = ref(false);
const checked4 = ref(false);
const counter = suiteStore();
let ragTestShow = ref(false);
let llmTestShow = ref(false);

let getRequireData = ref([]);
let projectData = ref([]);
// 新增：存储完整的需求数据（包含项目关联）
let allRequirements = ref([]);
const activeName = ref("first");

let suitedvalue=ref("");
const reportCards = ref([
]);
function chartreportbutton(row) {
  chartreport.value = true;
  reportCards.value=[{
    label: "需求名称",
    value: row.requirement__name,
  },
  {
    label: "用例集",
    value: row.name,
  },
  {
    label: "版本",
    value: row.version,
  },
  {
    label: "创建人",
    value: row.creator__username,
  }
  ];
  suitedvalue.value=row.id
}

async function handleClick(tab, event) {

  if (tab.props.name === "second") {
    await nextTick(() => {
      counter.getllmReportStatistics(suitedvalue.value,"llm-element")
      counter.getllmReportStats (suitedvalue.value,"llm-element-pie",'precision')
    });
  }
}

const handleOpen = () => {
  const element = document.getElementById("my-element");
  const elementpie = document.getElementById("my-element-pie");
  if (element) {
    counter.getRagReportStatistics(suitedvalue.value,"my-element")
  } else {
    console.error("Element not found!9999");
  }

  if (elementpie) {
    counter.getRagReportStats(suitedvalue.value,"my-element-pie")

  } else {
    console.error("Element not found!");
  }
};

const formSize = ref("default");
const ruleFormRef = ref();
const editFormRef = ref();

// 用例集新增校验规则
const suiteRules = reactive({
  name: [
    { required: true, message: '用例集名称为必填项', trigger: 'blur' },
    { min: 2, max: 100, message: '用例集名称长度需在2-100字符之间', trigger: 'blur' }
  ],
  description: [
    { max: 500, message: '描述长度不能超过500字符', trigger: 'blur' }
  ],
  requirement: [
    { required: true, message: '请选择关联需求', trigger: 'change' }
  ],
  material: [
    { required: true, message: '请选择关联物料', trigger: 'change' }
  ],
  suite_type: [
    { required: true, message: '请选择用例集类型', trigger: 'change' }
  ]
});

// 用例集编辑校验规则
const suiteEditRules = reactive({
  name: [
    { required: true, message: '用例集名称为必填项', trigger: 'blur' },
    { min: 2, max: 100, message: '用例集名称长度需在2-100字符之间', trigger: 'blur' }
  ],
  description: [
    { max: 500, message: '描述长度不能超过500字符', trigger: 'blur' }
  ],
  requirement__name: [
    { required: true, message: '请选择关联需求', trigger: 'change' }
  ],
  material: [
    { required: true, message: '请选择关联物料', trigger: 'change' }
  ],
  suite_type: [
    { required: true, message: '请选择用例集类型', trigger: 'change' }
  ]
});

let num = 100;
const chartreport = ref(false);
// 新增用例集表单验证
function handleSubmitForm() {
  if (!ruleFormRef.value) return;

  ruleFormRef.value.validate((valid) => {
    if (valid) {
      counter.postaddSuite().then(() => {
        ElMessage.success('用例集创建成功');
      }).catch(() => {
        ElMessage.error('用例集创建失败');
      });
    } else {
      ElMessage.warning('请检查表单填写是否正确');
    }
  });
}

// 编辑用例集表单验证
function handleSubmitEditForm() {
  if (!editFormRef.value) return;

  editFormRef.value.validate((valid) => {
    if (valid) {
      counter.submitEditForm().then(() => {
        ElMessage.success('用例集更新成功');
      }).catch(() => {
        ElMessage.error('用例集更新失败');
      });
    } else {
      ElMessage.warning('请检查表单填写是否正确');
    }
  });
}

function testAdd() {
  counter.addDialogVisible = true;
  counter.ruleForm = {
    name: '',
    description: '',
    requirement: null,
    material: [],
    suite_type: null,
    version: ''
  };
  // 清除独立变量（保持兼容性）
  counter.optionsId = null;
  counter.materialListDataId = null;
  // 清除表单验证状态
  setTimeout(() => {
    if (ruleFormRef.value) {
      ruleFormRef.value.clearValidate();
    }
  }, 0);
}

function resetHandler() {
  counter.projectId = null;
  counter.RequireId = null;
  // 刷新用例数据
  counter.requestSuiteInProject();
}

// 取消新增用例集
function handleResetForm() {
  if (ruleFormRef.value) {
    ruleFormRef.value.resetFields();
    ruleFormRef.value.clearValidate();
  }
  counter.addDialogVisible = false;
}

function handleClose() {
  counter.addDialogVisible = false;
  checked3.value = false;
  checked4.value = false;
  llmTestShow.value = false;
  ragTestShow.value = false;
  // 清除表单验证状态
  if (ruleFormRef.value) {
    ruleFormRef.value.clearValidate();
  }
}

function getProject() {
  let params = {
    page: 1,
    page_size: 100,
  };
  serviceList(params).then((res) => {
    projectData.value = res.resp.map((item) => {
      return {
        value: item.id,
        label: item.name,
      };
    });
  });
}

function getRequire() {
  if(counter.projectId!=null){
    requireList1(counter.projectId).then((res) => {
    getRequireData.value = res.resp.map((item) => {
      return {
        label: item.name,
        value: item.id,
      };
    });
  });
  }else{
    ElMessage.error('请先选择项目！');
  }

}

// 新增：提取层级数据的函数
function extractHierarchicalData(hierarchicalData) {


  if (!hierarchicalData || !Array.isArray(hierarchicalData)) {
    console.warn('层级数据格式不正确');
    return;
  }

  // 用于存储唯一的项目和需求数据
  const projectMap = new Map();
  const requirementMap = new Map();

  // 遍历第一层（项目层）
  hierarchicalData.forEach(project => {
    // 提取项目信息
    if (project.id && project.label) {
      projectMap.set(project.id, {
        value: project.id,
        label: project.label
      });
    }

    // 遍历第二层（需求层）
    if (project.children && Array.isArray(project.children)) {
      project.children.forEach(requirement => {

        // 提取需求信息
        if (requirement.id && requirement.label) {
          requirementMap.set(requirement.id, {
            value: requirement.id,
            label: requirement.label,
            projectId: project.id // 记录所属项目ID
          });
        }
      });
    }
  });

  // 转换为数组格式
  const extractedProjects = Array.from(projectMap.values());
  const extractedRequirements = Array.from(requirementMap.values());

  // 替换查询选项中的数据
  if (extractedProjects.length > 0) {
    projectData.value = extractedProjects;
  }

  if (extractedRequirements.length > 0) {
    // 存储完整的需求数据
    allRequirements.value = extractedRequirements;
    // 初始显示所有需求
    getRequireData.value = extractedRequirements;
  }

}

// 新增：项目选择变化时的处理函数
function onProjectChange(projectId) {

  // 清空需求选择
  counter.RequireId = null;

  if (projectId && allRequirements.value.length > 0) {
    // 根据项目ID过滤需求
    const filteredRequirements = allRequirements.value.filter(req => req.projectId === projectId);
    getRequireData.value = filteredRequirements;
  } else {
    // 如果没有选择项目，显示所有需求
    getRequireData.value = allRequirements.value;
  }
}
function goToPage() {
  router.push('/execute')
      counter.ExecuteSuccess = false

    }
onBeforeMount(async () => {

  try {
    // 添加延迟，确保组件完全初始化
    await new Promise(resolve => setTimeout(resolve, 100));

    const res = await getHierarchicalStructure();

    if (res && res.resp) {
      counter.suiteProjectList = res.resp;

      // 提取第二层数据并设置到查询选项中
      extractHierarchicalData(res.resp);
    } else {
    }
  } catch (error) {


    if (error.code) {
      console.error('错误代码:', error.code);
    }

    // 检查具体的错误类型并给出用户友好的提示
    if (error.code === 'ECONNABORTED' || error.message?.includes('timeout')) {

      ElMessage.warning('接口响应较慢，请稍后重试');
    } else if (error.message?.includes('canceled') || error.message?.includes('cancelled')) {
      console.error('错误原因: 请求被取消');
      ElMessage.warning('请求被取消，请重新加载页面');
    } else if (error.response?.status === 404) {
      console.error('错误原因: 接口不存在');
      ElMessage.error('接口不存在，请联系管理员');
    } else if (error.response?.status >= 500) {
      console.error('错误原因: 服务器错误');
      ElMessage.error('服务器错误，请稍后重试');
    } else {
      console.error('错误原因: 未知错误');
      ElMessage.warning('获取层级结构失败，功能可能受限');
    }
  }
});

onMounted(async () => {
  counter.requestSuiteInProject();

});


</script>
<style scoped>
.q1 {
  /* content: "数据评估"; */
  margin-left: 3%;
}

.cards-section {
  display: flex;
  justify-content: space-around;
  gap: 1rem;
  flex-wrap: wrap;
  margin-bottom: 2rem;
}
.card {
  background-color: #f8f9fa;
  border-radius: 8px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  flex: 1;
  min-width: 200px;
  text-align: center;
  transition: transform 0.3s ease;
}
.card:hover {
  transform: translateY(-5px);
}
.card h3 {
  font-size: 1.1rem;
  color: #555;
  margin-bottom: 0.5rem;
}
.card .count {
  font-size: 1.8rem;
  color: #333;
  font-weight: bold;
}
.custom-placeholder :deep(.el-select__placeholder.is-transparent) {
  color: #606266 !important;
}

.treeSelect :deep(div .el-select__wrapper .el-tooltip__trigger .el-tooltip__trigger){
  width: 50% !important;
}

/* 操作按钮样式 */
.action-buttons {
  display: flex;
  gap: 6px;
  align-items: center;
  flex-wrap: wrap;
}

.action-buttons .el-button {
  padding: 4px 6px;
  font-size: 12px;
  border-radius: 6px;
  min-width: 60px;
}
</style>
