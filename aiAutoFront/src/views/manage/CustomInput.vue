<template>
    <span v-if="show">{{localContent}} <button @click="showInput"><el-icon><EditPen/></el-icon></button></span> 
    <el-input v-else v-model="localContent" autocomplete="off" @change="save" style="width: 140px"/>
</template>

<script lang="ts">
    export default{
        name: 'CustomInput',
    }
</script>


<script lang="ts" setup>
    import { ref,onMounted } from 'vue'
    const props = defineProps(['index','content','saveContent'])
    const show=ref(true)
    const localContent = ref('')
    const showInput=()=>{
        show.value=false
    }
    const save=()=>{
        props.saveContent(props.index,localContent.value)
        show.value=true
    }
    
    onMounted(() => {
        setTimeout(() => {
            localContent.value = props.content
        }, 500)
    })
</script>
<style>
    .p-input{
        margin: 2px;
    }
</style>
