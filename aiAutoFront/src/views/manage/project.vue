<template>
  <span>项目数据</span>

  <el-table :data="tableData" stripe style="width: 100%">
    <el-table-column prop="id" label="ID" width="100"/>
    <el-table-column prop="name" label="项目名称"/>
    <el-table-column prop="creator__username" label="创建人"/>
    <el-table-column prop="description" label="描述"/>
    <el-table-column label="操作">
        <template #header>
            <el-button type="success" class="create-project" @click="handleCreate()">新建</el-button>
        </template>
        <template #default="scope">
            <el-button size="small" @click="handleEdit(scope.$index, scope.row)">修改</el-button>
            <el-button size="small" type="danger" @click="handleDelete(scope.$index, scope.row)">删除</el-button>
      </template>
    </el-table-column>
  </el-table>

  <!-- 分页 -->
  <div>
    <el-pagination background
        layout="sizes ,prev, pager, next, total"
        v-model:current-page="page"
        v-model:page-size="page_size"
        :default-page-size="page_size"
        :total="total"
        :page-count="page_count"
        :page-sizes="[10, 20, 50, 100]"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        />
  </div>

  <!-- 新增 -->
  <el-dialog v-model="createVisible" title="新建项目" width="500" @close="createClose">
    <el-form :model="createForm" ref="createFormRef" :rules="rules">
      <el-form-item label="项目名称" :label-width="100" prop="name">
        <el-input v-model="createForm.name" autocomplete="off" />
      </el-form-item>
      <el-form-item label="项目描述" :label-width="100">
        <el-input v-model="createForm.description" autocomplete="off" />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="createCancel">取消</el-button>
        <el-button type="primary" @click="createSave(createFormRef)">保存</el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 修改 -->
  <el-dialog v-model="updateVisible" title="更新项目" width="500" @close="updateClose">
    <el-form :model="updateForm" ref="updateFormRef" :rules="rules">
      <el-form-item label="项目名称" :label-width="100" prop="name">
        <el-input v-model="updateForm.name" autocomplete="off" />
      </el-form-item>
      <el-form-item label="项目描述" :label-width="100">
        <el-input v-model="updateForm.description" autocomplete="off" />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="updateCancel">取消</el-button>
        <el-button type="primary" @click="updateSave(updateFormRef)">保存</el-button>
      </div>
    </template>
  </el-dialog>

</template>

<script lang="ts" setup>
    import { reactive,onMounted, ref,nextTick } from 'vue'
    import { getAllProject, createProject, updateProject, deleteProject } from '@/api/manageApi'
    import { ElMessage, ElMessageBox } from 'element-plus'
    import type { FormInstance, FormRules } from 'element-plus'

    const createFormRef = ref<FormInstance>()
    const updateFormRef = ref<FormInstance>()
    const rules = reactive<FormRules>({
        name: [
            { required: true, message: '请填写项目名称', trigger: 'blur' },
        ]
    })


    // 创建相关
    const createForm = reactive({
        name: '',
        description: '',
    })

    const createVisible=ref(false)

    const handleCreate = () => {
        createVisible.value=true
    }

    const createClose = () =>{
        createVisible.value = false
        createForm.name=''
        createForm.description=''
    }

    const createCancel = () =>{
        createClose()
    }

    const createSave = async (formEl: FormInstance | undefined) =>{
        if (!formEl) return
        await formEl.validate((valid, fields) => {
            if (valid) {
                let data = {
                    name:createForm.name,
                    description:createForm.description,
                }

                createProject(data).then((res)=>{
                    getData(page.value,page_size.value)
                    createVisible.value = false
                })
            } else {
                console.log('error submit!', fields)
            }
        })
    }

    // 修改相关
    const updateVisible=ref(false)
    const updateForm = reactive({
        id:0,
        name: '',
        description: '',
    })

    const handleEdit = (index: number, row: Project) => {
        updateForm.id= row.id
        updateForm.name = row.name
        updateForm.description = row.description
        updateVisible.value = true
    }

    const updateClose = () =>{
        updateVisible.value = false
    }

    const updateCancel = () =>{
        updateVisible.value = false
    }

    const updateSave = async (formEl: FormInstance | undefined) =>{
        if (!formEl) return
        await formEl.validate((valid, fields) => {
            if (valid) {
                let data = {
                    id:updateForm.id,
                    name:updateForm.name,
                    description:updateForm.description,
                }
                updateProject(data).then((res)=>{
                    getData(page.value,page_size.value)
                    updateVisible.value = false
                })
            } else {
                console.log('error submit!', fields)
            }
        })
    }

    // 删除相关
    const handleDelete = (index: number, row: Project) => {
        ElMessageBox.confirm('确定删除?','Warning', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            }
        ).then(() => {
            let params ={
                id: row.id
            }
            deleteProject({...params}).then((res)=>{
                getData(page.value,page_size.value)
                ElMessage({type: 'success',message: '删除成功',})
            })}
        ).catch(() => {ElMessage({type: 'info',message: '取消删除',})})
    }

    // 查询
    const page = ref(1)
    const page_size = ref(20)
    const total = ref(0)
    const page_count = ref(0)

    // 单页数据条数变更
    const handleSizeChange = (val: number) => {
        getData(page.value, val)
    }

    // 页数变更
    const handleCurrentChange = (val: number) => {
        getData(val, page_size.value)
    }

    // 表格数据
    let  tableData = ref([])

    // 获取全部数据
    const getData=(page:number, page_size:number)=>{
        let params = {
            page: page,
            page_size: page_size
        }
        getAllProject({...params}).then((res)=>{

            tableData.value = res.resp
            total.value = res.total
            page_count.value = res.total_page
        })
    }

    interface Project {
        id: number
        name: string
        description: string
    }

    // 初始化调用
    onMounted(()=>{
        nextTick(() => {
            getData(page.value,page_size.value)
        })


    })

</script>

<style scoped>
    .el-pagination {
        margin: 10px 20px 10px ;
        display: flex;
        justify-content: flex-end;
    }

    .create-project{
        margin: 10px 20px 10px ;
    }

</style>