<template>
  <span>用户数据</span>  

  <el-table :data="tableData" stripe style="width: 100%">
    <el-table-column prop="id" label="ID" width="100"/>
    <el-table-column prop="staff_no" label="工号"/>
    <el-table-column prop="username" label="姓名"/>
    <el-table-column prop="role" :formatter="roleFormat" label="角色"/>
    <el-table-column label="操作" width="400">
        <template #header>
            <el-input v-model="search" style="max-width: 200px" placeholder="Please input">
                <template #append>
                    <el-button @click="searchUser">
                        <el-icon><Search /></el-icon>
                    </el-button>
                </template>
            </el-input>
            <el-button type="success" class="create-user" @click="handleCreate()">新建</el-button>
        </template>
        <template #default="scope">
            <el-button size="small" @click="handleEdit(scope.$index, scope.row)">修改</el-button>
            <el-button size="small" type="danger" @click="handleDelete(scope.$index, scope.row)">删除</el-button>
      </template>
    </el-table-column>
  </el-table>

  <!-- 分页 -->
  <div>
    <el-pagination background 
        layout="sizes ,prev, pager, next, total"
        v-model:current-page="page"
        v-model:page-size="page_size"
        :default-page-size="page_size"
        :total="total" 
        :page-count="page_count"
        :page-sizes="[10, 20, 50, 100]"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        />
  </div>

  <!-- 新增 -->
  <el-dialog v-model="createVisible" title="新建用户" width="500" @close="createClose">
    <el-form :model="createForm" :rules="rules" ref="createFormRef">
      <el-form-item label="姓名" :label-width="100" prop="username">
        <el-input v-model="createForm.username" autocomplete="off" />
      </el-form-item>
      <el-form-item label="工号" :label-width="100" prop="staff_no">
        <el-input v-model="createForm.staff_no" autocomplete="off" />
      </el-form-item>
      <el-form-item label="角色" :label-width="100" prop="role">
        <el-select v-model="createForm.role">
          <el-option v-for="item in roleOption"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="createCancel">取消</el-button>
        <el-button type="primary" @click="createSave(createFormRef)">保存</el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 修改 -->
  <el-dialog v-model="updateVisible" title="更新用户" width="500" @close="updateClose">
    <el-form :model="updateForm" ref="updateFormRef" :rules="rules">
      <el-form-item label="姓名" :label-width="100" prop="username">
        <el-input v-model="updateForm.username" autocomplete="off" />
      </el-form-item>
      <el-form-item label="工号" :label-width="100" prop="staff_no">
        <el-input v-model="updateForm.staff_no" autocomplete="off" />
      </el-form-item>
      <el-form-item label="角色" :label-width="100" prop="role">
        <el-select v-model="updateForm.role">
          <el-option v-for="item in roleOption"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="updateCancel">取消</el-button>
        <el-button type="primary" @click="updateSave(updateFormRef)">保存</el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 删除 -->

</template>

<script lang="ts" setup>
    import { reactive,onMounted, ref } from 'vue'
    import { getAllUser,createUser,getUserDetail,updateUser,deleteUser } from '@/api/manageApi'
    import { ElMessage, ElMessageBox } from 'element-plus'
    import type { FormInstance, FormRules } from 'element-plus'

    const createFormRef = ref<FormInstance>()
    const updateFormRef = ref<FormInstance>()
    const rules = reactive<FormRules>({
        username: [
            { required: true, message: '请填写名称', trigger: 'blur' },
        ],
        staff_no: [
            { required: true, message: '请填写工号', trigger: 'blur' },
        ],
        role: [
            { required: true, message: '请选择角色', trigger: 'change' },
        ]
    })

    // 增加
    const createForm = reactive({
        role: 0,
        staff_no: '',
        username: '',
    })

    const createVisible=ref(false)

    const roleOption = [
        {
            value: 0,
            label: '普通用户',
        },
        {
            value: 1,
            label: '超级管理员',
        },
    ]
    
    // 创建相关
    const handleCreate = () => {
        createVisible.value=true
    }

    const createClose = () =>{
        createVisible.value = false
        createForm.role=0
        createForm.staff_no=''
        createForm.username=''
    }

    const createCancel = () =>{
        createClose()
    }
    
    const createSave = async (formEl: FormInstance | undefined) =>{
        if (!formEl) return
        await formEl.validate((valid, fields) => {
            if (valid) {
              let data = {
                  username:createForm.username,
                  staff_no:createForm.staff_no,
                  role:createForm.role,
              }
              createUser(data).then((res)=>{
                  getUserData(page.value,page_size.value,search.value)
                  createVisible.value = false
              })
            } else {
                console.log('error submit!', fields)
            }
        })
    }


    // 修改相关
    const updateVisible=ref(false)
    const updateForm = reactive({
        id:0,
        role: 0,
        staff_no: '',
        username: '',
    })

    const handleEdit = (index: number, row: User) => {
        getUserDetail(row.id).then((res)=>{
            updateForm.id=row.id
            updateForm.role = res.resp[0].role
            updateForm.staff_no = res.resp[0].staff_no
            updateForm.username = res.resp[0].username
            updateVisible.value = true
        })
    }

    const updateClose = () =>{
        updateVisible.value = false
    }

    const updateCancel = () =>{
        updateVisible.value = false
    }
    
    const updateSave = async (formEl: FormInstance | undefined) =>{
        if (!formEl) return
        await formEl.validate((valid, fields) => {
            if (valid) {
              let data = {
                  id:updateForm.id,
                  username:updateForm.username,
                  staff_no:updateForm.staff_no,
                  role:updateForm.role,
              }
              updateUser(data).then((res)=>{
                  getUserData(page.value,page_size.value,search.value)
                  updateVisible.value = false
              })
            } else {
                console.log('error submit!', fields)
            }
        })
    }

    // 删除相关
    const handleDelete = (index: number, row: User) => {
        ElMessageBox.confirm('确定删除?','Warning', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            }
        ).then(() => {
            let params ={
                id: row.id
            }
            deleteUser({...params}).then((res)=>{
                getUserData(page.value,page_size.value,search.value)
                ElMessage({type: 'success',message: '删除成功',})
            })}
        ).catch(() => {ElMessage({type: 'info',message: '取消删除',})})
    }

    // 查询
    const search = ref('')
    const page = ref(1)
    const page_size = ref(20)
    const total = ref(0)
    const page_count = ref(0)
    
    // 按照查询条件过滤user数据
    const searchUser = () => {
        getUserData(page.value, page_size.value ,search.value)
    }

    // 单页数据条数变更
    const handleSizeChange = (val: number) => {
        getUserData(page.value, val ,search.value)
    }

    // 页数变更
    const handleCurrentChange = (val: number) => {
        getUserData(val, page_size.value ,search.value)
    }

    // 表格数据
    const tableData = ref([])

    // 获取全部数据
    const getUserData=(page:number, page_size:number, fliter:string)=>{
        let params = {
            page: page,
            page_size: page_size,
            fliter: fliter,
        }
        getAllUser({...params}).then((res)=>{
            tableData.value = res.resp
            total.value = res.total
            page_count.value = res.total_page
        })
    }

    interface User {
        id: number
        role: number
        staff_no: string
        username: string
    }

    // 转化表格中的角色
    const roleFormat=(row:User)=>{
        return row.role===1?'超级管理员':'普通用户'
    }


    // 初始化调用
    onMounted(()=>{
        getUserData(page.value,page_size.value,search.value)
    })

</script>

<style scoped>
    .el-pagination {
        margin: 10px 20px 10px ;
        display: flex;
        justify-content: flex-end;
    }

    .create-user{
        margin: 10px 20px 10px ;
    }

</style>