<template>
  <div class="common-layout">
    <el-container>
      <el-header>后台管理</el-header>
      <hr/>
      <el-container>
        <el-aside width="180px">
          <el-scrollbar>
            <el-menu default-active="1-1">
              <el-sub-menu index="1">
                <template #title>
                  <el-icon><coin /></el-icon>
                  <span>项目</span>
                </template>
                <el-menu-item index="1-1" @click="activeTab = 'project'">全部项目</el-menu-item>
                <el-menu-item index="1-2" @click="activeTab = 'member'">全部用户</el-menu-item>
                <el-menu-item index="1-3" @click="activeTab = 'projectMember'">项目成员</el-menu-item>
              </el-sub-menu>
              
              <el-menu-item index="2" @click="activeTab = 'promptTemplate'">
                <el-icon><message /></el-icon>
                <template #title>提示词模版</template>
              </el-menu-item>
            </el-menu>
          </el-scrollbar>
        </el-aside>
        
        <el-main>
          <component :is="currentTabComponent" />
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>


<script setup>
  import {ref,computed} from 'vue'
  import projectMember from "./projectMember.vue"
  import member from "./member.vue"
  import project from "./project.vue"
  import promptTemplate from "./promptTemplate.vue"

  const activeTab = ref('project')

  const currentTabComponent = computed(() => {
    switch (activeTab.value) {
      case "projectMember":
        return projectMember;
      case "promptTemplate":
        return promptTemplate;
      case "member":
        return member;
      case "project":
        return project;
      default:
        return projectMember;
    }
  })

</script>

<style scoped>
  .el-header{
    font-size: 30px;
    font-weight: bold;
    text-align: center;
    border-bottom: 0.5px solid #dcdfe6;
  }
</style>