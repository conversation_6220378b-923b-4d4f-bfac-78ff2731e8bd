<template>
    <span>提示词模板</span>  
    <el-table
        :data="tableData"
        :preserve-expanded-content="true"
        stripe 
        style="width: 100%"
    >
        <el-table-column label="模板名称" prop="name" />
        <el-table-column label="模板描述" prop="description" />
        <el-table-column label="创建人" prop="creator__username" />
        <el-table-column label="更新人" prop="updater__username" />
        <el-table-column label="操作">
            <template #header>
                <el-button type="success" class="create-project-member" @click="handleCreate()">新建</el-button>
            </template>
            <template #default="scope">
                <el-button size="small" @click="handleUpdate(scope.row)">修改</el-button>
                <el-button size="small" type="danger" @click="handleDelete(scope.$index, scope.row)">删除</el-button>
            </template>
        </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div>
        <el-pagination background 
            layout="sizes ,prev, pager, next, total"
            v-model:current-page="page"
            v-model:page-size="page_size"
            :default-page-size="page_size"
            :total="total" 
            :page-count="page_count"
            :page-sizes="[10, 20, 50, 100]"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            />
    </div>

  <!-- 新增 -->
  <el-dialog v-model="createVisible" v-if="createVisible" title="增加提示词模板" width="80%" @close="createClose">
    <el-form :model="createForm" :rules="rules" ref="createFormRef">
      <el-form-item label="模版名称" :label-width="100" prop="name">
        <el-input v-model="createForm.name" autocomplete="off" />
      </el-form-item>
      <el-form-item label="模版描述" :label-width="100" prop="description">
        <el-input v-model="createForm.description" autocomplete="off" />
      </el-form-item>
      <CustomEditor ref="editorCreateRef"></CustomEditor>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="createCancel">取消</el-button>
        <el-button type="primary" @click="createSave(createFormRef)">保存</el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 更新 -->
  <el-dialog v-model="updateVisible" v-if="updateVisible" title="修改提示词模板" width="80%" @close="updateClose">
    <el-form :model="updateForm" :rules="rules" ref="updateFormRef">
      <el-form-item label="模版名称" :label-width="100" prop="name">
        <el-input v-model="updateForm.name" autocomplete="off" />
      </el-form-item>
      <el-form-item label="模版描述" :label-width="100" prop="description">
        <el-input v-model="updateForm.description" autocomplete="off" />
      </el-form-item>
      <CustomEditor ref="editorUpdateRef" :content="updateForm.content"></CustomEditor>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="updateCancel">取消</el-button>
        <el-button type="primary" @click="updateSave(updateFormRef)">保存</el-button>
      </div>
    </template>
  </el-dialog>

</template>

<script lang="ts" setup>
    import CustomInput from './CustomInput.vue'
    import CustomEditor from './CustomEditor.vue'
    import { reactive,onMounted, ref } from 'vue'
    import { getAllPromptTemplate ,updatePromptTemplate, addPromptTemplate, deletePromptTemplate} from '@/api/manageApi'
    import { ElMessage, ElMessageBox } from 'element-plus'
    import type { FormInstance, FormRules } from 'element-plus'

    // 更新数据开始
    const editorUpdateRef = ref(null)

    const updateForm = reactive({
        id: null,
        name: null,
        description: "",
        content: null,
    })

    const updateVisible=ref(false)
    
    const handleUpdate = (row) => {
        updateVisible.value=true
        updateForm.id=row.id
        updateForm.name=row.name
        updateForm.description=row.description
        updateForm.content=row.content
    }

    const updateClose = () =>{
        updateForm.id=null
        updateForm.name=null
        updateForm.description=""
        updateForm.content=null
        updateVisible.value = false
    }

    const updateCancel = () =>{
        updateClose()
    }
    
    const updateSave = async (formEl: FormInstance | undefined) =>{
        if (!formEl) return
        await formEl.validate((valid, fields) => {
            if (valid) {
                let data = {
                    id:updateForm.id,
                    name: updateForm.name,
                    description: updateForm.description,
                    content:editorUpdateRef.value.getHtml()
                }
                updatePromptTemplate(data).then((res)=>{
                    getData()
                    updateVisible.value = false
                    ElMessage({type: 'success',message: '更新成功',})
                })
            } else {
                console.log('error submit!', fields)
            }
        })
    }

    const updateFormRef = ref<FormInstance>()
    // 更新数据结束


    // 创建数据开始
    const editorCreateRef = ref(null)

    const createForm = reactive({
        name: null,
        description: "",
    })

    const createVisible=ref(false)
    
    const handleCreate = () => {
        createVisible.value=true
    }

    const createClose = () =>{
        createForm.name=null
        createForm.description=""
        createVisible.value=false
    }

    const createCancel = () =>{
        createClose()
    }
    
    const createSave = async (formEl: FormInstance | undefined) =>{
        if (!formEl) return
        await formEl.validate((valid, fields) => {
            if (valid) {
                let data = {
                    name: createForm.name,
                    description: createForm.description,
                    content:editorCreateRef.value.getHtml()
                }
                addPromptTemplate(data).then((res)=>{
                    getData()
                    createClose()
                    ElMessage({type: 'success',message: '创建成功',})
                })
            } else {
                console.log('error submit!', fields)
            }
        })
    }

    const createFormRef = ref<FormInstance>()
    const rules = reactive<FormRules>({
        name: [
            { required: true, message: '请填写名称', trigger: 'change' },
        ],
    })
    // 创建数据结束


    // 删除相关
    const handleDelete = (index: number, row) => {
        ElMessageBox.confirm('确定删除?','Warning', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            }
        ).then(() => {
            let params ={
                id: row.id
            }
            deletePromptTemplate({...params}).then((res)=>{
                getData()
                ElMessage({type: 'success',message: '删除成功',})
            })}
        ).catch(() => {ElMessage({type: 'info',message: '取消删除',})})
    }

    // 查询
    const page = ref(1)
    const page_size = ref(20)
    const total = ref(0)
    const page_count = ref(0)
    

    // 单页数据条数变更
    const handleSizeChange = (val: number) => {
        page_size.value=val
        getData()
    }

    // 页数变更
    const handleCurrentChange = (val: number) => {
        page.value = val
        getData()
    }

    // 表格数据
    const tableData = ref([])

    // 获取全部数据
    const getData=()=>{
        let params = {
            page: page.value,
            page_size: page_size.value,
        }
        getAllPromptTemplate({...params}).then((res)=>{
            tableData.value = res.resp
            total.value = res.total
            page_count.value = res.total_page
        })
    }

    // 初始化调用
    onMounted(()=>{
        getData()
    })

</script>

<style scoped>
    .el-pagination {
        margin: 10px 20px 10px ;
        display: flex;
        justify-content: flex-end;
    }

    .create-project-member{
        margin: 10px 20px 10px ;
    }

    .user-fliter{
        width: 200px;
        padding: 5px 10px 0 0;
    }

    .project-fliter{
        width: 200px;
        padding: 5px 10px 0 0;
    }

    .search-filter{
        margin-top: 5px;
        margin-left: 20px;
    }

</style>