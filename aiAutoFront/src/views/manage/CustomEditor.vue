<template>
    <div v-if="show">
        <button @click="execUpFunction">保存</button>
    </div>
    <div style="border: 1px solid #ccc">
        <Toolbar style="border-bottom: 1px solid #ccc" :editor="editorRef" :defaultConfig="toolbarConfig"
            :mode="mode" />
        <Editor style="height: 500px; overflow-y: hidden;" v-model="valueHtml" :defaultConfig="editorConfig"
            :mode="mode" @onCreated="handleCreated" @onChange="handleChange" />
    </div>
</template>

<script lang="ts">
    export default{
        name: 'CustomEditor',
        components: { Editor, Toolbar },
    }
</script>


<script lang="ts" setup>
// 
// 包安装
// npm install @wangeditor/editor --save
// pm install @wangeditor/editor-for-vue@next --save
// 文档地址
// https://www.wangeditor.com/v5/for-frame.html#vue3
//  
    import '@wangeditor/editor/dist/css/style.css' // 引入 css
    import { onBeforeUnmount, ref, shallowRef, onMounted, nextTick, defineProps } from 'vue'
    import { Editor, Toolbar } from '@wangeditor/editor-for-vue'
    import { DomEditor } from '@wangeditor/editor'
    const mode = 'default'

    // 调用父组件的方法
    const execUpFunction=()=>{
        const editor = editorRef.value
        props.saveContent(props.index,getHtml(),getText())
    }

    // 定义接受的参数
    const props = defineProps(['showSave','index','content','saveContent'])

    // 编辑器实例，必须用 shallowRef
    const editorRef = shallowRef()

    // 内容 HTML
    const valueHtml = ref()
    const show = ref(false)

    // 模拟 ajax 异步获取内容
    onMounted(() => {
        setTimeout(() => {
            valueHtml.value = props.content 
            show.value = props.showSave
        }, 200)
    })

    // 工具栏配置 可在handleChange方法中获取默认配置
    const toolbarConfig = { 
        toolbarKeys: ['bold', 'color', 'bgColor', 'fontSize', 'fontFamily', 'lineHeight', 'clearStyle'] ,
        // insertKeys:{
        //     index: 0, // 插入的位置，基于当前的 toolbarKeys
        //     keys: ['menu-custome-save'],
        // }
    }

    // 编辑器配置 可在handleChange方法中获取默认配置
    const editorConfig = {
        hoverbarKeys: {
            text: {
                menuKeys: ["bold", "clearStyle"]
            }
        }
    }

    // 组件销毁时，也及时销毁编辑器
    onBeforeUnmount(() => {
        const editor = editorRef.value
        if (editor == null) return
        editor.destroy()
    })

    const handleCreated = (editor) => {
        editorRef.value = editor // 记录 editor 实例，重要！
    }

    const showHtml = () => {
        const editor = editorRef.value;
        console.log("showHtml")
        alert(editor.getHtml())
    }

    const showText = () => {
        const editor = editorRef.value;
        console.log("showText")
        alert(editor.getText())
    }

    const getHtml = () => {
        const editor = editorRef.value;
        return editor.getHtml()
    }

    const getText = () => { 
        const editor = editorRef.value;
        return editor.getText()
    }

    const handleChange = (editor) => {
        // console.log("change")
        // console.log(editor.getText())
        // console.log(editor.getHtml())
        // const toolbar = DomEditor.getToolbar(editor)
        // 工具栏配置
        // const curToolbarConfig = toolbar.getConfig()
        // console.log(curToolbarConfig.toolbarKeys)
        // 编辑器配置
        // console.log(editor.getConfig())
    }

    defineExpose({
        getHtml,
        getText
    })
</script>
<style src="@wangeditor/editor/dist/css/style.css"></style>
