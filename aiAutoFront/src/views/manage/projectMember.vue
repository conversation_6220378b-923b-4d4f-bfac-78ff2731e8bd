<template>
  <span>项目成员</span>  
  <div class="filter">
    <el-select v-model="projectFilter" filterable placeholder="项目" clearable class="project-fliter">
        <el-option
            v-for="item in projectOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
        />
    </el-select>
    <el-select v-model="userFilter" filterable placeholder="员工" clearable class="user-fliter">
        <el-option
            v-for="item in userOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
        />
    </el-select>
    <el-button type="success" @click="handleFilter()" class="search-filter"><el-icon><Search /></el-icon></el-button>
    <el-button type="primary" @click="handleResetFilter()" class="search-filter">重置</el-button>
  </div>
  <el-table :data="tableData" stripe style="width: 100%">
    <el-table-column prop="id" label="ID" width="100"/>
    <el-table-column prop="user__username" label="成员名称"/>
    <el-table-column prop="user__staff_no" label="成员工号"/>
    <el-table-column prop="project__name" label="项目名称"/>
    <el-table-column label="操作">
        <template #header>
            <el-button type="success" class="create-project-member" @click="handleCreate()">新建</el-button>
        </template>
        <template #default="scope">
            <el-button size="small" type="danger" @click="handleDelete(scope.$index, scope.row)">删除</el-button>
      </template>
    </el-table-column>
  </el-table>

  <!-- 分页 -->
  <div>
    <el-pagination background 
        layout="sizes ,prev, pager, next, total"
        v-model:current-page="page"
        v-model:page-size="page_size"
        :default-page-size="page_size"
        :total="total" 
        :page-count="page_count"
        :page-sizes="[10, 20, 50, 100]"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        />
  </div>

  <!-- 新增 -->
  <el-dialog v-model="createVisible" title="增加成员" width="500" @close="createClose">
    <el-form :model="createForm" :rules="rules" ref="createFormRef">
      <el-form-item label="项目" :label-width="100" prop="project_id">
        <el-select v-model="createForm.project_id" filterable placeholder="项目" style="width: 240px">
            <el-option
                v-for="item in projectOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
            />
        </el-select>
      </el-form-item>
      <el-form-item label="员工" :label-width="100" prop="user_id">
        <el-select v-model="createForm.user_id" filterable placeholder="员工" style="width: 240px">
            <el-option
                v-for="item in userOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
            />
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="createCancel">取消</el-button>
        <el-button type="primary" @click="createSave(createFormRef)">保存</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
    import { reactive,onMounted, ref } from 'vue'
    import { getAllProjectMember, addProjectMember, deleteProjectMember, getAllProject, getAllUser} from '@/api/manageApi'
    import { ElMessage, ElMessageBox } from 'element-plus'
    import type { FormInstance, FormRules } from 'element-plus'

    // 项目数据
    const projectOptions = ref([])

    // 获取全部项目数据
    const getAllProjectData=()=>{
        let params = {
            page: 1,
            page_size: 1000
        }
        getAllProject({...params}).then((res)=>{
            res.resp.forEach(element => {
                projectOptions.value.push({"value":element.id,"label":element.name,"text":element.name+" # "+ element.id})
            });
        })
    }

    // 用户数据
    const userOptions = ref([])

    // 获取全部项目数据
    const getAllUserData=()=>{
        let params = {
            page: 1,
            page_size: 1000
        }
        getAllUser({...params}).then((res)=>{
            res.resp.forEach(element => {
                userOptions.value.push({"value":element.id,"label":element.username+' # '+element.staff_no,"text":element.username+' # '+element.staff_no})
            });
        })
    }

    // 过滤相关
    const userFilter = ref()
    const projectFilter = ref()
    const handleFilter = () => {
        user_ids.value.length = 0
        project_ids.value.length = 0
        if (userFilter.value && userFilter.value !== null){
            user_ids.value.push(userFilter.value)
        }
        if (projectFilter.value && projectFilter.value !== null){
            project_ids.value.push(projectFilter.value)
        }
        getData()

    }
    
    const handleResetFilter = () => {
        userFilter.value = null
        projectFilter.value = null
        user_ids.value.length = 0
        project_ids.value.length = 0
        getData()
    }

    // 创建相关
    const createForm = reactive({
        project_id: null,
        user_id: null,
    })

    const createVisible=ref(false)
    
    const handleCreate = () => {
        createVisible.value=true
    }

    const createClose = () =>{
        createVisible.value = false
        createForm.project_id=null
        createForm.user_id=null
    }

    const createCancel = () =>{
        createClose()
    }
    
    const createSave = async (formEl: FormInstance | undefined) =>{
        if (!formEl) return
        await formEl.validate((valid, fields) => {
            if (valid) {
                let data = {
                    project_id: createForm.project_id,
                    user_id: createForm.user_id,
                }

                addProjectMember(data).then((res)=>{
                    getData()
                    createVisible.value = false
                })
            } else {
                console.log('error submit!', fields)
            }
        })
    }

    const createFormRef = ref<FormInstance>()
    const rules = reactive<FormRules>({
        project_id: [
            { required: true, message: '请选择项目', trigger: 'change' },
        ],
        user_id: [
            { required: true, message: '请选择用户', trigger: 'change' },
        ],
    })

    // 删除相关
    const handleDelete = (index: number, row) => {
        ElMessageBox.confirm('确定删除?','Warning', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            }
        ).then(() => {
            let params ={
                id: row.id
            }
            deleteProjectMember({...params}).then((res)=>{
                getData()
                ElMessage({type: 'success',message: '删除成功',})
            })}
        ).catch(() => {ElMessage({type: 'info',message: '取消删除',})})
    }

    // 查询
    const page = ref(1)
    const page_size = ref(20)
    const total = ref(0)
    const page_count = ref(0)
    const user_ids = ref([])
    const project_ids = ref([])
    

    // 单页数据条数变更
    const handleSizeChange = (val: number) => {
        page_size.value=val
        getData()
    }

    // 页数变更
    const handleCurrentChange = (val: number) => {
        page.value = val
        getData()
    }

    // 表格数据
    const tableData = ref([])

    // 获取全部数据
    const getData=()=>{
        let data = {
            page: page.value,
            page_size: page_size.value,
            user_ids:user_ids.value,
            project_ids:project_ids.value
        }
        getAllProjectMember(data).then((res)=>{
            tableData.value = res.resp
            total.value = res.total
            page_count.value = res.total_page
        })
    }

    // 初始化调用
    onMounted(()=>{
        getData()
        getAllProjectData()
        getAllUserData()
    })

</script>

<style scoped>
    .el-pagination {
        margin: 10px 20px 10px ;
        display: flex;
        justify-content: flex-end;
    }

    .create-project-member{
        margin: 10px 20px 10px ;
    }

    .user-fliter{
        width: 200px;
        padding: 5px 10px 0 0;
    }

    .project-fliter{
        width: 200px;
        padding: 5px 10px 0 0;
    }

    .search-filter{
        margin-top: 5px;
        margin-left: 20px;
    }

</style>