<template>
  <el-card class="box-card" style="border-radius: 35px; display: flex; flex-flow: row; overflow: hidden; position: relative; background-color: rgb(247, 248, 252); width: 100%; height: 100%">
    <div>
      <el-button type="primary" @click="handleAdd">新增</el-button>
      <el-button type="primary" @click="handleQuery">查询</el-button>
      <el-button type="primary" @click="handleEdit">修改</el-button>
    </div>
    <el-form :model="form" label-width="120px">
      <el-form-item label="名称">
        <el-input v-model="form.name"></el-input>
      </el-form-item>
      <el-form-item label="描述">
        <el-input v-model="form.description" type="textarea"></el-input>
      </el-form-item>
    </el-form>
  </el-card>
</template>

<script>
export default {
  data() {
    return {
      form: {
        name: '',
        description: ''
      }
    };
  },
  methods: {
    handleAdd() {
      // 新增逻辑
      console.log('新增:', this.form);
    },
    handleQuery() {
      // 查询逻辑
      console.log('查询:', this.form);
    },
    handleEdit() {
      // 修改逻辑
      console.log('修改:', this.form);
    }
  }
};
</script>

<style>
/* ... existing code ... */
</style>