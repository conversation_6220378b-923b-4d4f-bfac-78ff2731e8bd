<template>
  <div class="project-list-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h1 class="page-title">项目管理</h1>
          <p class="page-subtitle">管理和监控所有项目的进展情况</p>
        </div>
        <div class="header-right">
          <el-button
            type="primary"
            class="btn-primary"
            @click="showCreateDialog = true"
          >
            <el-icon><Plus /></el-icon>
            新建项目
          </el-button>
        </div>
      </div>
    </div>

    <!-- 搜索和筛选区域 -->
    <div class="search-section">
      <div class="search-card">
        <div class="search-form">
          <div class="search-item">
            <label class="search-label">项目名称</label>
            <el-input
              v-model="searchForm.name"
              placeholder="请输入项目名称"
              class="search-input"
              clearable
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </div>

          <div class="search-item">
            <label class="search-label">创建人</label>
            <el-select
              v-model="searchForm.creator"
              placeholder="请选择创建人"
              class="search-select"
              clearable
              filterable
            >
              <el-option
                v-for="creator in creatorOptions"
                :key="creator.value"
                :label="creator.label"
                :value="creator.value"
              />
            </el-select>
          </div>

          <div class="search-item">
            <label class="search-label">创建时间</label>
            <el-date-picker
              v-model="searchForm.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              class="search-date"
            />
          </div>

          <div class="search-actions">
            <el-button type="primary" @click="handleSearch">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="handleReset">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-section">
      <div class="stats-grid">
        <div class="stat-card">
          <div class="stat-icon stat-icon--primary">
            <el-icon><Folder /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ stats.total }}</div>
            <div class="stat-label">总项目数</div>
          </div>
        </div>

        <div class="stat-card">
          <div class="stat-icon stat-icon--success">
            <el-icon><CircleCheck /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ stats.active }}</div>
            <div class="stat-label">进行中</div>
          </div>
        </div>

        <div class="stat-card">
          <div class="stat-icon stat-icon--warning">
            <el-icon><Clock /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ stats.pending }}</div>
            <div class="stat-label">待开始</div>
          </div>
        </div>

        <div class="stat-card">
          <div class="stat-icon stat-icon--info">
            <el-icon><Check /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ stats.completed }}</div>
            <div class="stat-label">已完成</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 项目列表 -->
    <div class="table-section">
      <div class="table-card">
        <div class="table-header">
          <div class="table-title">项目列表</div>
          <div class="table-actions">
            <el-button-group>
              <el-button
                :type="viewMode === 'table' ? 'primary' : ''"
                @click="viewMode = 'table'"
              >
                <el-icon><List /></el-icon>
                列表
              </el-button>
              <el-button
                :type="viewMode === 'card' ? 'primary' : ''"
                @click="viewMode = 'card'"
              >
                <el-icon><Grid /></el-icon>
                卡片
              </el-button>
            </el-button-group>
          </div>
        </div>

        <!-- 表格视图 -->
        <div v-if="viewMode === 'table'" class="table-container">
          <el-table
            :data="projectList"
            class="modern-table"
            v-loading="loading"
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" width="55" />

            <el-table-column prop="id" label="项目ID" width="100" sortable>
              <template #default="{ row }">
                <span class="project-id">#{{ row.id }}</span>
              </template>
            </el-table-column>

            <el-table-column prop="name" label="项目名称" min-width="200" sortable>
              <template #default="{ row }">
                <div class="project-name">
                  <div class="name-text">{{ row.name }}</div>
                  <div class="name-desc" v-if="row.description">{{ row.description }}</div>
                </div>
              </template>
            </el-table-column>

            <el-table-column prop="creator__username" label="创建人" width="120">
              <template #default="{ row }">
                <div class="creator-info">
                  <div class="creator-avatar">
                    {{ getInitials(row.creator__username) }}
                  </div>
                  <span class="creator-name">{{ row.creator__username }}</span>
                </div>
              </template>
            </el-table-column>

            <el-table-column prop="status" label="状态" width="100">
              <template #default="{ row }">
                <el-tag
                  :type="getStatusType(row.status)"
                  class="status-tag"
                >
                  {{ getStatusText(row.status) }}
                </el-tag>
              </template>
            </el-table-column>

            <el-table-column prop="create_time" label="创建时间" width="180" sortable>
              <template #default="{ row }">
                <div class="time-info">
                  <div class="time-date">{{ formatDate(row.create_time) }}</div>
                  <div class="time-relative">{{ getRelativeTime(row.create_time) }}</div>
                </div>
              </template>
            </el-table-column>

            <el-table-column label="操作" width="200" fixed="right">
              <template #default="{ row }">
                <div class="action-buttons">
                  <el-button
                    type="primary"
                    size="small"
                    @click="handleEdit(row)"
                    link
                  >
                    <el-icon><Edit /></el-icon>
                    编辑
                  </el-button>
                  <el-button
                    type="info"
                    size="small"
                    @click="handleView(row)"
                    link
                  >
                    <el-icon><View /></el-icon>
                    查看
                  </el-button>
                  <el-button
                    type="danger"
                    size="small"
                    @click="handleDelete(row)"
                    link
                  >
                    <el-icon><Delete /></el-icon>
                    删除
                  </el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 卡片视图 -->
        <div v-else class="card-container">
          <div class="project-cards">
            <div
              v-for="project in projectList"
              :key="project.id"
              class="project-card"
              @click="handleView(project)"
            >
              <div class="card-header">
                <div class="card-title">{{ project.name }}</div>
                <el-tag
                  :type="getStatusType(project.status)"
                  size="small"
                >
                  {{ getStatusText(project.status) }}
                </el-tag>
              </div>

              <div class="card-content">
                <p class="card-description">{{ project.description || '暂无描述' }}</p>
              </div>

              <div class="card-footer">
                <div class="card-meta">
                  <div class="creator-info">
                    <div class="creator-avatar">
                      {{ getInitials(project.creator__username) }}
                    </div>
                    <span class="creator-name">{{ project.creator__username }}</span>
                  </div>
                  <div class="create-time">{{ getRelativeTime(project.create_time) }}</div>
                </div>

                <div class="card-actions">
                  <el-button
                    type="primary"
                    size="small"
                    @click.stop="handleEdit(project)"
                  >
                    编辑
                  </el-button>
                  <el-button
                    type="danger"
                    size="small"
                    @click.stop="handleDelete(project)"
                  >
                    删除
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="pagination.current"
            v-model:page-size="pagination.size"
            :page-sizes="[10, 20, 50, 100]"
            :total="pagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>

    <!-- 创建/编辑项目对话框 -->
    <ProjectDialog
      v-model="showCreateDialog"
      :project-data="currentProject"
      :mode="dialogMode"
      @success="handleDialogSuccess"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus, Search, Refresh, Folder, CircleCheck, Clock, Check,
  List, Grid, Edit, View, Delete
} from '@element-plus/icons-vue'
import ProjectDialog from './components/ProjectDialog.vue'
import { formatDate, getRelativeTime } from '@/utils/date'

// 响应式数据
const loading = ref(false)
const viewMode = ref('table')
const showCreateDialog = ref(false)
const dialogMode = ref('create') // 'create' | 'edit'
const currentProject = ref(null)

// 搜索表单
const searchForm = reactive({
  name: '',
  creator: '',
  dateRange: null
})

// 分页
const pagination = reactive({
  current: 1,
  size: 20,
  total: 0
})

// 项目列表
const projectList = ref([])
const selectedProjects = ref([])

// 创建人选项
const creatorOptions = ref([])

// 统计数据
const stats = reactive({
  total: 0,
  active: 0,
  pending: 0,
  completed: 0
})

// 计算属性
const hasSelection = computed(() => selectedProjects.value.length > 0)

// 方法
const handleSearch = () => {
  pagination.current = 1
  fetchProjectList()
}

const handleReset = () => {
  Object.assign(searchForm, {
    name: '',
    creator: '',
    dateRange: null
  })
  handleSearch()
}

const handleSelectionChange = (selection) => {
  selectedProjects.value = selection
}

const handleEdit = (project) => {
  currentProject.value = { ...project }
  dialogMode.value = 'edit'
  showCreateDialog.value = true
}

const handleView = (project) => {
  // 跳转到项目详情页
}

const handleDelete = async (project) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除项目 "${project.name}" 吗？`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    // 调用删除API
    await deleteProject(project.id)
    ElMessage.success('删除成功')
    fetchProjectList()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const handleSizeChange = (size) => {
  pagination.size = size
  fetchProjectList()
}

const handleCurrentChange = (current) => {
  pagination.current = current
  fetchProjectList()
}

const handleDialogSuccess = () => {
  showCreateDialog.value = false
  fetchProjectList()
}

// 工具函数
const getInitials = (name) => {
  return name ? name.slice(0, 2).toUpperCase() : 'UN'
}

const getStatusType = (status) => {
  const statusMap = {
    'active': 'success',
    'pending': 'warning',
    'completed': 'info',
    'archived': 'info'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status) => {
  const statusMap = {
    'active': '进行中',
    'pending': '待开始',
    'completed': '已完成',
    'archived': '已归档'
  }
  return statusMap[status] || '未知'
}

// API 调用
const fetchProjectList = async () => {
  loading.value = true
  try {
    // 这里调用实际的API
    // const response = await projectApi.getList({
    //   ...searchForm,
    //   page: pagination.current,
    //   size: pagination.size
    // })

    // 模拟数据
    const mockData = {
      list: [
        {
          id: 1,
          name: 'AutoMatrix 前端重构',
          description: '使用 Vue 3 + Tailwind CSS 重构前端界面',
          creator__username: '张三',
          status: 'active',
          create_time: '2024-01-15T10:30:00Z',
          update_time: '2024-01-20T15:45:00Z'
        },
        // 更多模拟数据...
      ],
      total: 50
    }

    projectList.value = mockData.list
    pagination.total = mockData.total

    // 更新统计数据
    updateStats()
  } catch (error) {
    ElMessage.error('获取项目列表失败')
  } finally {
    loading.value = false
  }
}

const deleteProject = async (id) => {
  // 调用删除API
  // return await projectApi.delete(id)
  return Promise.resolve()
}

const updateStats = () => {
  stats.total = projectList.value.length
  stats.active = projectList.value.filter(p => p.status === 'active').length
  stats.pending = projectList.value.filter(p => p.status === 'pending').length
  stats.completed = projectList.value.filter(p => p.status === 'completed').length
}

// 生命周期
onMounted(() => {
  fetchProjectList()
})
</script>

<style lang="scss" scoped>
.project-list-page {
  @apply min-h-full bg-gray-50;
}

// ===== 页面头部 =====
.page-header {
  @apply bg-white border-b border-gray-200 mb-6;
}

.header-content {
  @apply container-responsive py-6 flex items-center justify-between;
}

.header-left {
  .page-title {
    @apply text-2xl font-bold text-gray-900 mb-1;
  }

  .page-subtitle {
    @apply text-gray-600 text-sm;
  }
}

.header-right {
  .btn-primary {
    @apply shadow-sm hover:shadow-md transition-shadow duration-200;
  }
}

// ===== 搜索区域 =====
.search-section {
  @apply container-responsive mb-6;
}

.search-card {
  @apply tech-card p-6;
}

.search-form {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 items-end;
}

.search-item {
  @apply space-y-2;
}

.search-label {
  @apply block text-sm font-medium text-gray-700;
}

.search-input,
.search-select,
.search-date {
  @apply w-full;
}

.search-actions {
  @apply flex gap-2;
}

// ===== 统计卡片 =====
.stats-section {
  @apply container-responsive mb-6;
}

.stats-grid {
  @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4;
}

.stat-card {
  @apply tech-card p-6 flex items-center gap-4 hover:tech-glow transition-all duration-300;
}

.stat-icon {
  @apply w-12 h-12 rounded-lg flex items-center justify-center text-white;

  &--primary {
    @apply bg-primary-500;
  }

  &--success {
    @apply bg-green-500;
  }

  &--warning {
    @apply bg-yellow-500;
  }

  &--info {
    @apply bg-blue-500;
  }
}

.stat-content {
  .stat-value {
    @apply text-2xl font-bold text-gray-900;
  }

  .stat-label {
    @apply text-sm text-gray-600;
  }
}

// ===== 表格区域 =====
.table-section {
  @apply container-responsive;
}

.table-card {
  @apply tech-card overflow-hidden;
}

.table-header {
  @apply p-6 border-b border-gray-200 flex items-center justify-between;
}

.table-title {
  @apply text-lg font-semibold text-gray-900;
}

.table-actions {
  // Element Plus button group styles will apply
}

.table-container {
  @apply overflow-x-auto;
}

.modern-table {
  @apply w-full;

  :deep(.el-table__header) {
    @apply bg-gray-50;
  }

  :deep(.el-table__row) {
    @apply hover:bg-gray-50 transition-colors duration-150;
  }
}

// ===== 表格内容样式 =====
.project-id {
  @apply font-mono text-sm text-gray-600;
}

.project-name {
  .name-text {
    @apply font-medium text-gray-900;
  }

  .name-desc {
    @apply text-sm text-gray-500 mt-1 truncate;
  }
}

.creator-info {
  @apply flex items-center gap-2;
}

.creator-avatar {
  @apply w-8 h-8 rounded-full bg-primary-600 text-white text-xs font-medium flex items-center justify-center;
}

.creator-name {
  @apply text-sm text-gray-900;
}

.status-tag {
  @apply font-medium;
}

.time-info {
  .time-date {
    @apply text-sm text-gray-900;
  }

  .time-relative {
    @apply text-xs text-gray-500;
  }
}

.action-buttons {
  @apply flex items-center gap-2;
}

// ===== 卡片视图 =====
.card-container {
  @apply p-6;
}

.project-cards {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6;
}

.project-card {
  @apply tech-card p-6 cursor-pointer hover:tech-glow transition-all duration-300 hover:scale-105;
}

.card-header {
  @apply flex items-start justify-between mb-4;
}

.card-title {
  @apply text-lg font-semibold text-gray-900 flex-1 mr-2;
}

.card-content {
  @apply mb-4;
}

.card-description {
  @apply text-gray-600 text-sm line-clamp-2;
}

.card-footer {
  @apply space-y-3;
}

.card-meta {
  @apply flex items-center justify-between;
}

.create-time {
  @apply text-xs text-gray-500;
}

.card-actions {
  @apply flex gap-2;
}

// ===== 分页 =====
.pagination-container {
  @apply p-6 border-t border-gray-200 flex justify-center;
}

// ===== 响应式设计 =====
@media (max-width: 768px) {
  .search-form {
    @apply grid-cols-1;
  }

  .stats-grid {
    @apply grid-cols-1;
  }

  .project-cards {
    @apply grid-cols-1;
  }

  .header-content {
    @apply flex-col items-start gap-4;
  }
}

// ===== 暗色主题 =====
.dark {
  .project-list-page {
    @apply bg-gray-900;
  }

  .page-header {
    @apply bg-gray-800 border-gray-700;
  }

  .page-title {
    @apply text-white;
  }

  .page-subtitle {
    @apply text-gray-300;
  }

  .search-label {
    @apply text-gray-300;
  }

  .table-title {
    @apply text-white;
  }

  .project-name .name-text {
    @apply text-white;
  }

  .creator-name {
    @apply text-gray-300;
  }

  .card-title {
    @apply text-white;
  }

  .card-description {
    @apply text-gray-400;
  }
}
</style>
