<template>
  <el-dialog
    :model-value="modelValue"
    :title="dialogTitle"
    width="600px"
    :before-close="handleClose"
    class="project-dialog"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      class="project-form"
    >
      <el-form-item label="项目名称" prop="name">
        <el-input
          v-model="formData.name"
          placeholder="请输入项目名称"
          maxlength="50"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="项目描述" prop="description">
        <el-input
          v-model="formData.description"
          type="textarea"
          placeholder="请输入项目描述"
          :rows="4"
          maxlength="200"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="项目状态" prop="status">
        <el-select
          v-model="formData.status"
          placeholder="请选择项目状态"
          class="w-full"
        >
          <el-option
            v-for="status in statusOptions"
            :key="status.value"
            :label="status.label"
            :value="status.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="项目标签" prop="tags">
        <el-select
          v-model="formData.tags"
          multiple
          filterable
          allow-create
          placeholder="请选择或输入项目标签"
          class="w-full"
        >
          <el-option
            v-for="tag in tagOptions"
            :key="tag.value"
            :label="tag.label"
            :value="tag.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="项目优先级" prop="priority">
        <el-radio-group v-model="formData.priority">
          <el-radio-button
            v-for="priority in priorityOptions"
            :key="priority.value"
            :label="priority.value"
          >
            {{ priority.label }}
          </el-radio-button>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="预计时间" prop="estimatedTime">
        <el-date-picker
          v-model="formData.estimatedTime"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          class="w-full"
        />
      </el-form-item>

      <el-form-item label="团队成员" prop="members">
        <el-select
          v-model="formData.members"
          multiple
          filterable
          placeholder="请选择团队成员"
          class="w-full"
        >
          <el-option
            v-for="member in memberOptions"
            :key="member.value"
            :label="member.label"
            :value="member.value"
          >
            <div class="member-option">
              <div class="member-avatar">
                {{ getInitials(member.label) }}
              </div>
              <span class="member-name">{{ member.label }}</span>
            </div>
          </el-option>
        </el-select>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button 
          type="primary" 
          @click="handleSubmit"
          :loading="loading"
        >
          {{ mode === 'create' ? '创建' : '更新' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  projectData: {
    type: Object,
    default: null
  },
  mode: {
    type: String,
    default: 'create', // 'create' | 'edit'
    validator: (value) => ['create', 'edit'].includes(value)
  }
})

const emit = defineEmits(['update:modelValue', 'success'])

// 响应式数据
const formRef = ref(null)
const loading = ref(false)

// 表单数据
const formData = reactive({
  name: '',
  description: '',
  status: 'pending',
  tags: [],
  priority: 'medium',
  estimatedTime: null,
  members: []
})

// 计算属性
const dialogTitle = computed(() => {
  return props.mode === 'create' ? '创建项目' : '编辑项目'
})

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入项目名称', trigger: 'blur' },
    { min: 2, max: 50, message: '项目名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  description: [
    { max: 200, message: '项目描述不能超过 200 个字符', trigger: 'blur' }
  ],
  status: [
    { required: true, message: '请选择项目状态', trigger: 'change' }
  ],
  priority: [
    { required: true, message: '请选择项目优先级', trigger: 'change' }
  ]
}

// 选项数据
const statusOptions = [
  { label: '待开始', value: 'pending' },
  { label: '进行中', value: 'active' },
  { label: '已完成', value: 'completed' },
  { label: '已暂停', value: 'paused' },
  { label: '已取消', value: 'cancelled' }
]

const priorityOptions = [
  { label: '低', value: 'low' },
  { label: '中', value: 'medium' },
  { label: '高', value: 'high' },
  { label: '紧急', value: 'urgent' }
]

const tagOptions = ref([
  { label: '前端', value: 'frontend' },
  { label: '后端', value: 'backend' },
  { label: '移动端', value: 'mobile' },
  { label: '测试', value: 'testing' },
  { label: '设计', value: 'design' },
  { label: '运维', value: 'devops' }
])

const memberOptions = ref([
  { label: '张三', value: 'zhangsan' },
  { label: '李四', value: 'lisi' },
  { label: '王五', value: 'wangwu' },
  { label: '赵六', value: 'zhaoliu' }
])

// 方法
const handleClose = () => {
  emit('update:modelValue', false)
  resetForm()
}

const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    
    loading.value = true
    
    // 这里调用API
    if (props.mode === 'create') {
      await createProject(formData)
      ElMessage.success('项目创建成功')
    } else {
      await updateProject(props.projectData.id, formData)
      ElMessage.success('项目更新成功')
    }
    
    emit('success')
    handleClose()
  } catch (error) {
    if (error !== false) { // 不是表单验证错误
      ElMessage.error(props.mode === 'create' ? '创建失败' : '更新失败')
    }
  } finally {
    loading.value = false
  }
}

const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  Object.assign(formData, {
    name: '',
    description: '',
    status: 'pending',
    tags: [],
    priority: 'medium',
    estimatedTime: null,
    members: []
  })
}

const getInitials = (name) => {
  return name ? name.slice(0, 2).toUpperCase() : 'UN'
}

// API 调用
const createProject = async (data) => {
  // 调用创建项目API
  // return await projectApi.create(data)
  return new Promise(resolve => setTimeout(resolve, 1000))
}

const updateProject = async (id, data) => {
  // 调用更新项目API
  // return await projectApi.update(id, data)
  return new Promise(resolve => setTimeout(resolve, 1000))
}

// 监听器
watch(() => props.modelValue, (newValue) => {
  if (newValue && props.projectData && props.mode === 'edit') {
    // 编辑模式下填充表单数据
    Object.assign(formData, {
      name: props.projectData.name || '',
      description: props.projectData.description || '',
      status: props.projectData.status || 'pending',
      tags: props.projectData.tags || [],
      priority: props.projectData.priority || 'medium',
      estimatedTime: props.projectData.estimatedTime || null,
      members: props.projectData.members || []
    })
  }
})
</script>

<style lang="scss" scoped>
.project-dialog {
  :deep(.el-dialog__header) {
    @apply border-b border-gray-200 pb-4;
  }
  
  :deep(.el-dialog__body) {
    @apply pt-6;
  }
}

.project-form {
  .el-form-item {
    @apply mb-6;
  }
  
  .el-textarea {
    :deep(.el-textarea__inner) {
      @apply resize-none;
    }
  }
}

.member-option {
  @apply flex items-center gap-2;
}

.member-avatar {
  @apply w-6 h-6 rounded-full bg-primary-600 text-white text-xs font-medium flex items-center justify-center;
}

.member-name {
  @apply text-sm;
}

.dialog-footer {
  @apply flex justify-end gap-3;
}

// 响应式设计
@media (max-width: 768px) {
  .project-dialog {
    :deep(.el-dialog) {
      @apply w-full mx-4;
    }
  }
}

// 暗色主题
.dark {
  .project-dialog {
    :deep(.el-dialog__header) {
      @apply border-gray-600;
    }
  }
}
</style>
