<template>
  <div class="image-upload-container">
    <!-- 图片上传区域 -->
    <div class="upload-section">
      <el-upload
        ref="uploadRef"
        class="image-uploader"
        :show-file-list="false"
        :before-upload="beforeUpload"
        :on-change="handleImageChange"
        :auto-upload="false"
        accept="image/*"
        drag
      >
        <div v-if="!uploadedImage" class="upload-placeholder">
          <el-icon class="upload-icon"><Plus /></el-icon>
          <div class="upload-text">
            <p>点击或拖拽图片到此处上传</p>
            <p class="upload-hint">支持 JPG、PNG、GIF、BMP、WEBP 格式，文件大小不超过 10MB</p>
          </div>
        </div>
        <div v-else class="uploaded-image">
          <img :src="imagePreviewUrl" alt="上传的图片" />
          <div class="image-overlay">
            <el-button type="danger" size="small" @click.stop="removeImage">
              <el-icon><Delete /></el-icon>
              删除
            </el-button>
          </div>
        </div>
      </el-upload>
    </div>

    <!-- 表单区域 -->
    <div class="form-section">
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="100px"
        @submit.prevent="handleSubmit"
      >
        <el-form-item label="上下文信息" prop="context">
          <el-input
            v-model="formData.context"
            type="textarea"
            :rows="4"
            placeholder="请描述图片的背景信息，如：这是一个用户登录流程图，包含用户名密码验证、记住密码、忘记密码等功能..."
            maxlength="1000"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="测试需求" prop="requirements">
          <el-input
            v-model="formData.requirements"
            type="textarea"
            :rows="4"
            placeholder="请描述测试用例生成的具体需求，如：需要生成功能测试用例，包括正常流程、异常流程、边界值测试等..."
            maxlength="1000"
            show-word-limit
          />
        </el-form-item>

        <el-form-item>
          <el-button
            type="primary"
            @click="handleSubmit"
            :loading="isGenerating"
            :disabled="!uploadedImage || !formData.context.trim() || !formData.requirements.trim()"
            size="large"
            style="width: 100%;"
          >
            <el-icon v-if="!isGenerating"><Star /></el-icon>
            {{ isGenerating ? '正在生成测试用例...' : '生成测试用例11' }}
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 生成进度 -->
    <div v-if="isGenerating" class="progress-section">
      <el-progress
        :percentage="progressPercentage"
        :status="progressStatus"
        :stroke-width="8"
      />
      <p class="progress-text">{{ progressText }}</p>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus, Delete, Star } from '@element-plus/icons-vue'

// Props
const props = defineProps({
  isGenerating: {
    type: Boolean,
    default: false
  },
  uploadedImage: {
    type: Object,
    default: null
  }
})

// Emits
const emit = defineEmits(['image-uploaded', 'generate-test-cases'])

// 响应式数据
const uploadRef = ref(null)
const formRef = ref(null)
const uploadedImage = ref(null)
const imagePreviewUrl = ref('')
const progressPercentage = ref(0)
const progressStatus = ref('')
const progressText = ref('')

// 表单数据
const formData = reactive({
  context: '',
  requirements: ''
})

// 表单验证规则
const formRules = {
  context: [
    { required: true, message: '请输入上下文信息', trigger: 'blur' },
    { min: 10, message: '上下文信息至少需要10个字符', trigger: 'blur' }
  ],
  requirements: [
    { required: true, message: '请输入测试需求', trigger: 'blur' },
    { min: 10, message: '测试需求至少需要10个字符', trigger: 'blur' }
  ]
}

// 监听生成状态变化
watch(() => props.isGenerating, (newVal) => {
  if (newVal) {
    startProgress()
  } else {
    stopProgress()
  }
})

// 上传前验证
const beforeUpload = (file) => {
  const isImage = file.type.startsWith('image/')
  const isLt10M = file.size / 1024 / 1024 < 10

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt10M) {
    ElMessage.error('图片大小不能超过 10MB!')
    return false
  }
  return false // 阻止自动上传
}

// 处理图片变化
const handleImageChange = (file) => {
  if (file.raw) {
    uploadedImage.value = file.raw

    // 创建预览URL
    const reader = new FileReader()
    reader.onload = (e) => {
      imagePreviewUrl.value = e.target.result
    }
    reader.readAsDataURL(file.raw)

    emit('image-uploaded', file.raw)
  }
}

// 删除图片
const removeImage = () => {
  uploadedImage.value = null
  imagePreviewUrl.value = ''
  if (uploadRef.value) {
    uploadRef.value.clearFiles()
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!uploadedImage.value) {
    ElMessage.warning('请先上传图片')
    return
  }

  try {
    await formRef.value.validate()

    // 创建FormData
    const formDataToSend = new FormData()
    formDataToSend.append('image', uploadedImage.value)
    formDataToSend.append('context', formData.context)
    formDataToSend.append('requirements', formData.requirements)

    emit('generate-test-cases', formDataToSend)
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

// 开始进度条
const startProgress = () => {
  progressPercentage.value = 0
  progressStatus.value = ''
  progressText.value = '正在上传图片...'

  const interval = setInterval(() => {
    if (progressPercentage.value < 90) {
      progressPercentage.value += Math.random() * 10

      if (progressPercentage.value < 30) {
        progressText.value = '正在分析图片内容...'
      } else if (progressPercentage.value < 60) {
        progressText.value = '正在生成测试用例...'
      } else {
        progressText.value = '正在优化测试用例...'
      }
    }
  }, 500)

  // 存储interval ID以便清理
  progressInterval = interval
}

// 停止进度条
const stopProgress = () => {
  if (progressInterval) {
    clearInterval(progressInterval)
    progressInterval = null
  }
  progressPercentage.value = 100
  progressStatus.value = 'success'
  progressText.value = '测试用例生成完成!'
}

let progressInterval = null

// 清理
const cleanup = () => {
  if (progressInterval) {
    clearInterval(progressInterval)
  }
}

// 组件卸载时清理
import { onUnmounted } from 'vue'
onUnmounted(cleanup)
</script>

<style scoped>
.image-upload-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.upload-section {
  flex-shrink: 0;
}

.image-uploader {
  width: 100%;
}

:deep(.el-upload) {
  width: 100%;
}

:deep(.el-upload-dragger) {
  width: 100%;
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  transition: all 0.3s;
}

:deep(.el-upload-dragger:hover) {
  border-color: #409eff;
}

.upload-placeholder {
  text-align: center;
  color: #8c939d;
}

.upload-icon {
  font-size: 48px;
  color: #c0c4cc;
  margin-bottom: 16px;
}

.upload-text p {
  margin: 8px 0;
}

.upload-hint {
  font-size: 12px;
  color: #a8abb2;
}

.uploaded-image {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.uploaded-image img {
  max-width: 100%;
  max-height: 180px;
  object-fit: contain;
  border-radius: 4px;
}

.image-overlay {
  position: absolute;
  top: 10px;
  right: 10px;
  opacity: 0;
  transition: opacity 0.3s;
}

.uploaded-image:hover .image-overlay {
  opacity: 1;
}

.form-section {
  flex: 1;
  overflow-y: auto;
}

.progress-section {
  flex-shrink: 0;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.progress-text {
  text-align: center;
  margin-top: 10px;
  color: #606266;
  font-size: 14px;
}

:deep(.el-form-item__label) {
  font-weight: 600;
}

:deep(.el-textarea__inner) {
  resize: none;
}
</style>
