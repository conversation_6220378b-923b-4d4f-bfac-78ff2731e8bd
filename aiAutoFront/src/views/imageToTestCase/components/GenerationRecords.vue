<template>
  <div class="generation-records">
    <el-table
      :data="records"
      v-loading="loading"
      stripe
      style="width: 100%"
      @row-click="handleRowClick"
      row-class-name="clickable-row"
    >
      <el-table-column prop="created_at" label="生成时间" width="180">
        <template #default="{ row }">
          {{ formatDate(row.created_at) }}
        </template>
      </el-table-column>
      
      <el-table-column prop="context" label="上下文" min-width="200">
        <template #default="{ row }">
          <el-tooltip :content="row.context" placement="top" :disabled="row.context.length <= 50">
            <span class="text-ellipsis">{{ truncateText(row.context, 50) }}</span>
          </el-tooltip>
        </template>
      </el-table-column>
      
      <el-table-column prop="requirements" label="测试需求" min-width="200">
        <template #default="{ row }">
          <el-tooltip :content="row.requirements" placement="top" :disabled="row.requirements.length <= 50">
            <span class="text-ellipsis">{{ truncateText(row.requirements, 50) }}</span>
          </el-tooltip>
        </template>
      </el-table-column>
      
      <el-table-column prop="status" label="状态" width="120">
        <template #default="{ row }">
          <el-tag :type="getStatusType(row.status)" size="small">
            {{ getStatusText(row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column label="文件" width="180">
        <template #default="{ row }">
          <div class="file-actions">
            <el-button
              v-if="row.excel_url"
              type="success"
              size="small"
              @click.stop="handleDownloadExcel(row.id)"
              :loading="downloadingExcel === row.id"
            >
              <el-icon><Download /></el-icon>
              Excel
            </el-button>
            <el-button
              v-if="row.xmind_url"
              type="primary"
              size="small"
              @click.stop="handleDownloadXMind(row.id)"
              :loading="downloadingXMind === row.id"
            >
              <el-icon><Download /></el-icon>
              XMind
            </el-button>
            <span v-if="!row.excel_url && !row.xmind_url" class="no-files">
              {{ row.status === 'processing' ? '生成中...' : '无文件' }}
            </span>
          </div>
        </template>
      </el-table-column>
      
      <el-table-column label="操作" width="100">
        <template #default="{ row }">
          <el-button
            type="primary"
            size="small"
            @click.stop="handleViewRecord(row)"
          >
            查看
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div v-if="records.length > 0" class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 空状态 -->
    <div v-if="!loading && records.length === 0" class="empty-state">
      <el-empty description="暂无生成记录">
        <template #image>
          <el-icon class="empty-icon"><Folder /></el-icon>
        </template>
      </el-empty>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Download, Folder } from '@element-plus/icons-vue'
import dayjs from 'dayjs'

// Props
const props = defineProps({
  records: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['record-selected', 'download-excel', 'download-xmind'])

// 响应式数据
const currentPage = ref(1)
const pageSize = ref(20)
const downloadingExcel = ref(null)
const downloadingXMind = ref(null)

// 计算属性
const total = computed(() => props.records.length)

// 格式化日期
const formatDate = (dateString) => {
  return dayjs(dateString).format('YYYY-MM-DD HH:mm:ss')
}

// 截断文本
const truncateText = (text, maxLength) => {
  if (!text) return ''
  return text.length > maxLength ? text.substring(0, maxLength) + '...' : text
}

// 获取状态类型
const getStatusType = (status) => {
  const statusMap = {
    'processing': 'warning',
    'ai_completed': 'info',
    'completed': 'success',
    'failed': 'danger',
    'timeout': 'warning'
  }
  return statusMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    'processing': '生成中',
    'ai_completed': '文件生成中',
    'completed': '已完成',
    'failed': '失败',
    'timeout': '超时'
  }
  return statusMap[status] || status
}

// 处理行点击
const handleRowClick = (row) => {
  emit('record-selected', row)
}

// 查看记录
const handleViewRecord = (row) => {
  emit('record-selected', row)
}

// 下载Excel
const handleDownloadExcel = async (recordId) => {
  try {
    downloadingExcel.value = recordId
    emit('download-excel', recordId)
  } catch (error) {
    ElMessage.error('下载失败: ' + error.message)
  } finally {
    downloadingExcel.value = null
  }
}

// 下载XMind
const handleDownloadXMind = async (recordId) => {
  try {
    downloadingXMind.value = recordId
    emit('download-xmind', recordId)
  } catch (error) {
    ElMessage.error('下载失败: ' + error.message)
  } finally {
    downloadingXMind.value = null
  }
}

// 处理页面大小变化
const handleSizeChange = (newSize) => {
  pageSize.value = newSize
  currentPage.value = 1
}

// 处理当前页变化
const handleCurrentChange = (newPage) => {
  currentPage.value = newPage
}
</script>

<style scoped>
.generation-records {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.text-ellipsis {
  display: inline-block;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  vertical-align: top;
}

.file-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.no-files {
  color: #909399;
  font-size: 12px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.empty-state {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.empty-icon {
  font-size: 120px;
  color: #c0c4cc;
}

:deep(.clickable-row) {
  cursor: pointer;
}

:deep(.clickable-row:hover) {
  background-color: #f5f7fa;
}

:deep(.el-table__row) {
  transition: background-color 0.2s;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .file-actions {
    flex-direction: column;
    gap: 4px;
  }
  
  :deep(.el-button) {
    padding: 4px 8px;
    font-size: 12px;
  }
}
</style>
