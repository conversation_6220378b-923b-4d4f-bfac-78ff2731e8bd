<template>
  <div class="test-case-display">
    <!-- 生成状态显示 -->
    <div v-if="isGenerating || generationStatus" class="status-section">
      <el-alert
        :title="generationStatus || '正在生成测试用例...'"
        :type="getAlertType()"
        :closable="false"
        show-icon
      >
        <template v-if="isGenerating">
          <div class="generating-animation">
            <el-icon class="rotating"><Loading /></el-icon>
            <span>AI正在分析图片并生成测试用例，请稍候...</span>
          </div>
        </template>
      </el-alert>
    </div>

    <!-- 测试用例内容 -->
    <div v-if="testCases" class="content-section">
      <!-- 内容切换标签 -->
      <el-tabs v-model="activeTab" class="content-tabs">
        <el-tab-pane label="预览模式" name="preview">
          <div class="markdown-content" v-html="renderedMarkdown"></div>
        </el-tab-pane>
        <el-tab-pane label="源码模式" name="source">
          <el-input
            :model-value="testCases"
            type="textarea"
            :rows="20"
            readonly
            class="source-textarea"
          />
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 空状态 -->
    <div v-else-if="!isGenerating" class="empty-state">
      <el-empty
        description="暂无测试用例"
        :image-size="120"
      >
        <template #image>
          <el-icon class="empty-icon"><Document /></el-icon>
        </template>
        <template #description>
          <p>请上传图片并填写相关信息后生成测试用例</p>
        </template>
      </el-empty>
    </div>

    <!-- 统计信息 -->
    <div v-if="testCases && !isGenerating" class="stats-section">
      <el-row :gutter="16">
        <el-col :span="8">
          <el-statistic title="字符数" :value="characterCount" />
        </el-col>
        <el-col :span="8">
          <el-statistic title="行数" :value="lineCount" />
        </el-col>
        <el-col :span="8">
          <el-statistic title="预估用例数" :value="estimatedTestCases" />
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { Loading, Document } from '@element-plus/icons-vue'
import { marked } from 'marked'

// Props
const props = defineProps({
  testCases: {
    type: String,
    default: ''
  },
  isGenerating: {
    type: Boolean,
    default: false
  },
  generationStatus: {
    type: String,
    default: ''
  }
})

// 响应式数据
const activeTab = ref('preview')

// 计算属性
const renderedMarkdown = computed(() => {
  if (!props.testCases) return ''

  try {
    // 配置marked选项
    marked.setOptions({
      breaks: true,
      gfm: true
    })

    return marked(props.testCases)
  } catch (error) {
    console.error('Markdown渲染失败:', error)
    return `<pre>${props.testCases}</pre>`
  }
})

// 字符数统计
const characterCount = computed(() => {
  return props.testCases ? props.testCases.length : 0
})

// 行数统计
const lineCount = computed(() => {
  return props.testCases ? props.testCases.split('\n').length : 0
})

// 预估测试用例数量
const estimatedTestCases = computed(() => {
  if (!props.testCases) return 0

  // 简单的启发式算法：统计包含"测试用例"、"TC"、"Test Case"等关键词的行数
  const lines = props.testCases.split('\n')
  let count = 0

  lines.forEach(line => {
    const lowerLine = line.toLowerCase()
    if (
      lowerLine.includes('测试用例') ||
      lowerLine.includes('test case') ||
      lowerLine.includes('tc-') ||
      lowerLine.includes('tc_') ||
      /^\s*\d+\.\s/.test(line) || // 数字列表
      /^\s*-\s/.test(line) // 破折号列表
    ) {
      count++
    }
  })

  return Math.max(count, Math.floor(characterCount.value / 200)) // 至少按字符数估算
})

// 获取警告类型
const getAlertType = () => {
  if (props.isGenerating) return 'info'
  if (props.generationStatus.includes('完成')) return 'success'
  if (props.generationStatus.includes('失败') || props.generationStatus.includes('错误')) return 'error'
  if (props.generationStatus.includes('超时')) return 'warning'
  return 'info'
}

// 监听测试用例变化，自动切换到预览模式
watch(() => props.testCases, (newVal) => {
  if (newVal && activeTab.value === 'source') {
    // 如果有新内容且当前在源码模式，可以选择是否自动切换
    // activeTab.value = 'preview'
  }
})
</script>

<style scoped>
.test-case-display {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.status-section {
  margin-bottom: 16px;
  flex-shrink: 0;
}

.generating-animation {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 8px;
}

.rotating {
  animation: rotate 2s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.content-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.content-tabs {
  height: 100%;
  display: flex;
  flex-direction: column;
}

:deep(.el-tabs__content) {
  flex: 1;
  overflow: hidden;
}

:deep(.el-tab-pane) {
  height: 100%;
  overflow-y: auto;
}

.markdown-content {
  padding: 16px;
  background-color: #fafafa;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
  height: 100%;
  overflow-y: auto;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
}

/* Markdown样式 */
:deep(.markdown-content h1) {
  color: #303133;
  border-bottom: 2px solid #409eff;
  padding-bottom: 8px;
  margin-bottom: 16px;
}

:deep(.markdown-content h2) {
  color: #606266;
  border-bottom: 1px solid #dcdfe6;
  padding-bottom: 6px;
  margin: 20px 0 12px 0;
}

:deep(.markdown-content h3) {
  color: #909399;
  margin: 16px 0 8px 0;
}

:deep(.markdown-content p) {
  margin: 8px 0;
  color: #606266;
}

:deep(.markdown-content ul, .markdown-content ol) {
  margin: 8px 0;
  padding-left: 24px;
}

:deep(.markdown-content li) {
  margin: 4px 0;
  color: #606266;
}

:deep(.markdown-content table) {
  width: 100%;
  border-collapse: collapse;
  margin: 16px 0;
}

:deep(.markdown-content th, .markdown-content td) {
  border: 1px solid #dcdfe6;
  padding: 8px 12px;
  text-align: left;
}

:deep(.markdown-content th) {
  background-color: #f5f7fa;
  font-weight: 600;
  color: #303133;
}

:deep(.markdown-content code) {
  background-color: #f0f2f5;
  padding: 2px 6px;
  border-radius: 3px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.9em;
  color: #e6a23c;
}

:deep(.markdown-content pre) {
  background-color: #f0f2f5;
  padding: 12px;
  border-radius: 6px;
  overflow-x: auto;
  margin: 12px 0;
}

:deep(.markdown-content blockquote) {
  border-left: 4px solid #409eff;
  padding-left: 16px;
  margin: 16px 0;
  color: #909399;
  background-color: #f8f9fa;
  padding: 12px 16px;
  border-radius: 0 6px 6px 0;
}

.source-textarea {
  height: 100%;
}

:deep(.source-textarea .el-textarea__inner) {
  height: 100% !important;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.5;
}

.empty-state {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.empty-icon {
  font-size: 120px;
  color: #c0c4cc;
}

.stats-section {
  margin-top: 16px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
  flex-shrink: 0;
}

:deep(.el-statistic__head) {
  font-size: 14px;
  color: #909399;
}

:deep(.el-statistic__content) {
  font-size: 20px;
  font-weight: 600;
  color: #303133;
}
</style>
