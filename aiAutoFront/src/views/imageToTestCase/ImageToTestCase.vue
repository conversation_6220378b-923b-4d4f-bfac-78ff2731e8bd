<template>
  <div class="image-to-testcase-container">
    <el-container>
      <el-header class="page-header">
        <h1>智能工具 - 图生用例</h1>
        <p>上传流程图、思维导图或UI截图，AI智能生成测试用例</p>
        <div v-if="!backendConnected" class="backend-status">
          <el-alert
            title="后端服务未连接"
            description="请启动后端服务以使用完整功能"
            type="warning"
            :closable="false"
            show-icon
          />
        </div>
      </el-header>
      
      <el-main>
        <el-row :gutter="20">
          <!-- 左侧：上传和生成区域 -->
          <el-col :span="12">
            <el-card class="upload-card" shadow="hover">
              <template #header>
                <div class="card-header">
                  <span>图片上传与用例生成</span>
                </div>
              </template>
              
              <!-- 图片上传区域 -->
              <ImageUpload 
                ref="imageUploadRef"
                @image-uploaded="handleImageUploaded"
                @generate-test-cases="handleGenerateTestCases"
                :is-generating="isGenerating"
                :uploaded-image="uploadedImage"
              />
            </el-card>
          </el-col>
          
          <!-- 右侧：测试用例展示区域 -->
          <el-col :span="12">
            <el-card class="result-card" shadow="hover">
              <template #header>
                <div class="card-header">
                  <span>生成的测试用例</span>
                  <div class="header-actions" v-if="currentRecord">
                    <el-button 
                      type="primary" 
                      size="small" 
                      @click="downloadExcel"
                      :loading="downloadingExcel"
                      :disabled="!currentRecord.excel_url"
                    >
                      <el-icon><Download /></el-icon>
                      下载Excel
                    </el-button>
                    <el-button 
                      type="success" 
                      size="small" 
                      @click="downloadXMind"
                      :loading="downloadingXMind"
                      :disabled="!currentRecord.xmind_url"
                    >
                      <el-icon><Download /></el-icon>
                      下载XMind
                    </el-button>
                  </div>
                </div>
              </template>
              
              <!-- 测试用例展示 -->
              <TestCaseDisplay 
                :test-cases="generatedTestCases"
                :is-generating="isGenerating"
                :generation-status="generationStatus"
              />
            </el-card>
          </el-col>
        </el-row>
        
        <!-- 生成记录列表 -->
        <el-row class="records-section">
          <el-col :span="24">
            <el-card shadow="hover">
              <template #header>
                <div class="card-header">
                  <span>生成记录</span>
                  <el-button 
                    type="primary" 
                    size="small" 
                    @click="refreshRecords"
                    :loading="loadingRecords"
                  >
                    <el-icon><Refresh /></el-icon>
                    刷新
                  </el-button>
                </div>
              </template>
              
              <GenerationRecords 
                :records="generationRecords"
                :loading="loadingRecords"
                @record-selected="handleRecordSelected"
                @download-excel="downloadRecordExcel"
                @download-xmind="downloadRecordXMind"
              />
            </el-card>
          </el-col>
        </el-row>
      </el-main>
    </el-container>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElNotification } from 'element-plus'
import { Download, Refresh } from '@element-plus/icons-vue'
import ImageUpload from './components/ImageUpload.vue'
import TestCaseDisplay from './components/TestCaseDisplay.vue'
import GenerationRecords from './components/GenerationRecords.vue'
import { useImageToTestCaseStore } from '@/stores/imageToTestCaseStore'

const store = useImageToTestCaseStore()

// 响应式数据
const uploadedImage = ref(null)
const isGenerating = ref(false)
const generatedTestCases = ref('')
const generationStatus = ref('')
const currentRecord = ref(null)
const generationRecords = ref([])
const loadingRecords = ref(false)
const downloadingExcel = ref(false)
const downloadingXMind = ref(false)
const backendConnected = ref(true)

// 组件引用
const imageUploadRef = ref(null)

// 处理图片上传
const handleImageUploaded = (imageFile) => {
  uploadedImage.value = imageFile
  ElMessage.success('图片上传成功')
}

// 处理生成测试用例
const handleGenerateTestCases = async (formData) => {
  try {
    isGenerating.value = true
    generatedTestCases.value = ''
    generationStatus.value = '正在生成测试用例...'
    
    const result = await store.generateTestCases(formData)
    currentRecord.value = result.record
    
    // 处理流式响应
    const reader = result.response.body.getReader()
    const decoder = new TextDecoder()
    
    while (true) {
      const { done, value } = await reader.read()
      if (done) break
      
      const chunk = decoder.decode(value, { stream: true })
      generatedTestCases.value += chunk
    }
    
    generationStatus.value = '测试用例生成完成'
    ElNotification({
      title: '生成成功',
      message: '测试用例已生成完成，文件正在后台处理中',
      type: 'success'
    })
    
    // 刷新记录列表
    await refreshRecords()
    
  } catch (error) {
    console.error('生成测试用例失败:', error)
    ElMessage.error('生成测试用例失败，请确保后端服务已启动')
    generationStatus.value = '生成失败'
  } finally {
    isGenerating.value = false
  }
}

// 下载Excel文件
const downloadExcel = async () => {
  if (!currentRecord.value?.id) return
  
  try {
    downloadingExcel.value = true
    await store.downloadExcel(currentRecord.value.id)
    ElMessage.success('Excel文件下载成功')
  } catch (error) {
    ElMessage.error('下载Excel文件失败，请确保后端服务已启动')
  } finally {
    downloadingExcel.value = false
  }
}

// 下载XMind文件
const downloadXMind = async () => {
  if (!currentRecord.value?.id) return
  
  try {
    downloadingXMind.value = true
    await store.downloadXMind(currentRecord.value.id)
    ElMessage.success('XMind文件下载成功')
  } catch (error) {
    ElMessage.error('下载XMind文件失败，请确保后端服务已启动')
  } finally {
    downloadingXMind.value = false
  }
}

// 刷新记录列表
const refreshRecords = async () => {
  try {
    loadingRecords.value = true
    generationRecords.value = await store.getGenerationRecords()
    backendConnected.value = true
  } catch (error) {
    // 静默处理错误，不显示错误消息
    console.warn('获取记录列表失败，可能是后端服务未启动:', error.message)
    generationRecords.value = []
    backendConnected.value = false
  } finally {
    loadingRecords.value = false
  }
}

// 处理记录选择
const handleRecordSelected = (record) => {
  currentRecord.value = record
  generatedTestCases.value = record.markdown_content || ''
  generationStatus.value = getStatusText(record.status)
}

// 下载记录的Excel文件
const downloadRecordExcel = async (recordId) => {
  try {
    await store.downloadExcel(recordId)
    ElMessage.success('Excel文件下载成功')
  } catch (error) {
    ElMessage.error('下载Excel文件失败，请确保后端服务已启动')
  }
}

// 下载记录的XMind文件
const downloadRecordXMind = async (recordId) => {
  try {
    await store.downloadXMind(recordId)
    ElMessage.success('XMind文件下载成功')
  } catch (error) {
    ElMessage.error('下载XMind文件失败，请确保后端服务已启动')
  }
}

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    'processing': '正在生成测试用例...',
    'ai_completed': '测试用例生成完成，正在生成文件...',
    'completed': '全部完成',
    'failed': '生成失败',
    'timeout': '生成超时'
  }
  return statusMap[status] || status
}

// 组件挂载时获取记录列表
onMounted(() => {
  refreshRecords()
})
</script>

<style scoped>
.image-to-testcase-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
}

.image-to-testcase-container .el-container {
  background-color: transparent;
}

.image-to-testcase-container .el-main {
  padding-top: 0;
}

.page-header {
  text-align: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 8px;
  margin-bottom: 20px;
  padding: 30px 20px;
  min-height: 120px;
}

.page-header h1 {
  margin: 0 0 15px 0;
  font-size: 28px;
  font-weight: 600;
  line-height: 1.2;
}

.page-header p {
  margin: 0 0 15px 0;
  font-size: 16px;
  opacity: 0.9;
  line-height: 1.4;
}

.backend-status {
  margin-top: 20px;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    padding: 20px 15px;
    min-height: 100px;
  }

  .page-header h1 {
    font-size: 24px;
    margin-bottom: 10px;
  }

  .page-header p {
    font-size: 14px;
    margin-bottom: 10px;
  }

  .image-to-testcase-container {
    padding: 15px;
  }
}

.upload-card,
.result-card {
  height: 600px;
  margin-bottom: 20px;
}

.records-section {
  margin-top: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  font-size: 16px;
}

.header-actions {
  display: flex;
  gap: 10px;
}

:deep(.el-card__body) {
  height: calc(100% - 60px);
  overflow-y: auto;
}
</style>
