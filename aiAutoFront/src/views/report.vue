<template>
    <!-- ... 其他现有代码 ... -->

    <!-- 结果详情弹窗 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="结果详情"
      :width="800"
      :before-close="handleDetailClose"
    >
      <el-descriptions border :column="1" style="margin: 20px 0;">
        <el-descriptions-item label="参考内容">
          {{ currentDetail.reference }}
        </el-descriptions-item>
        <el-descriptions-item label="响应内容">
          {{ currentDetail.response }}
        </el-descriptions-item>
        <el-descriptions-item label="检索上下文">
          {{ currentDetail.retrieved_contexts }}
        </el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </template>

<script setup>
import { ref } from 'vue';
import { reportStore } from '@/pinia/reportModule';

const counter = reportStore();
const detailDialogVisible = ref(false);  // 控制弹窗显示
const currentDetail = ref({});  // 当前选中的详情数据

// 触发详情弹窗的方法（例如在表格操作列中调用）
const handleShowDetail = (item) => {
  currentDetail.value = {
    reference: item.reference,
    response: item.response,
    retrieved_contexts: item.retrieved_contexts
  };
  detailDialogVisible.value = true;
};

// 关闭弹窗时清空数据
const handleDetailClose = () => {
  currentDetail.value = {};
  detailDialogVisible.value = false;
};
</script>

<style scoped>
.el-dialog__body {
  padding: 20px 30px;
}
.el-descriptions-item__content {
  word-break: break-all;  /* 长文本自动换行 */
  font-size: 14px;
}
</style>

