<template>
  <transition name="fade">
    <div v-show="visible" class="modal-overlay" @click.self="handleClose">
      <div class="modal-content">
        <!-- 弹窗标题 -->
        <div class="modal-header">
          <h3>提示词库</h3>
          <button class="close-button" @click="handleClose">×</button>
        </div>

        <!-- Tabs + 搜索框 -->
        <div class="tabs-container">
          <!-- 子菜单 Tabs -->
          <div class="tabs">
            <button
              :class="{ active: currentTab === 'recommended' }"
              @click="switchTab('recommended')"
              aria-pressed="currentTab === 'recommended'"
            >
              推荐
            </button>
            <button
              :class="{ active: currentTab === 'personal' }"
              @click="switchTab('personal')"
              aria-pressed="currentTab === 'personal'"
            >
              个人
            </button>
          </div>

          <!-- 搜索框 -->
          <div class="search-and-add">
            <input
              type="text"
              placeholder="搜索模板名称或内容..."
              v-model="searchQuery"
              class="search-input"
              aria-label="模板搜索"
            />
            <button
              class="add-button"
              @click="openAddTemplateModal"
              aria-label="新增提示词"
            >
              <span> + 新增提示词 </span>
            </button>
          </div>
        </div>

        <!-- 内容区域 -->
        <div class="modal-body">
          <!-- 推荐内容 -->
          <div v-if="currentTab === 'recommended'" class="tab-content">
            <recommended-templates
              :templates="filteredTemplates"
              :selected-template="selectedTemplate"
              :search-query="searchQuery"
              @select="selectTemplate"
            />
          </div>

          <!-- 个人内容 -->
          <div v-if="currentTab === 'personal'" class="tab-content">
            <div v-if="personalTemplatesData.length > 0">
              <recommended-templates
                :templates="filteredTemplates"
                :selected-template="selectedTemplate"
                :search-query="searchQuery"
                @select="selectTemplate"
              />
            </div>
            <div v-else class="empty-state">暂无提示词</div>

            <!-- 右侧预览区域 -->
            <div
              v-if="selectedTemplate && selectedTemplate.value"
              class="preview-area"
            >
              <div class="preview-content">
                <generic-content-renderer
                  :content="selectedTemplate.value.content"
                />
              </div>
            </div>
          </div>
        </div>

        <!-- 新增按钮区域 -->
        <div class="preview-actions">
          <button
            class="preview-button"
            @click="handleComparisonDebug"
            title="提示词对比调试"
          >
            提示词对比调试
          </button>
          <button class="preview-button" @click="copyPrompt" title="复制提示词">
            复制提示词
          </button>
          <button
            class="preview-button"
            @click="insertPrompt"
            title="插入提示词"
          >
            插入提示词
          </button>
        </div>
      </div>
    </div>
  </transition>
  <!-- 调用创建提示词表单 -->
  <template-modal
    v-model:model-value="showAddTemplateModal"
    :is-new="modalTemplateData.isNew"
    :template="{
      name: modalTemplateData.name,
      description: modalTemplateData.description,
      content: modalTemplateData.content,
    }"
    @save="handleSubmitTemplate"
  />
</template>

<script setup>
import {
  ref,
  computed,
  onMounted,
  reactive,
  watch,
  watchEffect,
  nextTick,
} from "vue";
import RecommendedTemplates from "./RecommendedTemplates.vue";
import { templateAdd, templateList, promptList } from "@/api/promptTemplateApi";
import TemplateModal from "./TemplateModal.vue";
import DOMPurify from "dompurify";
import { debounce } from "lodash-es";
import { useRouter, useRoute } from "vue-router";
import { useClipboard } from "@vueuse/core";
// 导入通用内容渲染组件
import GenericContentRenderer from "@/components/GenericContentRenderer.vue";

const router = useRouter();
const route = useRoute();
const { copy } = useClipboard();

let params = {
  page: "1",
  page_size: "10",
};
const props = defineProps({
  modelValue: Boolean,
});
const sanitizedContent = ref(""); // 安全处理后的内容
const showAddTemplateModal = ref(false); // 新增提示词弹窗
const editorEl = ref(null);
const emit = defineEmits([
  "update:modelValue",
  "select",
  "save",
  "saved",
  "insert", // 添加 insert 事件声明
]);

// 控制显示状态
const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit("update:modelValue", val),
});

// 新增提示词相关数据和方法
let modalTemplateData = reactive({
  name: "",
  description: "",
  content: "",
  isNew: true,
});

// 当前 tab（推荐 / 个人）
const currentTab = ref("recommended");

// 搜索关键词
const searchQuery = ref("");

// 是否已加载过数据
const hasLoadedRecommended = ref(false);

// 初始化加载默认模板内容
watch(
  () => props.modelValue,
  async (newVal) => {
    if (newVal) {
      currentTab.value = "recommended";
      searchQuery.value = "";
      selectedTemplate.value = null;

      // 只有在第一次打开时才加载数据
      if (!hasLoadedRecommended.value) {
        await loadRecommendedTemplates();
        hasLoadedRecommended.value = true;
      }
    } else if (newVal && currentTab.value === "personal") {
      await loadPersonalTemplates();
    }
  }
);

// 切换 tab 时加载对应数据
function switchTab(tab) {
  if (currentTab.value === tab) return; // 防止重复切换

  currentTab.value = tab;
  selectedTemplate.value = null; // 清空当前选中模板
  sanitizedContent.value = ""; // 清空右侧预览区域

  if (tab === "recommended") {
  } else if (tab === "personal") {
    loadPersonalTemplates();
  }
}

// 根据当前 tab 获取过滤后的模板数据
const filteredTemplates = computed(() => {
  const data =
    currentTab.value === "recommended"
      ? recommendedTemplatesData.value
      : personalTemplatesData.value;
  return filterTemplates(data);
});

// 模板数据
const recommendedTemplatesData = ref([]);
const personalTemplatesData = ref([]);
const selectedTemplate = ref(null); // 选中模板

// 过滤模板方法
function filterTemplates(templates) {
  if (!searchQuery.value) return templates;

  const query = searchQuery.value.toLowerCase();
  return templates.filter(
    (item) =>
      (item.name && item.name.toLowerCase().includes(query)) ||
      (item.content && item.content.toLowerCase().includes(query))
  );
}

// 加载推荐模板
async function loadRecommendedTemplates() {
  try {
    const res = await templateList({ ...params });
    if (res.success && Array.isArray(res.resp)) {
      recommendedTemplatesData.value = res.resp.map((item) => ({
        ...item,
        name: item.name,
        content: item.content,
      }));
    } else {
      recommendedTemplatesData.value = [];
    }
  } catch (error) {
    console.error("加载推荐模板失败:", error);
    recommendedTemplatesData.value = [];
  }
}

// 加载个人模板
async function loadPersonalTemplates() {
  try {
    const res = await promptList();
    if (res.success && Array.isArray(res.resp)) {
      personalTemplatesData.value = res.resp.map((item) => ({
        ...item,
        name: item.template_name || "未命名模板",
        content: item.template_content || "",
      }));
    } else {
      personalTemplatesData.value = [];
    }
  } catch (error) {
    console.error("加载个人模板失败:", error);
    personalTemplatesData.value = [];
  }
}

// 切换模板时更新内容
function selectTemplate(template) {
  if (!template) {
    console.error("选中的模板为 null 或 undefined");
    return;
  }

  selectedTemplate.value = { ...template }; // 确保 template 不为空再赋值
  if (currentTab.value === "personal" && !personalTemplatesData.value.length) {
    loadPersonalTemplates();
  }
}

// 提示词库 - 新增提示词按钮
function openAddTemplateModal() {
  modalTemplateData.name = "";
  modalTemplateData.description = "";
  modalTemplateData.content = "";
  modalTemplateData.isNew = true;
  showAddTemplateModal.value = true;
}

onMounted(() => {
  if (editorEl.value && modalTemplateData.content) {
    editorEl.value.innerHTML = modalTemplateData.content;
    editorEl.value.focus();
  }
});

// 外部传入模板内容时，仅当当前为空时设置
watch(
  () => modalTemplateData.content,
  (newVal) => {
    if (editorEl.value && newVal && !editorEl.value.innerHTML.trim()) {
      editorEl.value.innerHTML = newVal;
    }
  }
);

// 同步 props.template 到 currentTemplate，并设置默认值
watchEffect(() => {
  modalTemplateData.name = modalTemplateData.name || "";
  modalTemplateData.description = modalTemplateData.description || "";
  modalTemplateData.content = modalTemplateData.content || "<p><br></p>"; // 设置默认 HTML 内容

  // 如果是编辑模式，设置初始内容并聚焦
  if (editorEl.value && modalTemplateData.content) {
    editorEl.value.innerHTML = modalTemplateData.content;
  }
});

function handleClose() {
  visible.value = false;
  searchQuery.value = "";
}

// 提示词对比调试
function handleComparisonDebug() {
  if (!selectedTemplate.value) {
    alert("请先选择要对比的模板");
    return;
  }

  router.push({
    path: "./PromptComparison",
    query: {
      comparisonContent: encodeURIComponent(selectedTemplate.value.content),
      replaceComparison: true,
      templateType: "",
    },
  });
  visible.value = false;
}

// 复制提示词
async function copyPrompt() {
  if (!selectedTemplate.value) return;

  try {
    // 创建一个临时div并添加到DOM中
    const tempDiv = document.createElement("div");
    tempDiv.innerHTML = selectedTemplate.value.content;
    document.body.appendChild(tempDiv); // 先添加到DOM

    // 使用execCommand复制带样式的内容
    const range = document.createRange();
    range.selectNode(tempDiv);
    window.getSelection().removeAllRanges();
    window.getSelection().addRange(range);
    document.execCommand("copy");
    window.getSelection().removeAllRanges();

    // 移除临时元素
    document.body.removeChild(tempDiv);

    alert("提示词已复制到剪贴板");
  } catch (error) {
    console.error("复制失败:", error);
    alert("复制失败，请重试");
  }
}

// 插入提示词
function insertPrompt() {
  if (!selectedTemplate.value) {
    alert("请先选择要插入的模板");
    return;
  }

  // 判断当前路由是否为对比调试页面
  const isFromComparison = route.path.includes("PromptComparison");

  if (!isFromComparison) {
    // 来自提示词管理界面，直接插入
    emit("insert", selectedTemplate.value.content);
  } else {
    // 来自对比调试界面或其他页面，跳转至对比调试页并传递参数
    router.push({
      path: "./PromptComparison",
      query: {
        comparisonContent: encodeURIComponent(selectedTemplate.value.content),
        source: "ComparisonLibrary", // 标识来自提示词库界面
        insertComparison: true,
      },
    });
    emit("insert", selectedTemplate.value.content);
  }

  visible.value = false;
}
</script>

<style scoped>
/* 提示词库弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.modal-content {
  background-color: #fff;
  border-radius: 10px;
  padding: 20px;
  width: 1000px;
  max-height: 100vh;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

.modal-body {
  flex: 1;
}
/* 弹窗标题 */
.modal-header {
  display: flex;
  text-align: center;
}

.modal-header h3 {
  margin: 0;
  font-size: 20px;
  color: #2c3e50;
  font-weight: bold;
}
/* 新增提示词 */
.search-and-add {
  display: flex;
  align-items: center;
  gap: 10px;
  flex-wrap: flex-end;
}

/* 搜索框 */
.search-input {
  width: 300px;
  padding: 8px 12px;
  margin-bottom: 10px;
  border: 1px solid #ccc;
  border-radius: 6px;
  font-size: 14px;
}

/* 新增按钮样式 */
.add-button {
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #4a90e2, #3d7bd6);
  color: white;
  border: none;
  padding: 8px 14px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  min-width: 120px; /* 防止文本溢出 */
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
  margin-bottom: 8px;
}

.add-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.add-button span {
  font-weight: bold;
  color: white;
  margin-left: 5px;
}

/* 关闭按钮 */
.close-button {
  background-color: transparent;
  border: none;
  font-size: 24px;
  color: #999;
  cursor: pointer;
}

.close-button:hover {
  color: #333;
}

/* 标签页容器 */
.tabs-container {
  display: flex;
  margin-top: 10px;
  align-items: center; /* 垂直居中 */
  justify-content: space-between; /* 添加此行使子元素水平排列并靠右 */
}

.tabs {
  display: flex;
  justify-content: center; /* 水平居中 */
  margin-bottom: 10px;
}

.tabs button {
  padding: 8px 16px;
  border: none;
  background-color: #f0f0f0;
  cursor: pointer;
  font-weight: 500;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.tabs button.active {
  background-color: #4a90e2;
  color: white;
}

.tab-content {
  border-top: 1px solid #eee;
  padding-top: 10px;
}

.empty-state {
  text-align: center;
  color: #888;
  font-size: 14px;
  margin-top: 20px;
}

/* 个人模板列表 */
.template-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.template-item {
  padding: 10px 15px;
  border-bottom: 1px solid #eee;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.template-item:hover {
  background-color: #f7f7f7;
}

/* 过渡动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s;
}
.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.preview-area {
  margin-top: 20px;
  padding: 15px;
  border-top: 1px solid #eee;
}

.preview-content {
  white-space: pre-wrap;
  word-break: break-word;
  line-height: 1.6;
  color: #333;
}

/* 新增按钮样式 */
.preview-actions {
  display: flex;
  gap: 10px;
  padding-top: 20px;
  background-color: #fff;
  position: sticky;
  bottom: 0;
  border-top: 1px solid #eee;
  justify-content: flex-end;
}

.preview-button {
  padding: 8px 16px;
  background-color: #4a90e2;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.preview-button:hover {
  background-color: #3d7bd6;
}

/* 从 TemplateModal.vue 迁移的样式 */
.template-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10000;
}

/* 新增提示词弹窗内容容器 */
.template-modal-overlay .add-template-modal-content {
  background-color: #fff;
  width: 700px; /* 宽度明确作用于新增提示词弹窗 */
  border-radius: 10px;
  overflow: hidden;
}

.modal-header {
  padding: 15px 20px;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h3 {
  margin: 0;
  font-size: 18px;
}

.modal-header button {
  background: transparent;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #999;
  transition: color 0.2s ease;
}
</style>
