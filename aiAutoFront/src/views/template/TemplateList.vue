<template>
  <div class="page-wrapper">
    <!-- 左侧模型选择 + 左右两个面板 -->
    <div class="top-section">
      <!-- 模型选择下拉框 -->
      <div class="model-select-container">
        <label for="model-select">模型选择：</label>
        <select id="model-select" v-model="selectedModel" class="model-select">
          <option
            v-for="model in modelOptions"
            :key="model.name"
            :value="model.value"
          >
            {{ model.label }}
          </option>
        </select>
        <span class="arrow-icon">▼</span>
      </div>
      <!-- 左右两个面板 -->
      <div class="panels-container">
        <!-- 左侧导航栏 -->
        <div class="left-panel">
          <!-- 顶部按钮区域 -->
          <div class="top-button-area">
            <!-- 左侧标题 -->
            <div class="button-title">
              <b>人设与回复逻辑</b>
            </div>
            <!-- 右侧按钮组 -->
            <div class="button-group">
              <button
                class="top-button"
                @click="openSubmitTemplateModal"
                aria-label="提交到提示词库"
              >
                <img src="@/assets/svg/submitPrompt.svg" style="width: 20px" />
                <span class="tooltip">提交到提示词库</span>
              </button>
              <button
                class="top-button"
                @click="openPromptLibraryModal"
                aria-label="打开提示词库"
              >
                <img src="@/assets/svg/promptLibrary.svg" style="width: 20px" />
                <span class="tooltip">提示词库</span>
              </button>
              <button
                class="top-button"
                @click="comparativeDebugging"
                aria-label="提示词对比调试"
              >
                <img
                  src="@/assets/svg/comparisonPrompt.svg"
                  style="width: 20px"
                />
                <span class="tooltip">提示词对比调试</span>
              </button>
              <button
                class="top-button"
                @click="handleButton4"
                aria-label="自动优化提示词"
              >
                <img
                  src="@/assets/svg/optimizePrompt.svg"
                  style="width: 20px"
                />
                <span class="tooltip">自动优化提示词</span>
              </button>
            </div>
          </div>

          <!-- 显示带样式的内容 -->
          <div
            ref="editorEl"
            class="template-rich-text"
            contenteditable="true"
            @input="onTemplateContentChange"
          ></div>
        </div>
        <!-- 中间内容区域 -->
        <div class="center-panel">
          <div v-for="(section, index) in centerSections" :key="index">
            <h4>{{ section.title }}</h4>
            <ul>
              <li v-for="(item, idx) in section.items" :key="idx">
                {{ item }}
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>

    <!-- 右侧预览与调试区域 -->
    <div class="right-panel">
      <!-- 新增：顶部对齐容器 -->
      <div class="header-container">
        <div class="preview-header">
          <h3>预览与调试</h3>
        </div>
      </div>
      <!-- 聊天记录展示区域 -->
      <div class="chat-history" ref="chatHistory">
        <div
          class="chat-message"
          v-for="(msg, index) in chatMessages"
          :key="index"
          :class="{ 'user-message': msg.role === 'user' }" 
        >
          <div>
            {{ msg.role === "user" ? "" : "助手: " }}
            <!-- 修改判断逻辑，优先显示已有内容 -->
            <generic-content-renderer
              v-if="msg.content"
              :content="msg.content"
              :is-chat-message="true"
            />
            <!-- 只在没有内容且是流式处理时才显示提示 -->
            <span
              v-else-if="
                msg.role === 'assistant' &&
                isStreaming &&
                isCurrentAssistantMessageIndex(index)
              "
            >
              正在努力查询中...
              <span class="loading-spinner"></span>
            </span>
            <!-- 新增条件分支以展示错误内容 -->
            <span
              v-else-if="
                msg.role === 'assistant' &&
                msg.content &&
                msg.content.startsWith('发生错误：')
              "
              style="color: red;"
            >
              {{ msg.content.replace('发生错误：', '') }}
            </span>
            <!-- 修改：当 parsed.type 为 error 时不显示加载提示 -->
            <span
              v-else-if="
                msg.role === 'assistant' &&
                msg.content &&
                !isStreaming
              "
            >
              {{ msg.content }}
            </span>
          </div>
        </div>
      </div>

      <!-- 输入区域容器 -->
      <div class="input-container">
        <div class="preview-input-wrapper">
          <!-- 添加图片预览区域 -->
          <div v-if="previewImage" class="image-preview">
            <img :src="previewImage" class="preview-img" />
            <button @click="removeImage" class="remove-image-btn">×</button>
          </div>
          <!-- 输入区域 -->
          <div class="input-area">
            <!-- 清除按钮 -->
            <i class="delete-icon" @click="clearContent" aria-label="清除对话">
              <img src="@/assets/svg/clear.svg" style="width: 20px" />
              <span class="tooltip">清除对话</span>
            </i>

            <!-- 输入框容器 -->
            <div class="input-with-icons">
              <input
                type="text"
                placeholder="请输入内容..."
                class="chat-input"
                v-model="inputMessage"
                @keyup.enter="sendMessage"
                :disabled="isStreaming"
              />

              <!-- 图标按钮绝对定位在 input 内部右侧 -->
              <button
                class="icon-button upload-btn"
                title="上传图片"
                aria-label="上传图片"
                @click="triggerFileInput"
              >
                <img src="@/assets/svg/upload.svg" style="width: 20px" />
                <span class="tooltip">上传图片</span>
              </button>
              <input
                type="file"
                ref="fileInput"
                @change="handleFileUpload"
                accept="image/*"
                style="display: none"
              />
              <button
                class="icon-button voice-btn"
                title="语音输入"
                aria-label="语音输入"
              >
                <img src="@/assets/svg/voice.svg" style="width: 20px" />
                <span class="tooltip">语音输入</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 提示词库弹窗 -->
  <template-library-modal
    v-model="showPromptLibraryModal"
    :template-list="templateListData"
    :personal-template-list="personalTemplateListData"
    @select="selectTemplate"
    @insert="handleInsertTemplate"
    ref="templateLibraryModalRef"
  />

  <!-- 调用创建提示词表单 -->
  <template-modal
    v-model:model-value="showAddTemplateModal"
    :is-new="modalTemplateData.isNew"
    :template="{
      name: modalTemplateData.name,
      description: modalTemplateData.description,
      content: modalTemplateData.content,
    }"
    @save="handleSubmitTemplate"
  />
</template>

<script setup>
import {
  ref,
  onMounted,
  computed,
  reactive,
  watchEffect,
  nextTick,
  watch,
} from "vue";
import { templateList, templateAdd, promptList } from "@/api/promptTemplateApi";
import TemplateLibraryModal from "./TemplateLibraryModal.vue"; // 已保留 TemplateLibraryModal 引用
import TemplateModal from "./TemplateModal.vue";
import {modelList} from "@/api/modelApi";
import DOMPurify from "dompurify";
import { debounce } from "lodash-es";
import { useRouter } from "vue-router";
import { useChat } from "@/hooks/useChat";
import GenericContentRenderer from "@/components/GenericContentRenderer.vue";

// 引入路由
const router = useRouter();

const sanitizedContent = ref(""); // 安全处理后的内容
const selectedModel = ref(null); // 修改默认选中的模型，初始为空
let params = {
  page: "1",
  page_size: "10",
};

// 修改模型选项为从 modelList 获取
const modelListData = ref([]);
const modelOptions = computed(() => {
  return modelListData.value.map(model => ({
    label: model.model_name,
    value: model.id // 确保使用 model.id 作为 value
  }));
});

const editorEl = ref(null);
const templateLibraryModalRef = ref(null);

// 🧠 流式聊天相关状态
let eventSource = null;
const { chatMessages, inputMessage, isStreaming, sendMessage, clearContent } =
  useChat(selectedModel); 

const chatHistory = ref(null);

function updateEditorContent(content) {
  if (editorEl.value) {
    editorEl.value.innerHTML = content;
  }
}

// 防抖处理
const debouncedSanitize = debounce(() => {
  if (editorEl.value) {
    sanitizedContent.value = DOMPurify.sanitize(editorEl.value.innerHTML);
  }
}, 300);

function onTemplateContentChange(event) {
  debouncedSanitize();
}

const defaultTemplate = ref({
  content: "<p>请选择或创建模板</p>", // 添加默认内容
});

// 初始化加载默认模板内容
async function loadDefaultTemplate() {
  try {
    const res = await templateList({ page: 1, page_size: 10 });
    const targetTemplate = res.resp.find((item) => item.id === 1);

    if (targetTemplate) {
      sanitizedContent.value = DOMPurify.sanitize(targetTemplate.content);
      defaultTemplate.value = {
        content: sanitizedContent.value,
      };
      updateEditorContent(sanitizedContent.value); // 确保更新编辑器内容
    } else {
      // 如果没有找到模板，使用默认内容
      updateEditorContent(defaultTemplate.value.content);
    }
  } catch (error) {
    console.error("默认模板数据获取失败:", error);
    updateEditorContent(defaultTemplate.value.content); // 失败时也显示默认内容
  }
}

onMounted(async () => {
  await loadDefaultTemplate();
  await loadModels(); // 新增模型加载
});

const centerSections = [
  { title: "技能", items: ["插件", "工作流", "触发器"] },
  { title: "知识", items: ["文本", "表格", "照片"] },
  { title: "记忆", items: ["变量", "数据库", "长期记忆", "文件盒子"] },
  {
    title: "对话体验",
    items: [
      "开场白",
      "用户问题建议",
      "快捷指令",
      "背景图片",
      "语音",
      "用户输入方式",
    ],
  },
];

// 提交新的提示词到提示词库
const modalTemplateData = reactive({
  name: "",
  description: "",
  content: "",
  isNew: true,
});
const showAddTemplateModal = ref(false); // 创建提示词弹窗

// 打开提交到提示词库弹窗
function openSubmitTemplateModal() {
  if (editorEl.value) {
    sanitizedContent.value = DOMPurify.sanitize(editorEl.value.innerHTML);
  }

  // 确保内容不为空
  if (!sanitizedContent.value) {
    alert("请先填写提示词内容");
    return;
  }

  modalTemplateData.name = "";
  modalTemplateData.description = "";
  modalTemplateData.content = sanitizedContent.value || "";
  modalTemplateData.isNew = true;

  showAddTemplateModal.value = true;
}

async function handleSubmitTemplate() {
  try {
    const isValid = await validateTemplate();
    if (!isValid) return;

    await saveTemplate();

    // 刷新个人模板数据
    await loadUserTemplates();

    // 确保 modal 已挂载
    await nextTick();

    if (templateLibraryModalRef.value) {
      console.log("调用 refreshPersonalTemplates...");
      templateLibraryModalRef.value.refreshPersonalTemplates();
    } else {
      console.warn("templateLibraryModalRef 为空，可能未挂载");
      // 可选：延迟重试
      setTimeout(() => {
        if (templateLibraryModalRef.value) {
          templateLibraryModalRef.value.refreshPersonalTemplates();
          console.log("延迟调用 refreshPersonalTemplates 成功");
        }
      }, 300);
    }

    closeModal();
  } catch (error) {
    console.error("提交模板失败:", error);
    alert("提交失败，请重试");
  }
}

// 提示词库弹窗
const showPromptLibraryModal = ref(false); //提示词库弹窗
const templateListData = ref([]); // 推荐模板数据
const personalTemplateListData = ref([]); // 个人模板数据
const selectedTemplate = ref(null); // 选中模板

// 打开弹窗并加载数据
function openPromptLibraryModal() {
  showPromptLibraryModal.value = true;
}

// 加载推荐模板数据
async function loadTemplateData() {
  try {
    const res = await templateList({ ...params });
    templateListData.value = res.resp || [];
  } catch (error) {
    console.error("加载模板数据失败:", error);
    templateListData.value = [];
  }
}

// 加载用户个人模板
async function loadUserTemplates() {
  try {
    const res = await promptList();
    if (res.success && Array.isArray(res.resp)) {
      // 统一字段映射
      personalTemplateListData.value = res.resp.map((item) => ({
        ...item,
        name: item.template_name,
        content: item.template_content,
      }));

      // 强制 Vue 更新 DOM
      nextTick(() => {
        templateLibraryModalRef.value?.refreshPersonalTemplates();
        console.log("refreshPersonalTemplates called"); // 可以加日志确认
      });
    } else {
      personalTemplateListData.value = [];
    }
  } catch (error) {
    console.error("加载用户模板数据失败:", error);
    personalTemplateListData.value = [];
  }
}

watchEffect(() => {
  if (templateLibraryModalRef.value) {
    templateLibraryModalRef.value.personalTemplates =
      personalTemplateListData.value;
  }
});

// 选择模板时更新内容
function selectTemplate(template) {
  selectedTemplate.value = { ...template }; //记录选中模板
  updateEditorContent(template.content); // 更新编辑器内容
}

// 插入模板内容
function handleInsertTemplate(content) {
  if (editorEl.value) {
    // 获取当前编辑器内容
    const currentContent = editorEl.value.innerHTML;

    // 如果当前内容是默认内容，直接替换
    if (currentContent.includes("请选择或创建模板")) {
      editorEl.value.innerHTML = content;
    } else {
      // 否则在现有内容末尾插入
      editorEl.value.innerHTML = currentContent + content;
    }

    // 聚焦编辑器并滚动到新插入内容的起始位置
    editorEl.value.focus();

    // 强制 reflow 确保 scrollHeight 准确
    void editorEl.value.offsetHeight;

    nextTick(() => {
      // 获取插入前的内容高度
      const originalScrollHeight = currentContent
        ? editorEl.value.scrollHeight - content.length * 8
        : 0; // 近似计算

      // 滚动到新插入内容的起始位置
      editorEl.value.scrollTop = originalScrollHeight;
    });
  }
}

// 关闭弹窗
function closeModal() {
  showAddTemplateModal.value = false;
}

//提示词对比调试
function comparativeDebugging() {
  router.push({
    path: "./PromptComparison",
  });
}

// 🔽 自动滚动到底部
const scrollToBottom = () => {
  nextTick(() => {
    if (chatHistory.value) {
      chatHistory.value.scroll({
        top: chatHistory.value.scrollHeight,
        behavior: 'smooth' // 添加平滑滚动效果
      });
    }
  });
};

// 修改 watch 逻辑，确保在消息内容变化和流式消息更新时都触发滚动
watch(
  () => [...chatMessages.value], // 使用数组扩展确保深度监听
  () => {
    scrollToBottom();
  },
  { deep: true }
);

const fileInput = ref(null);
const uploadedImage = ref(null);
const previewImage = ref(null); // 新增图片预览状态

const triggerFileInput = () => {
  fileInput.value.click();
};

const handleFileUpload = (event) => {
  const target = event.target;
  if (!target || !target.files || target.files.length === 0) {
    // 没有文件被选中或事件目标不可用
    return;
  }
  
  const file = target.files[0];
  if (!file.type.startsWith('image/')) {
    // 可选：添加文件类型验证
    alert('请选择一个图片文件');
    return;
  }

  uploadedImage.value = file;
  previewImage.value = URL.createObjectURL(file); // 生成预览URL
};

const removeImage = () => {
  // 新增删除图片方法
  previewImage.value = null;
  uploadedImage.value = null;
};

const sendMessageWithImage = async () => {
  if (!inputMessage.value && !uploadedImage.value) return;

  const formData = new FormData();
  if (inputMessage.value) formData.append("message", inputMessage.value);
  if (uploadedImage.value) formData.append("image", uploadedImage.value);

  // 发送到API...
  try {
    // 调用API发送数据和图片
    const response = await fetch("/api/chat", {
      method: "POST",
      body: formData,
    });
    // 处理响应...
  } catch (error) {
    console.error("上传失败:", error);
  }

  scrollToBottom(); // 新增滚动触发
};

// 检查是否是最后一个助手消息
const isCurrentAssistantMessageIndex = (index) => {
  console.log("isStreaming.value1:", isStreaming.value);
  const lastAssistantIndex = chatMessages.value
    .map((m, i) => (m.role === "assistant" ? i : -1))
    .filter((i) => i !== -1)
    .pop();
  return index === lastAssistantIndex;
};

// 在 useChat 返回的 chatMessages 更新后同步到 DOM
watch(
  () => chatMessages.value,
  () => {
    scrollToBottom(); // 自动滚动到底部
  }
);

// 在 getMsgContent 中返回原始 msg.content（已流式更新）
const getMsgContent = (msg) => {
  return msg.content;
};

// 新增：判断是否应渲染内容（暴露给模板）
const shouldRenderContent = (msg) => {
  // 有内容时始终渲染
  if (msg.content) return true;
  // 用户消息始终渲染
  if (msg.role === 'user') return true;
  // 其他情况不渲染
  return false;
};

// 将函数暴露给模板使用
defineExpose({ shouldRenderContent });

// 新增模型加载函数
async function loadModels() {
  try {
    const res = await modelList({...params});
    modelListData.value = res.resp || [];
    // 设置第一个模型为默认选择
    if (modelListData.value.length > 0) {
      selectedModel.value = modelListData.value[0].id;
    }
  } catch (error) {
    console.error("加载模型数据失败:", error);
    modelListData.value = [];
  }
}

</script>

<style scoped>
.top-section {
  width: 66%;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

/*模型选择*/
.model-select-container {
  min-height: 34px; /* 与 select 高度一致 */
  display: flex;
  align-items: center;
}

.model-select {
  padding-right: 20px; /* 为下拉箭头留出空间 */
  padding-left: 10px;
  height: 34px;
  font-size: 14px;
  border-radius: 6px;
  border: 1px solid #ccc;
  background-color: #fff;
  width: 180px;
}

.arrow-icon {
  position: absolute;
  right: 10px;
  top: 8px;
  pointer-events: none;
  color: #888;
  font-size: 12px;
}

.panels-container {
  display: flex;
}

.page-wrapper {
  display: flex;
  height: 95vh;
  padding: 20px;
  box-sizing: border-box;
}

/* 合并中间和右侧面板的共享样式 */
.center-panel,
.left-panel {
  flex: 1;
  border: 1px solid #ccc;
  padding: 20px;
  box-sizing: border-box;
  border-radius: 15px;
}

.template-rich-text {
  line-height: 1;
  white-space: pre-wrap;
  overflow-x: auto;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  background-color: #fff;
  padding: 15px;
  border-radius: 6px;
  border: 1px solid #eee;
  max-height: calc(95vh - 182px);
}

.template-rich-text pre {
  background-color: #f8f9fa;
  padding: 15px;
  border-radius: 4px;
  margin: 10px 0;
}

ul {
  list-style-type: none;
  padding: 0;
}

li {
  margin: 5px 0;
}

.top-button-area {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
}

.top-button,
.delete-icon {
  position: relative;
  cursor: pointer;
  padding: 6px;
  display: inline-block;
  background-color: transparent; /* 去掉背景色 */
  border: none; /* 去掉边框 */
  margin-right: 10px;
}

.top-button:hover {
  background-color: #f0f0f0; /* 可选：浅灰色 hover 背景 */
  border-radius: 4px;
}

/* 鼠标悬停时显示 Tooltip */
.top-button:hover .tooltip,
.delete-icon:hover .tooltip {
  opacity: 1;
  visibility: visible;
}

.right-panel {
  flex: 1;
  min-width: 300px;
  display: flex;
  flex-direction: column;
  border: 1px solid #ccc;
  border-radius: 15px;
  padding: 20px;
  box-sizing: border-box;
}
.header-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.preview-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-bottom: 10px; /* 调整边框与内容的间距 */
}
.preview-header h3 {
  margin: 0;
  font-size: 18px;
  color: #333;
}
.preview-input-wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.input-container {
  margin-top: 20px;
}
.input-area {
  display: flex;
  align-items: center;
  margin-top: auto; /* 推到最底部 */
}

.input-with-icons {
  position: relative;
  flex: 1;
}

.chat-input {
  width: 100%;
  padding: 20px 44px 20px 12px; /* 留出图标空间 */
  border: 1px solid #ccc;
  border-radius: 30px;
  outline: none;
  font-size: 14px;
  background-color: #fff;
}

.chat-input:focus {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(103, 114, 255, 0.2);
}

.icon-button {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background-color: transparent;
  border: none;
  cursor: pointer;
  transition: transform 0.2s;
  padding: 6px;
}

.icon-button:hover .tooltip {
  opacity: 1;
  visibility: visible;
}

.tooltip {
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: #333;
  color: #fff;
  padding: 4px 8px;
  font-size: 12px;
  border-radius: 4px;
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.2s ease, visibility 0.2s ease;
  z-index: 10;
  font-family: sans-serif;
}

.upload-btn {
  right: 36px;
  color: #4a90e2;
}

.voice-btn {
  right: 8px;
  color: #67c23a;
}

.chat-history {
  flex: 1;
  padding: 15px;
  overflow-y: auto;
  border-bottom: 1px solid #eee;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  background-color: #fff;
  border: 1px solid #eee;
  border-radius: 6px;
  min-height: 0; /* 关键：允许 flex 收缩 */
}

/* 新增：用户消息样式 */
.user-message {
  background-color: #eee;
  align-self: flex-end; /* 靠右对齐 */
  text-align: right; /* 文字也靠右 */
}

.chat-message {
  margin-bottom: 16px; /* 增加消息间距 */
  line-height: 1.6;
  padding: 12px 16px;
  border-radius: 8px;
  background-color: #f5f5f5; /* 添加背景色 */
}

.chat-message h3 {
  color: #2c3e50;
  margin-top: 0;
  margin-bottom: 12px;
  font-size: 18px;
  font-weight: 600;
}

.chat-message ul {
  padding-left: 20px;
  margin: 8px 0;
}

.chat-message li {
  margin: 4px 0;
  list-style-type: disc;
}

.chat-message p {
  margin: 8px 0;
}

.chat-message .user {
  color: #333;
  background-color: #eee; /* 用户消息浅灰色背景 */
  margin-left: auto; /* 右对齐 */
  text-align: right; /* 文字右对齐 */
}

.chat-message .assistant {
  color: #2d3748;
  background-color: #f8fafc; /* 助手消息浅灰色背景 */
}

.chat-message .tip {
  color: #718096;
  font-style: italic;
}

.input-with-icons {
  right: 8px;
  color: #4a90e2;
}

.loading-indicator {
  opacity: 0.7;
}

.loading-spinner {
  display: inline-block;
  width: 16px; /* 调整为合适的大小 */
  height: 16px; /* 调整为合适的大小 */
  margin-left: 5px;
  border: 4px solid #bab8b8;
  border-top-color: transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.image-preview {
  position: relative;
  margin-bottom: 10px;
  max-width: 200px;
}

.preview-img {
  max-width: 50px; /* 设置最大宽度 */
  max-height: 50px; /* 设置最大高度 */
  border-radius: 8px;
}

.remove-image-btn {
  position: absolute;
  top: -8px;
  right: -8px;
  width: 24px;
  height: 24px;
  background: #ff4d4f;
  color: white;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>