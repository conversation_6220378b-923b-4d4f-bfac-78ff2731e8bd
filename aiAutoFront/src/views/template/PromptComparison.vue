<template>
  <div class="prompt-comparison-page">
    <!-- 主体容器 -->
    <div class="main-container">
      <!-- 左侧区域 -->
      <div class="left-container">
        <!-- Tab 菜单 -->
        <div class="top-tab-menu">
          <div
            v-for="tab in tabs"
            :key="tab.key"
            :class="['tab-item', { active: activeTab === tab.key }]"
            @click="switchTab(tab.key)"
          >
            {{ tab.label }}
          </div>
        </div>

        <!-- 框1：默认模板 -->
        <div
          class="default-template"
          :class="{ 'full-height': activeTab === 'skill' }"
        >
          <div class="top-button-area" v-if="activeTab !== 'skill'">
            <div class="button-title"><b>默认模板</b></div>
            <div class="button-group">
              <button
                class="top-button"
                @click="openSubmitTemplateModal('default')"
                aria-label="提交到提示词库"
              >
                <img src="@/assets/svg/submitPrompt.svg" style="width: 20px" />
                <span class="tooltip">提交到提示词库</span>
              </button>
              <button
                class="top-button"
                @click="openPromptLibraryModal('default')"
                aria-label="打开提示词库"
              >
                <img src="@/assets/svg/promptLibrary.svg" style="width: 20px" />
                <span class="tooltip">提示词库</span>
              </button>
              <button
                class="top-button"
                @click="handleButton3"
                aria-label="自动优化提示词"
              >
                <img
                  src="@/assets/svg/optimizePrompt.svg"
                  style="width: 20px"
                />
                <span class="tooltip">自动优化提示词</span>
              </button>
              <button class="top-button else-button" @click="handleButton4">
                选这个
              </button>
            </div>
          </div>
          <!-- 根据当前 tab 显示不同内容 -->
          <div class="template-content">
            <div v-if="activeTab === 'prompt'">
              <pre
                v-if="defaultTemplate && defaultTemplate.content"
                ref="editorEl"
                contenteditable="true"
                class="rich-text-content editable"
                @input="handleContentEdit"
              ><!--:innerHTML="defaultTemplate.content"--></pre>
            </div>
            <!-- 技能 tab 下显示技能内容 -->
            <div v-if="activeTab === 'skill'" class="skills-sections-full">
              <div class="section top-section">
                <!-- 每个 section 都循环渲染 -->
                <div v-for="(section, idx) in centerSections" :key="idx">
                  <h4>{{ section.title }}</h4>
                  <ul>
                    <li
                      v-for="(item, itemIdx) in section.items"
                      :key="itemIdx"
                      class="skill-item"
                      @click="handleSkillItemClick(section.title, item)"
                    >
                      {{ item }}
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 框2：对比模板 -->
        <div class="comparison-template" v-if="activeTab !== 'skill'">
          <!-- 修改：使用 v-if 替代 v-show -->
          <div class="top-button-area">
            <div class="button-title"><b>对比模板</b></div>
            <div class="button-group">
              <button
                class="top-button"
                @click="openSubmitTemplateModal('comparison')"
                aria-label="提交到提示词库"
              >
                <img src="@/assets/svg/submitPrompt.svg" style="width: 20px" />
                <span class="tooltip">提交到提示词库</span>
              </button>
              <button
                class="top-button"
                @click="openPromptLibraryModal('comparison')"
                aria-label="打开提示词库"
              >
                <img src="@/assets/svg/promptLibrary.svg" style="width: 20px" />
                <span class="tooltip">提示词库</span>
              </button>
              <button
                class="top-button"
                @click="handleButton3"
                aria-label="自动优化提示词"
              >
                <img
                  src="@/assets/svg/optimizePrompt.svg"
                  style="width: 20px"
                />
                <span class="tooltip">自动优化提示词</span>
              </button>
              <button class="top-button else-button" @click="handleButton4">
                选这个
              </button>
            </div>
          </div>
          <div class="template-content">
            <!-- skill tab 下不显示对比模板内容 -->
            <div v-if="activeTab !== 'skill'">
              <pre
                v-if="comparisonTemplate && comparisonTemplate.content"
                ref="editorComparisonEl"
                contenteditable="true"
                class="rich-text-content editable"
                @input="handleContentEdit"
              ></pre>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧区域 -->
      <div class="right-container">
        <!-- 右边头部 -->
        <div class="right-header">
          <h3>提示词对比调试</h3>
          <!-- 模型选择下拉框 -->
          <div class="model-select-container">
            <label for="model-select">模型选择：</label>
            <select
              id="model-select"
              v-model="selectedModel"
              class="model-select"
            >
              <option
                v-for="model in modelOptions"
                :key="model.value"
                :value="model.value"
              >
                {{ model.label }}
              </option>
            </select>
            <span class="arrow-icon">▼</span>
          </div>
        </div>

        <!-- 框3 & 框4 -->
        <div class="template-row">
          <div class="default-template-right">
            <h4>默认提示词</h4>
            <div class="template-content"></div>
          </div>
          <div class="comparison-template-right">
            <h4>对比提示词</h4>
            <div class="template-content">
              <pre v-if="comparisonPrompt">{{ comparisonPrompt.content }}</pre>
            </div>
          </div>
        </div>

        <!-- 输入区域容器 -->
        <div class="input-container">
          <div class="preview-input-wrapper">
            <!-- 输入区域 -->
            <div class="input-area">
              <!-- 清除按钮 -->
              <i
                class="delete-icon"
                @click="clearContent"
                aria-label="清除对话"
              >
                <img src="@/assets/svg/clear.svg" style="width: 20px" />
                <span class="tooltip">清除对话</span>
              </i>

              <!-- 输入框容器 -->
              <div class="input-with-icons">
                <input
                  type="file"
                  ref="fileInput"
                  @change="handleFileUpload"
                  accept="image/*"
                  style="display: none"
                />

                <input
                  type="text"
                  placeholder="请输入内容..."
                  class="chat-input"
                  aria-label="用户输入"
                  v-model="inputMessage"
                  @keyup.enter="sendMessage"
                />

                <button
                  class="icon-button upload-btn"
                  title="上传图片"
                  aria-label="上传图片"
                  @click="triggerFileInput"
                >
                  <img src="@/assets/svg/upload.svg" style="width: 20px" />
                  <span class="tooltip">上传图片</span>
                </button>
                <button
                  class="icon-button voice-btn"
                  title="语音输入"
                  aria-label="语音输入"
                >
                  <img src="@/assets/svg/voice.svg" style="width: 20px" />
                  <span class="tooltip">语音输入</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!--调用提示词库弹窗-->
  <template-library-modal
    v-model="showPromptLibraryModal"
    @select="selectTemplate"
    ref="templateLibraryModalRef"
  />
  <!--调用创建提示词弹窗-->
  <template-modal
    v-model:model-value="showSubmitTemplateModal"
    :is-new="modalTemplateData.isNew"
    :template="{
      name: modalTemplateData.name,
      description: modalTemplateData.description,
      content: modalTemplateData.content,
    }"
    @save="handleSubmitTemplate"
  />
</template>

<script setup>
import { ref, onMounted, reactive, watch, watchEffect, nextTick } from "vue";
import { templateList, templateAdd, promptList } from "@/api/promptTemplateApi";
import TemplateLibraryModal from "./TemplateLibraryModal.vue";
import TemplateModal from "./TemplateModal.vue";
import DOMPurify from "dompurify";
import { debounce } from "lodash-es";
import { useRoute } from "vue-router";
const route = useRoute();

const savedComparisonContent = ref(""); // 用于保存对比模板内容

let params = {
  page: "1",
  page_size: "10",
};
const comparisonTemplate = ref(null);
const selectedTemplate = ref(null);

const tabs = [
  { key: "prompt", label: "提示词" },
  { key: "skill", label: "技能" },
];
const centerSections = [
  { title: "技能", items: ["插件", "工作流", "触发器"] },
  { title: "知识", items: ["文本", "表格", "照片"] },
  { title: "记忆", items: ["变量", "数据库", "长期记忆", "文件盒子"] },
  {
    title: "对话体验",
    items: [
      "开场白",
      "用户问题建议",
      "快捷指令",
      "背景图片",
      "语音",
      "用户输入方式",
    ],
  },
];
const selectedModel = ref("gpt-3.5"); // 默认选中的模型
const modelOptions = [
  { label: "GPT-3.5", value: "gpt-3.5" },
  { label: "GPT-4", value: "gpt-4" },
  { label: "Claude 3", value: "claude-3" },
  { label: "Qwen", value: "qwen" },
];
// ===== 响应式状态 =====
const showPromptLibraryModal = ref(false); // 控制提示词库弹窗
const showSubmitTemplateModal = ref(false); // 控制创建提示词弹窗
const activeTab = ref("prompt"); // 菜单Tab 切换
const defaultTemplate = ref({
  content: "<p>请选择或创建模板</p>", // 添加默认内容
});
const originalDefaultContent = ref(null); // ✅ 已有：缓存原始默认模板内容
const originalComparisonContent = ref(null); // ✅ 新增：缓存原始对比模板内容
const sanitizedContent = ref(""); // 安全处理后的内容
const editorEl = ref(null);
const editorComparisonEl = ref(null);
const templateLibraryModalRef = ref(null);
const modalTemplateData = reactive({
  name: "",
  description: "",
  content: "",
  isNew: true,
});
let lastSelection = null;
let lastContent = ""; // 用于缓存上一次内容

// onMounted(async () => {
//   if (modalTemplateData) {
//     sanitizedContent.value = DOMPurify.sanitize(modalTemplateData.content);
//   }
// });

// 同步 props.template 到 currentTemplate，并设置默认值
watchEffect(() => {
  // 使用副本防止污染原始数据
  const templateCopy = { ...modalTemplateData };

  modalTemplateData.name = templateCopy.name || "";
  modalTemplateData.description = templateCopy.description || "";
  modalTemplateData.content = templateCopy.content || "<p><br></p>"; // 设置默认 HTML 内容

  //   // 如果是编辑模式，设置初始内容并聚焦
  //   if (editorEl.value && modalTemplateData.content) {
  //     editorEl.value.innerHTML = templateCopy.content;
  //   }
});

function switchTab(tabKey) {
  activeTab.value = tabKey;

  // 如果切换回“提示词”标签页，则恢复默认模板和对比模板内容
  if (tabKey === "prompt") {
    nextTick(() => {
      // 恢复默认模板内容
      if (
        defaultTemplate.value &&
        editorEl.value &&
        originalDefaultContent.value
      ) {
        editorEl.value.innerHTML = originalDefaultContent.value;
      }

      // 恢复对比模板内容 ✅ 新增部分
      if (
        comparisonTemplate.value &&
        editorComparisonEl.value &&
        originalComparisonContent.value
      ) {
        editorComparisonEl.value.innerHTML = originalComparisonContent.value;
      }
    });
  }
}

// 将防抖函数移到顶层，确保只创建一次实例
const debouncedSanitize = debounce(() => {
  if (editorEl.value) {
    const rawHTML = editorEl.value.innerHTML;
    if (rawHTML !== lastContent) {
      sanitizedContent.value = DOMPurify.sanitize(rawHTML);
      defaultTemplate.value.content = sanitizedContent.value;
      lastContent = sanitizedContent.value;

      // 确保DOM更新完成后再恢复光标
      nextTick(() => {
        if (editorEl.value && document.contains(editorEl.value)) {
          restoreSelection();
        }
      });
    }
  }
}, 500);

/*默认提示词-编辑提示词模版*/
function handleContentEdit(event) {
  sanitizedContent.value = defaultTemplate.value.content;
  saveSelection(); // 输入时保存光标
  debouncedSanitize(); // 调用防抖函数
}

// 保存光标位置
function saveSelection() {
  const selection = window.getSelection();
  if (selection.rangeCount > 0) {
    const range = selection.getRangeAt(0);
    // 检查选区是否在编辑器内
    if (
      editorEl.value &&
      editorEl.value.contains(range.commonAncestorContainer)
    ) {
      lastSelection = range;
    }
  }
}

// 恢复光标位置
function restoreSelection() {
  if (lastSelection && editorEl.value && document.contains(editorEl.value)) {
    const selection = window.getSelection();
    selection.removeAllRanges();
    try {
      // 检查选区节点是否仍然在DOM中
      if (document.contains(lastSelection.commonAncestorContainer)) {
        selection.addRange(lastSelection);
        editorEl.value.focus();
      }
    } catch (e) {
      console.warn("恢复光标失败:", e);
    }
  }
}

/*提交到提示词库按钮*/
async function openSubmitTemplateModal(templateType) {
  // 设置当前操作的模板类型
  activeOperatedTemplate.value = templateType;
  console.log("当前操作的模板类型:", templateType);

  let content;
  // 仅从对应编辑器获取内容用于提交，不修改现有模板对象
  if (templateType === "comparison") {
    if (editorComparisonEl.value && editorComparisonEl.value.innerHTML) {
      content = DOMPurify.sanitize(editorComparisonEl.value.innerHTML);
    }
  } else {
    if (editorEl.value && editorEl.value.innerHTML) {
      content = DOMPurify.sanitize(editorEl.value.innerHTML);
    }
  }

  // 仅设置 modalTemplateData 用于弹窗提交展示，不会影响原始 defaultTemplate
  modalTemplateData.content = content || "";
  modalTemplateData.name = "";
  modalTemplateData.description = "";
  modalTemplateData.isNew = true;

  showSubmitTemplateModal.value = true;
}

// 提交处理
function handleSubmitTemplate(template) {
  // 提交成功后通知 TemplateLibraryModal 刷新
  templateLibraryModalRef.value?.refreshPersonalTemplates();
}

const activeOperatedTemplate = ref("comparison"); // 标记当前操作的模板类型
let templateType = ref(""); // ✅ 使用 ref 明确声明
let decodedContent = ref(""); //获取解码后的模板内容

function insertPromptToEditor(content, editorElement) {
  if (editorElement.value) {
    // 获取当前编辑器内容
    const currentContent = editorElement.value.innerHTML;
    let newContent;

    if (currentContent) {
      // 在现有内容末尾插入新内容
      newContent = currentContent + content;
    } else {
      newContent = content;
    }

    editorElement.value.innerHTML = newContent;

    // 聚焦编辑器
    editorElement.value.focus();

    // 使用 nextTick 确保 DOM 更新后再滚动
    nextTick(() => {
      // 确保元素存在且可滚动
      if (editorElement.value && editorElement.value.scrollHeight) {
        // 添加延迟确保DOM完全更新
        setTimeout(() => {
          editorElement.value.scrollTo({
            top: editorElement.value.scrollHeight,
            behavior: 'smooth'
          });
          console.log('实际滚动位置:', editorElement.value.scrollTop, '总高度:', editorElement.value.scrollHeight);
        }, 50);
      }
    });
  }
}

// 修改 selectTemplate 方法
function selectTemplate(template) {
  if (!template) return;

  // 根据当前操作的模板类型更新对应模板
  if (activeOperatedTemplate.value === "comparison") {
    comparisonTemplate.value = { ...template };
    nextTick(() => {
      if (editorComparisonEl.value) {
        editorComparisonEl.value.innerHTML = template.content;
      }
    });
  } else {
    // ✅ 仅在 activeOperatedTemplate 是 'default' 时才更新默认模板
    defaultTemplate.value = { ...template };
    nextTick(() => {
      if (editorEl.value) {
        editorEl.value.innerHTML = template.content;
        editorEl.value.dispatchEvent(new Event("input"));
      }
    });
  }
}

//打开提示词库
function openPromptLibraryModal(templateType) {
  // 确保传入的templateType被正确设置
  activeOperatedTemplate.value = templateType;
  showPromptLibraryModal.value = true;
}

// 修改onMounted生命周期，确保内容显示
onMounted(async () => {
  await loadDefaultTemplate();
  activeTab.value = "prompt";

  // 缓存原始内容
  if (defaultTemplate.value && defaultTemplate.value.content) {
    originalDefaultContent.value = DOMPurify.sanitize(
      defaultTemplate.value.content
    );
  }

  // ✅ 新增：缓存原始对比模板内容
  if (comparisonTemplate.value && comparisonTemplate.value.content) {
    originalComparisonContent.value = DOMPurify.sanitize(
      comparisonTemplate.value.content
    );
  }
});

watch(
  () => route.query,
  (newQuery) => {
    if (
      newQuery.comparisonContent &&
      (newQuery.replaceComparison || newQuery.insertComparison)
    ) {
      const decodedContent = decodeURIComponent(newQuery.comparisonContent);
      templateType = newQuery.templateType;
      const template = {
        content: decodedContent,
        id: Date.now() + 1,
        name: "对比模板",
      };
      if (templateType) {
        templateType = activeOperatedTemplate.value;
      } else {
        templateType = "comparison";
      }
      // 默认情况下作为对比调试插入
      if (
        newQuery.insertComparison &&
        activeOperatedTemplate.value === "comparison"
      ) {
        insertPromptToEditor(template.content, editorComparisonEl);
      } else if (
        newQuery.insertComparison &&
        activeOperatedTemplate.value === "default"
      ) {
        insertPromptToEditor(template.content, editorEl);
      } else {
        selectTemplate(template);
      }
    }
  },
  { immediate: true }
);

// 初始化加载默认模板内容
async function loadDefaultTemplate() {
  try {
    const res = await templateList({ ...params });
    const targetTemplate = res.resp.find((item) => item.id === 1);

    if (targetTemplate) {
      sanitizedContent.value = DOMPurify.sanitize(targetTemplate.content);
      defaultTemplate.value = {
        content: sanitizedContent.value,
      };
      updateEditorContent(sanitizedContent.value); // 确保更新编辑器内容
    } else {
      // 如果没有找到模板，使用默认内容
      updateEditorContent(defaultTemplate.value.content);
    }
  } catch (error) {
    console.error("默认模板数据获取失败:", error);
    updateEditorContent(defaultTemplate.value.content); // 失败时也显示默认内容
  }
}

function updateEditorContent(content) {
  if (editorEl.value) {
    editorEl.value.innerHTML = content;
  }
}

function handleSkillItemClick(sectionTitle, itemName) {
  console.log(`点击了【${sectionTitle}】下的【${itemName}】`);
  // 这里可以自定义逻辑，比如打开弹窗、加载数据等
}

function handleButton3() {
  console.log("Button 3 clicked");
}

// 清除对话
function clearContent() {
  console.log("Clear content");
}

const fileInput = ref(null);
const inputMessage = ref("");
const uploadedImage = ref(null);

const triggerFileInput = () => {
  fileInput.value.click();
};

const handleFileUpload = (event) => {
  const file = event.target.files[0];
  if (file) {
    uploadedImage.value = file;
  }
};

const sendMessage = async () => {
  if (!inputMessage.value && !uploadedImage.value) return;

  const formData = new FormData();
  if (inputMessage.value) formData.append("message", inputMessage.value);
  if (uploadedImage.value) formData.append("image", uploadedImage.value);

  // 发送到API...
};
</script>

<style scoped>
.top-tab-menu {
  display: flex;
  padding: 10px 20px;
  background-color: #f7f9fc;
  border-bottom: 1px solid #e4e8f0;
  font-size: 14px;
}

.tab-item {
  margin-right: 20px;
  padding: 6px 12px;
  cursor: pointer;
  color: #555;
  border-bottom: 2px solid transparent;
  transition: all 0.2s ease;
}

.tab-item:hover,
.tab-item.active {
  color: #007aff;
  border-bottom-color: #007aff;
  font-weight: bold;
}

.prompt-comparison-page {
  display: flex;
  padding: 20px;
  height: 100vh; /* 确保页面高度为视口高度 */
}

.main-container {
  display: flex;
  flex: 1;
  overflow: hidden;
}

/* 左边容器 */
.left-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  margin-right: 10px;
  border: 1px solid #ccc;
  border-radius: 8px;
  overflow: hidden; /* 防止内容溢出 */
}

/* 右边容器 */
.right-container {
  flex: 2;
  display: flex;
  flex-direction: column;
  border: 1px solid #ccc;
  border-radius: 8px;
  overflow: hidden;
  padding: 0px 20px 0px 20px; /* 顺时针：上右下左 */
  background-color: #f8f9fa;
  height: 100%; /* 关键 */
}

/* 右边头部 */
.right-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
}

/*模型选择*/
.model-select-container {
  min-height: 34px; /* 与 select 高度一致 */
  display: flex;
  align-items: center;
}

.model-select {
  padding-right: 20px; /* 为下拉箭头留出空间 */
  padding-left: 10px;
  height: 34px;
  font-size: 14px;
  border-radius: 6px;
  border: 1px solid #ccc;
  background-color: #fff;
  width: 180px;
}

.arrow-icon {
  position: absolute;
  right: 10px;
  top: 8px;
  pointer-events: none;
  color: #888;
  font-size: 12px;
}

.default-template,
.comparison-template {
  flex: 1;
  padding: 10px 0 20px 20px;
  border-bottom: 1px solid #ccc;
  display: flex;
  flex-direction: column;
  max-height: 900px; /* 固定最大高度 */
  min-height: 0; /* 允许内部 flex 子项滚动*/
}

/*提示词模板内容*/
.rich-text-content {
  flex: 1;
  white-space: pre-wrap;
  line-height: 1;
  font-size: 14px;
  background-color: #fff;
  border-radius: 4px;
  overflow-y: auto; /* 纵向滚动 */
}

.rich-text-content p {
  margin: 10px 0 10px;
}

.rich-text-content strong,
.rich-text-content b {
  font-weight: bold;
}

.rich-text-content em,
.rich-text-content i {
  font-style: italic;
}

.rich-text-content span {
  font-family: inherit;
}

.rich-text-content ul,
.rich-text-content ol {
  padding-left: 20px;
  margin: 5px 0;
}

.rich-text-content li {
  margin: 4px 0;
}

.rich-text-content code {
  background-color: #f0f0f0;
  padding: 2px 4px;
  border-radius: 3px;
}

.rich-text-content[contenteditable="true"]:focus {
  outline: none;
  border: none;
}

.comparison-template {
  border-bottom: none; /* 移除左下框的底部边框 */
}

/* 框3 & 框4 的布局 */
.template-row {
  display: flex;
  gap: 10px;
  flex: 1;
  overflow: hidden;
  height: 100%; /* 关键 */
}

.default-template-right,
.comparison-template-right {
  flex: 1;
  background-color: #fff;
  border-radius: 6px;
  padding: 0px 0px 10px 20px;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.default-template-right {
  border-right: none;
}

.template-content {
  flex: 1;
  overflow-y: auto; /* 内容过长时可滚动 */
}

.default-template.full-height {
  height: 100%;
}

.skills-sections-full {
  height: 100%;
  overflow-y: auto;
}

.skills-sections-full .section {
  padding-bottom: 10px;
  border-bottom: 1px solid #e4e8f0;
}

.skills-sections-full .section:last-child {
  border-bottom: none;
}

/* 输入框在右边最下方 */
.preview-input-wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.delete-icon {
  position: relative;
  cursor: pointer;
  padding: 6px;
  display: inline-block;
  background-color: transparent; /* 去掉背景色 */
  border: none; /* 去掉边框 */
}

.input-container {
  display: flex;
  flex-direction: column;
  justify-content: flex-start; /* 确保内容从顶部开始 */
  flex-shrink: 0;
  margin-top: 20px;
  margin-bottom: 20px;
}
.input-area {
  display: flex;
  align-items: center;
  margin-top: auto; /* 推到最底部 */
}

.input-with-icons {
  position: relative;
  flex: 1;
}

.chat-input {
  width: 100%;
  padding: 20px 44px 20px 12px; /* 留出图标空间 */
  border: 1px solid #ccc;
  border-radius: 30px;
  outline: none;
  font-size: 14px;
  background-color: #fff;
}

.chat-input:focus {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(103, 114, 255, 0.2);
}

.icon-button {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background-color: transparent;
  border: none;
  cursor: pointer;
  transition: transform 0.2s;
  padding: 6px;
}

.icon-button:hover .tooltip {
  opacity: 1;
  visibility: visible;
}

.tooltip {
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: #333;
  color: #fff;
  padding: 4px 8px;
  font-size: 12px;
  border-radius: 4px;
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.2s ease, visibility 0.2s ease;
  z-index: 10;
  font-family: sans-serif;
}

.upload-btn {
  right: 36px;
  color: #4a90e2;
}

.voice-btn {
  right: 8px;
  color: #67c23a;
}

/* Button Group */

.button-group {
  display: flex;
  padding: 10px 20px 10px 20px;
}

.top-button-area {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.top-button {
  padding: 6px 10px;
  border: none;
  cursor: pointer;
  border-radius: 4px;
  background-color: transparent; /* 去掉背景色 */
}

.else-button {
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #4a90e2, #3d7bd6);
  color: white;
  border: none;
  padding: 8px 8px;
  cursor: pointer;
  font-size: 14px;
  min-width: 80px; /* 防止文本溢出 */
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
  margin-left: 10px;
}

.top-button:hover {
  background-color: #f0f0f0; /* 可选：浅灰色 hover 背景 */
  border-radius: 4px;
}
.top-button:hover .tooltip,
.delete-icon:hover .tooltip {
  opacity: 1;
  visibility: visible;
}

/* 添加或修改 .preview-img 的样式 */
.preview-img {
  max-width: 200px; /* 设置最大宽度 */
  max-height: 150px; /* 设置最大高度 */
  border-radius: 8px;
}
</style>
