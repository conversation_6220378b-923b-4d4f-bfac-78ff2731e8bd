<!-- TemplateModal.vue -->
<template>
  <div class="template-modal-overlay" v-if="showModal">
    <div class="modal-content">
      <div class="modal-header">
        <h3>创建提示词</h3>
        <button @click="closeModal">×</button>
      </div>
      <div class="modal-body">
        <span><b>提示词名称</b></span>
        <input v-model="currentTemplate.name" placeholder="请输入提示词名称" />
        <span><b>提示词描述</b></span>
        <input
          v-model="currentTemplate.description"
          placeholder="请输入提示词描述"
        />
        <div
          style="
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
          "
        >
          <h4 style="margin: 0">提示词</h4>
          <button
            class="optimize-button"
            @click="handleButton4"
            aria-label="自动优化提示词"
          >
            <img
              src="@/assets/svg/optimizePrompt.svg"
              style="width: 16px; vertical-align: middle; margin-right: 4px"
            />
            优化
            <span class="tooltip">自动优化提示词</span>
          </button>
        </div>
        <div
          ref="editorEl"
          contenteditable="true"
          @input="onContentInput"
          class="content-editor"
        ></div>
      </div>
      <div class="modal-footer">
        <button @click="closeModal">取消</button>
        <button @click="saveTemplate">保存</button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { templateAdd, templateList, promptList } from "@/api/promptTemplateApi";
import DOMPurify from "dompurify";
import { debounce } from "lodash-es";
import {
  nextTick,
  defineProps,
  defineEmits,
  ref,
  watch,
  watchEffect,
  onMounted,
} from "vue";

const props = defineProps({
  modelValue: Boolean,
  template: {
    type: Object,
    default: () => ({ name: "", description: "", content: "" }),
  },
  isNew: Boolean,
});

const emit = defineEmits(["update:modelValue", "saved"]);
const editorEl = ref(null);
const sanitizedContent = ref("");
// 当前模板数据
const currentTemplate = ref({ ...props.template });
// 弹窗显示状态
const showModal = ref(props.modelValue);
let lastSelection = null;
const personalTemplateListData = ref("");
let lastContent = ""; // 用于缓存上一次内容

onMounted(() => {
  if (editorEl.value && props.template.content) {
    editorEl.value.innerHTML = props.template.content;
    editorEl.value.focus();
  }
});

watch(
  () => props.modelValue,
  (newVal) => {
    showModal.value = newVal;
  }
);
// // 加载用户个人模板
// async function loadUserTemplates() {
//   try {
//     const res = await promptList();
//     if (res.success && Array.isArray(res.resp)) {
//       // 统一字段映射
//       personalTemplateListData.value = res.resp.map((item) => ({
//         ...item,
//         name: item.template_name,
//         content: item.template_content,
//       }));
//     } else {
//       personalTemplateListData.value = [];
//     }
//   } catch (error) {
//     console.error("加载用户模板数据失败:", error);
//     personalTemplateListData.value = [];
//   }
// }
// 保存光标位置
function saveSelection() {
  const selection = window.getSelection();
  if (selection.rangeCount > 0) {
    lastSelection = selection.getRangeAt(0);
  }
}
// 恢复光标位置
function restoreSelection() {
  if (lastSelection && editorEl.value) {
    const selection = window.getSelection();
    selection.removeAllRanges();
    try {
      selection.addRange(lastSelection);
    } catch (e) {
      console.warn("无法恢复光标");
    }
    editorEl.value.focus(); // 强制聚焦
  }
}

// 只在组件挂载时初始化一次内容
onMounted(() => {
  if (editorEl.value) {
    editorEl.value.innerHTML = props.template.content || "<p><br></p>";
    editorEl.value.focus();
  }
});

// 外部传入模板内容时，仅当当前为空时设置
watch(
  () => props.template.content,
  (newVal) => {
    if (editorEl.value && newVal && !editorEl.value.innerHTML.trim()) {
      editorEl.value.innerHTML = newVal;
    }
  }
);

// 同步模板数据到本地状态
watch(
  () => props.template,
  (newVal) => {
    currentTemplate.value = { ...newVal };
  },
  { deep: true, immediate: true }
);

// 同步 props.template 到 currentTemplate，并设置默认值
watchEffect(() => {
  currentTemplate.value = {
    name: props.template.name || "",
    description: props.template.description || "",
    content: props.template.content || "", // 设置默认 HTML 内容
    //isNew: props.isNew
  };
  // 如果是编辑模式，设置初始内容并聚焦
  if (editorEl.value && props.template.content) {
    editorEl.value.innerHTML = props.template.content;
  }
});

// function initEditorContent() {
//   if (editorEl.value) {
//     const newContent = props.template.content || "<p><br></p>";
//     editorEl.value.innerHTML = newContent;
//     lastContent = newContent;
//     editorEl.value.focus();
//   }
// }

// 防抖处理
const debouncedSanitize = debounce(() => {
  if (editorEl.value) {
    const rawHTML = editorEl.value.innerHTML;

    // ✅ 只有内容变化时才更新
    if (rawHTML !== lastContent) {
      sanitizedContent.value = DOMPurify.sanitize(rawHTML);
      currentTemplate.value.content = sanitizedContent.value;
      lastContent = sanitizedContent.value;
      restoreSelection(); // 恢复光标
    }
  }
}, 300);

function onContentInput(e) {
  saveSelection(); // 输入时保存光标
  debouncedSanitize();
}

async function saveTemplate() {
  const payload = {
    template_name: currentTemplate.value.name.trim(),
    template_content: currentTemplate.value.content.trim(),
    description: currentTemplate.value.description.trim(),
  };
  // ✅ 增加非空校验
  if (!payload.template_name || !payload.template_content) {
    alert("请填写提示词名称和内容");
    return;
  }
  try {
    const res = await templateAdd(payload);
    if (res && res.resp.id) {
      alert("模板提交成功");
      emit("saved"); //触发事件
      closeModal();
    }
  } catch (error) {
    console.error("提交失败:", error);
    alert("提交失败，请重试");
  } finally {
    closeModal();
  }
}

function closeModal() {
  emit("update:modelValue", false);
}
</script>

<style scoped>
.template-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10000;
  outline: 5px solid yellow;
}

.modal-content {
  background-color: #fff;
  width: 700px;
  border-radius: 10px;
  overflow: hidden;
}

.modal-header {
  padding: 15px 20px;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h3 {
  margin: 0;
  font-size: 18px;
}

.modal-header button {
  background: transparent;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #999;
  transition: color 0.2s ease;
}
.modal-body {
  padding: 20px;
}

.modal-body input {
  display: block;
  width: 100%;
  margin-bottom: 30px;
  margin-top: 8px;
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
}
.optimize-button {
  display: inline-flex;
  align-items: center;
  padding: 6px 6px;
  background-color: #f0f8ff;
  border: 1px solid #4a90e2;
  color: #4a90e2;
  font-size: 14px;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.optimize-button:hover {
  background-color: #e0f7ff;
  color: #3d7bd6;
  border-color: #3d7bd6;
}
.optimize-button:hover .tooltip {
  opacity: 1;
  visibility: visible;
}
.optimize-button .tooltip {
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: #333;
  color: #fff;
  padding: 4px 8px;
  font-size: 12px;
  border-radius: 4px;
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.2s ease, visibility 0.2s ease;
  z-index: 10;
  font-family: sans-serif;
}
.content-editor {
  min-height: 300px;
  max-height: 400px;
  overflow-y: auto; /* 超出后滚动 */
  border: 1px solid #ccc;
  padding: 10px;
  margin-top: 10px;
  white-space: pre-wrap;
  word-break: break-word;
  background-color: #fff;
  outline: none;
  border-radius: 4px;
}
.content-editor:empty::before {
  content: "请输入提示词";
  color: #aaa;
  font-style: inherit;
}
.modal-footer {
  padding: 15px 20px;
  background-color: #f8f8f8;
  border-top: 1px solid #eee;
  text-align: right;
}

.modal-footer button {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  margin-left: 10px;
}

.modal-footer .cancel-button {
  background-color: #eaeaea;
  color: #333;
}

.modal-footer .cancel-button:hover {
  background-color: #dcdcdc;
}

.modal-footer .save-button {
  background-color: #4a90e2;
  color: white;
}

.modal-footer .save-button:hover {
  background-color: #3d7bd6;
}
</style>
