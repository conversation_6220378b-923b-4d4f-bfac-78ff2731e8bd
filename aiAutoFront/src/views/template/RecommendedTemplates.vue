<template>
  <div class="template-recommended-container">
    <!-- 左侧模板列表 -->
    <div class="template-list-wrapper">
      <ul class="template-list">
        <li
          v-for="(item, index) in filteredTemplates"
          :key="index"
          class="template-item"
          :class="{
            active: selectedTemplate && selectedTemplate.id === item.id,
          }"
          @click="selectTemplate(item)"
        >
          {{ item.name || "未命名模板" }}
        </li>
      </ul>
    </div>

    <!-- 右侧模板详情 -->
    <div class="template-detail-container">
      <div v-if="selectedTemplate" class="template-detail">
        <h4>{{ selectedTemplate.template_name || selectedTemplate.name }}</h4>
        <div
          v-html="
            DOMPurify.sanitize(
              selectedTemplate.template_content ||
                selectedTemplate.content ||
                ''
            )
          "
        ></div>
      </div>
      <div v-else class="no-template-selected">请选择一个模板查看详情</div>
    </div>
  </div>
</template>

<script setup>
import { defineProps, defineEmits, computed } from "vue";
import DOMPurify from "dompurify";

const props = defineProps({
  templates: {
    type: Array,
    required: true,
    default: () => [],
  },
  selectedTemplate: {
    type: Object,
    default: null,
  },
  searchQuery: {
    type: String,
    default: "",
  },
});

const emit = defineEmits(["select"]);

// 过滤后的模板列表
const filteredTemplates = computed(() => {
  if (!props.searchQuery) return props.templates;

  const query = props.searchQuery.toLowerCase();
  return props.templates.filter(
    (item) =>
      (item.name && item.name.toLowerCase().includes(query)) ||
      (item.content && item.content.toLowerCase().includes(query))
  );
});

// 选择模板
function selectTemplate(template) {
  emit("select", template);
}
</script>

<style scoped>
.template-recommended-container {
  display: flex;
  height: 600px;
  border: 1px solid #ddd;
  border-radius: 8px;
  overflow: hidden;
  background-color: #fbfbfb;
}

.template-list-wrapper {
  width: 30%;
  max-height: 100%;
  overflow-y: auto;
  padding: 10px;
  list-style-type: none;
  margin: 0;
  background-color: #fff;
  border-right: 1px solid #eee;
}

.template-list {
  padding: 0;
  margin: 0;
}

.template-item {
  padding: 12px 15px;
  cursor: pointer;
  font-size: 14px;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.2s ease;
}

.template-item:hover {
  background-color: #f5f5f5;
}

.template-item.active {
  background-color: #e6f7ff;
  color: #1890ff;
  font-weight: bold;
  border-left: 4px solid #1890ff;
}

.template-item:last-child {
  border-bottom: none;
}

.template-detail-container {
  width: 70%;
  padding: 15px;
  overflow-y: auto;
}

.template-detail {
  background-color: #ffffff;
  padding: 15px;
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  height: 100%;
  overflow-y: auto;
}

.template-detail h4 {
  margin-top: 0;
  font-size: 16px;
  color: #333;
}

.no-template-selected {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #aaa;
  font-style: italic;
}
</style>
