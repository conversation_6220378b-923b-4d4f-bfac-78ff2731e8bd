<template>
  <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
    <el-tab-pane label="RAG报告" name="first" lazy style="display: block;">
    <!-- 第一部分：查询区域 -->
  <el-row class="section" :gutter="20">
    <el-col :span="24">

      <!-- <el-card class="query-section"> -->
        <el-select filterable v-model="store.project_list_id" placeholder="请选择项目" style="width: 10%;" >
              <el-option v-for="item in store.project_list" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
        <el-button type="primary" @click="handleRagQuery" style="margin-left: 1%;">查询</el-button>
         <el-button plain @click="resetRagQuery">重置</el-button>

      <!-- </el-card> -->
    </el-col>
  </el-row>

  <!-- 第二部分：项目卡片 + 饼图 -->
  <el-row class="section" :gutter="20">
    <el-col :span="16">
        <el-card class="box-card">
    <template #header>
      <div class="card-header">
        <span>{{ store.rag_collect.project_name }}</span>
      </div>
    </template>
      <!-- 第一行4个卡片 -->
      <el-row :gutter="16" style="margin-bottom: 16px;" >
          <el-col :span="6" v-for="i in store.rag_collect_data" :key="'top'+i">
            <el-card class="small-card" shadow="never">
              <div class="card-text">{{i.label}}</div>
              <div class="card-number">{{i.value}}</div>
            </el-card>
          </el-col>
        </el-row>


  </el-card>


    </el-col>

    <!-- 右侧饼图（调小宽度：原16列→14列） -->
    <el-col :span="8">
      <div id="projectPie" class="pie-container"></div>
    </el-col>
  </el-row>

  <el-card class="comparison-query-card" shadow="hover" style="width: 91%;margin-top: 1%;margin-left: 1%;">
        <template #header>
          <div class="card-header">
            <h3 class="section-title">RAG指标查询</h3>
          </div>
        </template>

        <!-- 查询控制区域 -->
        <el-row :gutter="20" class="comparison-query-row">
          <!-- 执行次数对比 -->
          <el-col :span="8">
            <div class="query-group">
              <label class="query-group-label">用例集</label>
               <el-cascader
          v-model="store.ragCheckOPtionsValue"
          :options="store.ragCheckOPtions"
          :props="store.ragprops"

          clearable
          placeholder="请选择项目/需求/用例集"
          style="width: 100%; max-width: 350px;"
          size="default"
        />

            </div>
          </el-col>

          <!-- 测试套件选择 -->
          <el-col :span="8">
            <div class="query-group">
              <label class="query-group-label">版本号</label>
             <el-input
          v-model="store.input_version"
          placeholder="请输入版本号"
          size="default"
          clearable
          style="width: 50%;"
        />
            </div>
          </el-col>

          <!-- 操作按钮 -->
          <el-col :span="8">
            <div class="query-group">
              <label class="query-group-label">操作</label>
              <div class="action-buttons">
                <el-button
                  type="primary"
                  @click="store.handleRagCheckChange"
                  size="default"
                  style="margin-right: 12px;"
                  :icon="Search"
                >
                  查询
                </el-button>

              </div>
            </div>
          </el-col>
        </el-row>
      </el-card>


  <!-- 第三部分：折线图 + 饼图 -->
  <el-row class="section" :gutter="20">

    <!-- 左侧折线图 -->
    <el-col :span="16">
      <div id="trendChart11" class="chart-container" style="height: 400px;"></div>
    </el-col>

    <!-- 右侧饼图 -->
    <el-col :span="8">
      <el-select
          v-model="store.selectedAnalysisMetric"
          placeholder="选择分析指标"
          style="width: 80%; margin-bottom: 10px;"
          @change="store.handleAnalysisMetricChange()"
        >
          <el-option
            v-for="metric in store.analysisMetrics"
            :key="metric.value"
            :label="metric.label"
            :value="metric.value"
          ></el-option>
        </el-select>
      <div id="analysisPie" class="analysis_pie">

      </div>
    </el-col>
  </el-row>

  <!-- 第四部分：对比结果列表 -->
  <el-row class="section" :gutter="20">
    <el-col :span="24">
      <el-card class="comparison-query-card" shadow="hover" style="width: 93.5%;">
        <template #header>
          <div class="card-header">
            <h3 class="section-title">RAG对比结果分析</h3>
            <el-tag type="info" effect="light">执行结果对比查询</el-tag>
          </div>
        </template>

        <!-- 查询控制区域 -->
        <el-row :gutter="20" class="comparison-query-row">
          <!-- 执行次数对比 -->
          <el-col :span="8">
            <div class="query-group">
              <label class="query-group-label">执行次数对比</label>
              <div class="execution-times-controls">

                  <span class="input-label">前</span>
                  <el-input
                    v-model="store.execution_times_before"
                    placeholder="次数"
                    size="default"
                    style="width: 90px;"
                    type="number"
                    :min="1"
                  />

                <span class="separator">对比</span>

                  <span class="input-label">后</span>
                  <el-input
                    v-model="store.execution_times_after"
                    placeholder="次数"
                    size="default"
                    style="width: 90px;"
                    type="number"
                    :min="1"
                  />
              </div>
            </div>
          </el-col>

          <!-- 测试套件选择 -->
          <el-col :span="8">
            <div class="query-group">
              <label class="query-group-label">测试套件</label>
              <el-select
                v-model="store.suiteValue"
                @focus="store.getSuiteInProject()"
                placeholder="请选择测试套件"
                size="default"
                style="width: 50%;"
                clearable
              >
                <el-option
                  v-for="item in store.suite_list"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </div>
          </el-col>

          <!-- 操作按钮 -->
          <el-col :span="8">
            <div class="query-group">
              <label class="query-group-label">操作</label>
              <div class="action-buttons">
                <el-button
                  type="primary"
                  @click="store.getRagCompareResult('list')"
                  size="default"
                  style="margin-right: 12px;"
                  :icon="Search"
                >
                  查询
                </el-button>

              </div>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 对比结果列表 -->
      <div v-if="store.compare_result_data && store.compare_result_data.length > 0" class="comparison-list">
        <el-card v-for="(item, index) in store.compare_result_data" :key="index" class="comparison-item mb-4">
          <template #header>
            <div class="card-header">
              <!-- <h4>对比项 {{ index + 1 }}</h4> -->
              <el-tag type="info" size="small">Suite ID: {{ store.suite_id || 23 }}</el-tag>
            </div>
          </template>

          <!-- 用户输入和响应 -->
          <el-row :gutter="20" class="mb-3">
            <el-col :span="12">
              <div class="info-section">
                <h5 class="section-title">用户输入</h5>
                <p class="content-text">{{ item.user_input }}</p>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-section">
                <h5 class="section-title">参考答案</h5>
                <p class="content-text">{{ item.reference }}</p>
              </div>
            </el-col>
          </el-row>

          <!-- 检索上下文 -->
          <el-row class="mb-3">
            <el-col :span="24">
              <div class="info-section">
                <h5 class="section-title">检索上下文</h5>
                <p class="content-text">{{ item.retrieved_contexts }}</p>
              </div>
            </el-col>
          </el-row>

          <!-- 模型响应 -->
          <el-row class="mb-3">
            <el-col :span="24">
              <div class="info-section">
                <h5 class="section-title">模型响应</h5>
                <div class="response-content">
                  <el-scrollbar height="200px">
                    <p class="content-text">{{ item.response }}</p>
                  </el-scrollbar>
                </div>
              </div>
            </el-col>
          </el-row>

          <!-- 评估指标对比 -->
          <el-row>
            <el-col :span="24">
              <div class="metrics-section">
                <h5 class="section-title">评估指标对比</h5>
                <el-table :data="item.compare" border size="small" class="metrics-table" style="width: 100%;">
                  <el-table-column prop="field" label="指标名称" min-width="180">
                    <template #default="{ row }">
                      <span class="metric-name">{{ formatMetricName(row.field) }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="before_value" label="执行前" min-width="140" align="center">
                    <template #default="{ row }">
                      <el-tag :type="getValueTagType(row.before_value)" size="small">
                        {{ formatValue(row.before_value) }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="after_value" label="执行后" min-width="140" align="center">
                    <template #default="{ row }">
                      <el-tag :type="getValueTagType(row.after_value)" size="small">
                        {{ formatValue(row.after_value) }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="is_different" label="变化状态" min-width="140" align="center">
                    <template #default="{ row }">
                      <el-tag :type="row.is_different ? 'warning' : 'success'" size="small">
                        {{ row.is_different ? '有变化' : '无变化' }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column label="变化趋势" min-width="160" align="center">
                    <template #default="{ row }">
                      <span class="trend-indicator">
                        <el-icon v-if="getTrend(row) === 'up'" color="#67C23A" size="16">
                          <ArrowUp />
                        </el-icon>
                        <el-icon v-else-if="getTrend(row) === 'down'" color="#F56C6C" size="16">
                          <ArrowDown />
                        </el-icon>
                        <el-icon v-else color="#909399" size="16">
                          <Minus />
                        </el-icon>
                        {{ getTrendText(row) }}
                      </span>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </el-col>
          </el-row>
        </el-card>
      </div>

      <!-- 空状态 -->
      <el-empty v-else description="暂无对比数据，请点击查询按钮获取数据" />
    </el-col>
  </el-row>

  <!-- 新增对比详情对话框 -->
  <el-dialog
    v-model="store.detailDialogVisible"
    title="结果详情"
    width="80%"
    :before-close="handleDialogClose"
  >
    <!-- 使用v-for循环展示所有对比项 -->
    <el-card v-for="(item, index) in store.currentDetail" :key="index" class="mb-4">
      <!-- <div class="card-header">
        <h4>对比项 {{ index + 1 }}</h4>
      </div> -->
      <el-descriptions border :column="2">
        <el-descriptions-item label="参考内容">{{ item.reference }}</el-descriptions-item>
        <el-descriptions-item label="响应内容">{{ item.response }}</el-descriptions-item>
        <el-descriptions-item label="检索上下文">{{ item.retrieved_contexts }}</el-descriptions-item>
      </el-descriptions>
    </el-card>
  </el-dialog>
    </el-tab-pane>
    <el-tab-pane label="LLM报告" name="second" lazy style="display: block;">
      <llmReport ref="testllm" :llmCollect='store.llm_collect ' :llmReportData="store.llm_report_data"></llmReport>
    </el-tab-pane>
  </el-tabs>



</template>

<script setup>
import { ref,onMounted, nextTick } from 'vue'
import * as echarts from 'echarts'
import { reportStore } from '@/pinia/reportModule'
import llmReport from '@/components/llmReport.vue';
import {ragReport} from '@/api/reports';
import { ArrowUp, ArrowDown, Minus, Search, SuccessFilled, WarningFilled, Setting, Download } from '@element-plus/icons-vue';
 const activeName = ref('first')



const handleClick = (tab, event) => {
  // if(tab.paneName=='second'){
  //   store.getLlmReport()
  // }
}
 const store = reportStore()
 const selectedOption2 = ref('')

// 查询相关
const queryKey = ref('')
const  selectedOption1=ref('')

// 新增：统一触发三个RAG相关查询的方法
const handleRagQuery = () => {
  // 调用三个目标方法（根据实际需要传递参数，如图表容器ID）
  store.getRagReport('projectPie');          // 传递饼图容器ID
  // store.getRagReportStatistics('trendChart11'); // 传递折线图容器ID
  // store.getRagReportStats('analysisPie');      // 传递分析饼图容器ID
};
const resetRagQuery = () => {
  store.project_list_id=null
  store.getRagReport('projectPie');          // 传递饼图容器ID

};



// 辅助方法：格式化指标名称
const formatMetricName = (field) => {
  const nameMap = {
    'answer_relevancy': '答案相关性',
    'context_entity_recall': '上下文实体召回',
    'context_precision': '上下文精确度',
    'context_recall': '上下文召回率',
    'faithfulness': '忠实度'
  };
  return nameMap[field] || field;
};

// 辅助方法：格式化数值
const formatValue = (value) => {
  if (typeof value === 'number') {
    return value.toFixed(2);
  }
  return value || '0.00';
};

// 辅助方法：获取数值标签类型
const getValueTagType = (value) => {
  if (value >= 0.8) return 'success';
  if (value >= 0.5) return 'warning';
  return 'danger';
};

// 辅助方法：计算趋势
const getTrend = (row) => {
  const before = parseFloat(row.before_value) || 0;
  const after = parseFloat(row.after_value) || 0;
  if (after > before) return 'up';
  if (after < before) return 'down';
  return 'same';
};

// 辅助方法：获取趋势文本
const getTrendText = (row) => {
  const trend = getTrend(row);
  if (trend === 'up') return '提升';
  if (trend === 'down') return '下降';
  return '持平';
};

onMounted(() => {
  if(activeName.value=='first'){
    // 获取项目列表
    store.requestServiceList()

    // 获取RAG层级结构数据
    store.getRagHierarchicalStructure()

    // 延迟初始化图表数据，确保DOM已渲染
    setTimeout(() => {
      console.log('=== 页面加载完成，开始初始化图表 ===');
      store.getRagReportStatistics('trendChart11')
      store.getRagReport('projectPie')
      store.getRagReportStats('analysisPie')
      store.getRagCompareResult('list') // 修改为列表模式
    }, 500);
  }
})

// 新增对话框控制变量
const dialogVisible = ref(false)
// 假设从store获取最近两次对比数据（需根据实际接口返回结构调整）
const recentCompareData = ref([
  [
    { label: '上下文查准率', value: '85%' },
    { label: '上下文召回率', value: '78%' },
    { label: '忠实度', value: '92%' }
  ],
  [
    { label: '上下文查准率', value: '88%' },
    { label: '上下文召回率', value: '81%' },
    { label: '忠实度', value: '95%' }
  ]
])

// 对话框关闭前回调
const handleDialogClose = (done) => {
  dialogVisible.value = false
  done()
}
// 新增：分析指标的选项数据和绑定变量

</script>

<style scoped>
.section {
width: 100%;

  margin-top: 20px;
  padding: 0 16px;
}

/* 查询区域样式 */
.query-section {
  padding: 20px;
  display: flex;
  gap: 16px;
  align-items: center;
}

/* 项目卡片样式 */
.project-card {
  height: 300px;  /* 与右侧饼图高度一致 */

  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
}

/* 图表容器样式 */
.chart-container {
  height: 300px;
  width: 100%;
  border: 2px solid #ebedf0;
  border-radius: 4px;
  padding: 16px;
}

.pie-container {

  height: 399px;
  width: 80%;  /* 改为100%填满所在列 */
  border: 2px solid #ebedf0;
  border-radius: 4px;
  padding: 16px;
}
.analysis_pie{
    height: 358px;
  width: 80%;  /* 改为100%填满所在列 */
  border: 2px solid #ebedf0;
  border-radius: 4px;
  padding: 16px;
}

.small-card {
 height: 140px;  /* 小卡片高度 */
  padding: 16px;

  text-align: center;
  border: none;
}

.small-card .card-text {
  color: #606266;
  font-size: 14px;
  margin-bottom: 12px;
}

.small-card .card-number {
  color: #3f8bfa;
  font-size: 20px;
  font-weight: 500;
}

/* 查询卡片样式 */
.query-card {
  margin-bottom: 24px;
  border-radius: 12px;
  border: 1px solid #e4e7ed;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: box-shadow 0.3s ease;
}

.query-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
}

.query-row {
  align-items: flex-end;
  padding: 16px 0;
  min-height: 80px;
}

.query-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
  height: 100%;
  justify-content: flex-end;
}

.query-label {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 6px;
  line-height: 1.4;
}

/* 对比结果查询卡片样式 */
.comparison-query-card {
  margin-bottom: 28px;
  border-radius: 12px;
  border: 1px solid #e4e7ed;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  background: linear-gradient(135deg, #f8faff 0%, #ffffff 100%);
}

.comparison-query-card:hover {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

.comparison-query-card .card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 0;
  padding: 16px 20px;
  border-bottom: 1px solid #f0f2f5;
  background-color: rgba(64, 158, 255, 0.05);
}

.comparison-query-card .section-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
  position: relative;
  padding-left: 12px;
}

.comparison-query-card .section-title:before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 18px;
  background-color: #409eff;
  border-radius: 2px;
}

.comparison-query-row {
  align-items: flex-end;
  padding: 20px 0;
  min-height: 100px;
}

.query-group {
  display: flex;
  flex-direction: column;
  gap: 10px;
  height: 100%;
  justify-content: flex-end;
}

.query-group-label {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 6px;
  line-height: 1.4;
  position: relative;
  padding-left: 8px;
}

.query-group-label:before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 14px;
  background-color: #409eff;
  border-radius: 1.5px;
}

.execution-times-controls {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
  padding: 12px;


}

.times-input-group {
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: white;
  padding: 6px 10px;
  border-radius: 6px;
  border: 1px solid #ddd;
}

.input-label {
  font-size: 13px;
  color: #666;
  font-weight: 500;
  min-width: 24px;
  text-align: center;
}

.separator {
  font-size: 14px;
  color: #409eff;
  font-weight: 600;
  padding: 0 8px;
  background-color: rgba(64, 158, 255, 0.1);
  border-radius: 4px;
  line-height: 1.6;
}

.unit-label {
  font-size: 13px;
  color: #666;
  font-weight: 500;
  background-color: rgba(108, 117, 125, 0.1);
  padding: 4px 8px;
  border-radius: 4px;
}

.action-buttons {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
  justify-content: flex-start;
}

/* 对比结果列表样式 */
.query-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.control-label {
  font-weight: 500;
  color: #333;
  white-space: nowrap;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .query-row {
    flex-wrap: wrap;
    gap: 16px;
  }

  .query-item {
    min-width: 200px;
  }

  .comparison-query-row {
    flex-wrap: wrap;
    gap: 20px;
  }

  .execution-times-controls {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}

@media (max-width: 768px) {
  .query-card,
  .comparison-query-card {
    margin: 0 -8px 20px -8px;
    border-radius: 8px;
  }

  .query-row,
  .comparison-query-row {
    padding: 12px 0;
    flex-direction: column;
    align-items: stretch;
  }

  .query-item,
  .query-group {
    width: 100%;
    margin-bottom: 16px;
  }

  .execution-times-controls {
    flex-direction: row;
    justify-content: space-between;
  }

  .times-input-group {
    flex: 1;
    justify-content: center;
  }

  .action-buttons {
    justify-content: center;
    width: 100%;
  }

  .comparison-query-card .section-title {
    font-size: 16px;
  }
}

@media (max-width: 480px) {
  .execution-times-controls {
    flex-direction: column;
    gap: 12px;
  }

  .times-input-group {
    width: 100%;
    justify-content: space-between;
  }

  .action-buttons {
    flex-direction: column;
    width: 100%;
  }

  .action-buttons .el-button {
    width: 100%;
  }
}

/* 图标间距调整 */
.el-tag .el-icon {
  margin-right: 4px;
}

.el-button .el-icon {
  margin-right: 4px;
}

/* 微调样式 */
.query-card .el-card__body {
  padding: 20px;
}

.comparison-query-card .el-card__body {
  padding: 24px;
}

/* 输入框和选择器的统一样式 */
.el-input,
.el-select,
.el-cascader {
  transition: all 0.3s ease;
}

.el-input:hover,
.el-select:hover,
.el-cascader:hover {
  border-color: #409eff;
}

.el-input:focus-within,
.el-select:focus-within,
.el-cascader:focus-within {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.comparison-list {
  max-height: 800px;
  overflow-y: auto;
  width: 94.5%;
}

.comparison-item {
  margin-bottom: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h4 {
  margin: 0;
  color: #333;
  font-size: 16px;
  font-weight: 600;
}

.info-section {
  background: #f8f9fa;
  padding: 12px;
  border-radius: 6px;
  border-left: 4px solid #409eff;
}

.section-title {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
  color: #409eff;
}

.content-text {
  margin: 0;
  line-height: 1.6;
  color: #666;
  font-size: 13px;
}

.response-content {
  background: #fff;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 8px;
}

.metrics-section {
  background: #fafafa;
  padding: 16px;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
  width: 100%;
}

.metrics-table {
  margin-top: 12px;
  width: 100% !important;
}

.metrics-table .el-table__body-wrapper {
  width: 100% !important;
}

.metric-name {
  font-weight: 500;
  color: #333;
}

.trend-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  font-weight: 500;
  justify-content: center;
}

.mb-3 {
  margin-bottom: 16px;
}

.mb-4 {
  margin-bottom: 20px;
}

/* 确保表格撑满容器 */
.el-table {
  width: 100% !important;
}

.el-table__header-wrapper,
.el-table__body-wrapper {
  width: 100% !important;
}
</style>


