<template>


    <el-tabs v-model="frameName" type="border-card" style="width: 100%;margin-top: 1%;">

    <el-tab-pane label="框架自动评估" name="frameEvaluate">


    <el-tabs v-model="activeName01" class="demo-tabs" @tab-click="handleClick01">

    <el-tab-pane label="RAG" name="first">
      <el-input
          class="suiteinput"
          placeholder="请输入内容"
          style="width: 15%"
        ></el-input>

        <el-input
          class="userquestion"
          placeholder="请输入内容"
          style="width: 15%"
        ></el-input>
        <el-button style="color: #615ced;width: 3%; margin-left: 1%;" plain
          >查询
        </el-button>
        <!-- <el-button style="color: #615ced;width: 5%; margin-left: 1%;" plain
          >RAG评估
        </el-button> -->



        <el-table

        :data="autoEvaluationData"

        style="width: 100%; margin-top: 1%; margin-left: 0.3%; height: 90%"
    >
        <el-table-column
            v-for="(column, index) in autoEvaluationColumns"
            :key="index"
            :prop="column.prop"
            :label="column.label"
            min-width="10%"
            show-overflow-tooltip

        >


        </el-table-column>
        <el-table-column>
    <slot>
      <el-table-column label="操作" min-width="10%">

    <el-button color="#626aef"  plain>详情</el-button>

    </el-table-column>
</slot>




</el-table-column>




     </el-table>

      </el-tab-pane>
    <el-tab-pane label="LLM" name="second">

      <el-table

        :data="autoEvaluationData2"

        style="width: 100%; margin-top: 1%; margin-left: 0.3%; height: 90%"
    >
        <el-table-column
            v-for="(column, index) in autoEvaluationColumns2"
            :key="index"
            :prop="column.prop"
            :label="column.label"
            min-width="10%"
            show-overflow-tooltip

        >


        </el-table-column>
        <el-table-column>
          <slot>
      <el-table-column label="操作" min-width="10%">

    <el-button color="#626aef"  plain>详情</el-button>

    </el-table-column>
</slot>





</el-table-column>




    </el-table>


    </el-tab-pane>
    <el-footer style="float: right;background: #fff;width: 100%;" >
    <el-pagination
    :page-size=10
    :pager-count="11"
    layout="prev, pager, next"
    :total=ragListTotal
    @current-change="handleCurrentChange"
   style="float: right;margin-top: 0.8%;"
  />
        </el-footer>

  </el-tabs>


    </el-tab-pane>
<!--
    <el-tab-pane label="人工评估" name="humaneEvaluate">

<el-table
:data="Case"
style="width: 100%; margin-top: 1%; margin-left: 0.3%; height: 90%"
>
<el-table-column
            v-for="(column, index) in autoEvaluationColumns3"
            :key="index"
            :prop="column.prop"
            :label="column.label"
            min-width="30%"
            show-overflow-tooltip

        >
    </el-table-column>

  <el-table-column>
    <template #default="scope" >

<el-button color="#626aef" @click="handlerun(scope.row)"  plain>编辑</el-button>
<el-button color="#626aef" @click="detail(scope.row)" plain>详情</el-button>
</template>




</el-table-column>

</el-table>




</el-tab-pane> -->

    </el-tabs>

    <el-dialog
      title="数据评估"
      :model-value="dialogVisible"
      :before-close="handleClose"
      width="50%"
      height="90%"
    >


    <el-row>
    <el-col :span="12">
        <span>评测框架类型</span>
       <el-input placeholder="请输入内容" style="width: 50%;margin-left:2% ;"></el-input>
    </el-col>

  </el-row>

  <el-row style="margin-top: 2%;">
  <el-col :span="12">
        <span>获取测试数据</span>
       <el-input placeholder="请输入内容" style="width: 50%;margin-left:2% ;"></el-input>
    </el-col>

  </el-row>

  <el-row style="margin-top: 2%;">
  <el-col :span="12">
        <span>问题</span>
       <el-input placeholder="请输入内容" style="width: 50%;margin-left:13% ;"></el-input>
    </el-col>

  </el-row>

<el-row style="margin-top: 2%;">
  <el-col :span="12">
        <span>上下文信息</span>
       <el-input placeholder="请输入内容" style="width: 50%;margin-left:4% ;"></el-input>
    </el-col>

  </el-row>

  <el-row style="margin-top: 2%;">
  <el-col :span="12">
        <span>生成答案</span>
       <el-input placeholder="请输入内容" style="width: 50%;margin-left:7% ;"></el-input>
    </el-col>

  </el-row>



<el-row style="margin-top: 2%;">
  <el-col :span="12">
        <span>标准答案</span>
       <el-input placeholder="请输入内容" style="width: 50%;margin-left:7% ;"></el-input>
    </el-col>

  </el-row>

  <el-row style="margin-top: 2%;">
  <el-col :span="12">
        <span>测试评分</span>
       <el-input placeholder="请输入内容" style="width: 50%;margin-left:7% ;"></el-input>
    </el-col>

  </el-row>
  <template #footer>
      <span class="dialog-footer">
        <el-button >取消</el-button>
        <el-button >
          保存
        </el-button>
      </span>
    </template>
    </el-dialog>



</template>

<script setup>
import { onMounted, ref,defineProps,watch ,reactive,defineExpose} from "vue";
import { ragList } from '@/api/rag';
import dayjs from 'dayjs';
import {llmList } from '@/api/llm';
import {humanevaluatorList,updateDate} from '@/api/humanevaluator';



let dialogVisible = ref(false);


const activeName01 = ref('first')
const frameName = ref('frameEvaluate')

let ragListTotal=ref(0);
let ragListPage=ref(1);
const props = defineProps({
  message: {
    type: String
  }
});
let updateForm = ref({

  reference: "",
  remark: "",
  score: 0

});

let updateVisible=ref(false)

let ragtype=ref()
let ragversion=ref()
let ragsuit_id=ref()
let ragtest_case_id=ref()
let ragkeyword=ref()
let updateid=ref()

const handleClick01 = (tab, event) => {
  if(tab.paneName==='second'){

    let params = {
    page:1,
    size:10
  };
  llmList({...params}).then((res) => {
    if(res.success){

    autoEvaluationData2.value=res.resp.map(item => {
      return {
        ...item,
        create_time:dayjs(item.create_time).format('YYYY-MM-DD HH:mm:ss'),
        update_time:dayjs(item.update_time).format('YYYY-MM-DD HH:mm:ss')
      }})
    }
    })


    }
// res.total

// res.total_page



}



let value01 = ref(1);
const options01 = [
  {
    value: 1,
    label:'RAG',
  },
  {
    value: 2,
    label:'LLM',
  }

]


// 动态表头数据
let autoEvaluationColumns = ref([
     {
        prop: "id",
        label: "ID"

    },
    {
        prop: "version",
        label: "需求版本"

    },
    {
        prop: "suit_id",
        label: "用例集id"

    },
    {
        prop: "test_case_id",
        label: "测试用例id"

    },
    {
        prop: "type",
        label: "评测框架类型"

    },
    {
        prop: "user_input",
        label: "用户提问",
    },
    {
        prop: "retrieved_contexts",
        label: "检索上下文",
    },
    {
        prop: "response",
        label: "生成答案",
    },
    {
        prop: "reference",
        label: "标准答案",
    },
    {
        prop: "context_precision",
        label: "上下文精准度",
    },
    {
        prop: "faithfulness",
        label: "忠诚度",
    },
    {
        prop: "answer_relevancy",
        label: "答案准确性",
    },
    {
        prop: "score",
        label: "分数",
    },
    {
        prop: "context_entity_recall",
        label: "上下文召回度",
    },
     {
        prop: "executor",
        label: "执行人",
    },
     {
        prop: "created_time",
        label: "创建时间",
    },
     {
        prop: "updated_time",
        label: "更新时间",
    }
]);

let autoEvaluationColumns2 = ref([
    {
        prop: "id",
        label: "ID"

    },
    {
        prop: "type",
        label: "类型"

    },
    {
        prop: "suit_id",
        label: "用例集id",
    },
    {
        prop: "test_case_id",
        label: "用例id",
    },
    {
        prop: "version",
        label: "版本",
    },
    {
        prop: "conversation",
        label: "对话内容",
    },
    {
        prop: "reference",
        label: "正确答案",
    },
    {
        prop: "reference_topics",
        label: "主题",
    },
    {
        prop: "agent_goal_accuracy",
        label: "是否满足目标",
    },
    {
        prop: "f1",
        label: "AI回答F1",
    },
    {
        prop: "precision",
        label: "AI回答准确度",
    },
    {  prop:"recall",
    label:"AI回答的召回率"},


    {
        prop: "executor",
        label: "执行人",
    },

     {
        prop: "created_time",
        label: "创建时间",
    },
     {
        prop: "updated_time",
        label: "更新时间",
    }
]);




// 框架自动评估数据
let autoEvaluationData = ref([

]);


let autoEvaluationData2 = ref([
]);
const filterTag = (value, row) => {
  // if(row.frameworkType === "框架A"){
  //  console.log('row===',row);
  //}
  return row.frameworkType === value
}
function aaas(ff) {


  autoEvaluationColumns.value.forEach(column => {
            // if (column === "框架A") {
            //   console.log('aaaaaa');
            // }
           // column.visible = ["frameworkType", "testData", "question"].includes(column.prop);
        // } else if (selectedFramework === "框架B") {
        //     column.visible = ["frameworkType", "context", "generatedAnswer"].includes(column.prop);
        // } else {
        //     column.visible = true; // 默认显示所有列
        // }
    });
    // const selectedFramework = filters.frameworkType ? filters.frameworkType[0] : null;
    // autoEvaluationColumns.value.forEach(column => {
    //     if (selectedFramework === "框架A") {
    //         column.visible = ["frameworkType", "testData", "question"].includes(column.prop);
    //     } else if (selectedFramework === "框架B") {
    //         column.visible = ["frameworkType", "context", "generatedAnswer"].includes(column.prop);
    //     } else {
    //         column.visible = true; // 默认显示所有列
    //     }
    // });
}
function saveBut() {

let data={
  reference: updateForm.value.reference,
  remark: updateForm.value.remark,
  score: updateForm.value.score
}
updateDate(updateid.value,data).then((res) => {
  if(res.success){
    humanList()
    updateVisible.value=false

  }

  })
  //updateDate
}
// 接口请求
function testarglist(){
  let params = {
    page:ragListPage.value,
    size:10,
    type:ragtype.value,
    version:ragversion.value,
    suit_id:ragsuit_id.value,
    test_case_id:ragtest_case_id.value,
    keyword:ragkeyword.value,

  };
  ragList({...params}).then((res) => {
    if(res.success){
    autoEvaluationData.value=res.resp.map(item => {
      return {
        ...item,
        create_time:dayjs(item.create_time).format('YYYY-MM-DD HH:mm:ss'),
        update_time:dayjs(item.update_time).format('YYYY-MM-DD HH:mm:ss')
      }
    })

    ragListTotal.value=res.total

    }
// res.total

// res.total_page



});
}

function handleCurrentChange(val) {
    ragListPage.value=val;
    testarglist();
  }

  watch(
  () => props.message,
  (newVal, oldVal) => {
    frameName.value = newVal

  }
)

const childMethod = () => {
};
 defineExpose({
  childMethod
})

onMounted(() => {
  testarglist();
})

</script>
<style scoped>
.demo-tabs > .el-tabs__content {
  padding: 32px;
  color: #6b778c;
  font-size: 32px;
  font-weight: 600;
}
.dialog-footer button:first-child {
  margin-right: 10px;
}
.frameinput:before {
  content: "框架类型";
}

.suiteinput:before {
  content: "条件A";
}

.userquestion:before {
  content: "条件A";
}
</style>