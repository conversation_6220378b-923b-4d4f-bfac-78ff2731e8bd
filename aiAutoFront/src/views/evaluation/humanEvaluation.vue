<template>
    <h3>人工评估</h3>
    <el-table
:data="Case"
style="width: 100%; margin-top: 1%; margin-left: 0.3%; height: 90%"
>
<el-table-column
            v-for="(column, index) in autoEvaluationColumns3"
            :key="index"
            :prop="column.prop"
            :label="column.label"
            min-width="30%"
            show-overflow-tooltip

        >
    </el-table-column>

</el-table>
<el-footer style="float: right;background: #fff;width: 100%;" >
    <el-pagination
          :page-size="10"
          :pager-count="11"
          layout="prev, pager, next"
          :total=serviceListTotal
          @current-change="handleCurrentChange01"
          style="float: right; margin-top: 0.8%"
  />
        </el-footer>


</template>
<script setup>
import { onMounted, ref,defineProps,watchEffect ,reactive} from "vue";
import dayjs from 'dayjs';
import {humanevaluatorList} from '@/api/humanevaluator';



const props = defineProps({
  count: Number,
  // 新增：监听弹窗是否打开
  dialogVisible: {
    type: Boolean,
    default: false
  }
})

let serviceListPage=ref(1);
let serviceListTotal=ref(0);
function handleCurrentChange01(val) {
    serviceListPage.value=val;
    humanList();
  }

// 新增：清除数据的方法
function clearData() {
  Case.value = [];
  serviceListTotal.value = 0;
  serviceListPage.value = 1;
}

// 新增：强制刷新数据的方法
function forceRefresh() {
  // 先清除数据
  clearData();
  // 然后重新获取数据
  setTimeout(() => {
    humanList();
  }, 100);
}


function humanList(){
  let params = {
    page:serviceListPage.value,
    size:10
  };
  humanevaluatorList({...params}).then((res) => {
    if(res.success){
      serviceListTotal.value = res.total;

      Case.value = res.resp.map(item => {
        return {
          ...item,
          // 处理项目名称
          project_name: item.suite?.project?.name || item.project_name || '未知项目',
          // 处理用例集名称
          suite_name: item.suite?.name || item.suite_name || '未知用例集',
          // 处理用例名称
          case_name: item.case?.name || item.case_name || item.user_input || '未知用例',
          // 确保模型回答字段存在
          response: item.response || item.model_response || '无回答',
          // 确保得分字段存在
          score: item.score || 0,
          // 确保备注字段存在
          remark: item.remark || item.comment || '',
          // 保留时间格式化（如果需要的话）
          created_time: item.created_time ? dayjs(item.created_time).format('YYYY-MM-DD HH:mm:ss') : '',
          updated_time: item.updated_time ? dayjs(item.updated_time).format('YYYY-MM-DD HH:mm:ss') : ''
        }
      });

    }
  })
}

let Case =  ref([])

let autoEvaluationColumns3 = ref([
    {
        prop: "project_name",
        label: "项目"
    },
    {
        prop: "suite_name",
        label: "用例集"
    },
    {
        prop: "case_name",
        label: "用例名称"
    },
    {
        prop: "response",
        label: "模型回答"
    },
    {
        prop: "score",
        label: "得分"
    },
    {
        prop: "remark",
        label: "备注"
    }
]);






watchEffect(()=>{
    humanList();
})

// 监听弹窗打开状态，当弹窗打开时刷新数据
watchEffect(() => {
  if (props.dialogVisible) {
    humanList();
  }
});


onMounted(() => {
    humanList();
})

// 暴露清除方法给父组件
defineExpose({
  clearData,
  humanList,
  forceRefresh
});

</script>
<style scoped>

</style>
