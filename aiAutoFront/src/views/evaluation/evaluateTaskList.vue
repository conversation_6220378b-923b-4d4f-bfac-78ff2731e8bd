<template>
  <div class="execution-list-container">
    <!-- 搜索过滤区域 -->
    <div class="filter-bar">
      <!-- <el-select
      v-model="suite_sequence_id"
      filterable
      reserve-keyword
      @focus="getSuiteExecute(counter.get_rag_suite_id)"
      @blur="get1111()"
    >
      <el-option
        v-for="item in counter.SequenceData"
        :key="item.value"
        :label="item.label"
        :value="item.value"
      >
      <span style="float: left">{{'序列号'}}{{ item.num }}{{"："}}{{item.label}}</span>
      </el-option>
    </el-select> -->

      <el-tree-select
        v-model="store.suiteValue"
        :props="treeProps"
        :load="loadNode"
        lazy
        style="width: 200px"
        placeholder="请选择层级数据"
      />

      <el-select
        v-model="store.filterStatusvalue"
        placeholder="状态筛选"
        style="width: 200px"
      >
        <el-option label="待执行" value="1" />
        <el-option label="执行中" value="2" />
        <el-option label="成功" value="3" />
        <el-option label="失败" value="4" />
        <el-option label="超时" value="5" />
      </el-select>

      <el-button type="primary" @click="store.fetchTaskList()">查询</el-button>
    </div>

    <!-- 主表格区域 -->
    <el-table :data="filteredList" style="width: 100%">
      <!-- 基础信息列 -->
      <el-table-column
        prop="id"
        label="ID"
        min-width="8%"
        sortable
        show-overflow-tooltip
      />
      <el-table-column
        label="用例集ID"
        prop="suite_id"
        min-width="10%"
        sortable
        show-overflow-tooltip
      >
      </el-table-column>

      <el-table-column
        label="用例集名称"
        prop="suite_name"
        min-width="15%"
        sortable
        show-overflow-tooltip
      >
      </el-table-column>

      <el-table-column
        label="用例集执行ID"
        prop="original_request.suite_sequence_id"
        min-width="12%"
        sortable
        show-overflow-tooltip
      >
      </el-table-column>

      <el-table-column
        label="评估耗时"
        prop="execution_times"
        min-width="10%"
        sortable
        show-overflow-tooltip
      >
      </el-table-column>

      <el-table-column
        label="状态"
        min-width="10%"
        sortable
        show-overflow-tooltip
      >
        <template #default="scope">
          <el-tag :type="getTagType(scope.row.status)">
            {{ getStatusText(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column
        label="执行开始时间"
        prop="start_time"
        min-width="15%"
        sortable
        show-overflow-tooltip
      >
        <template #default="{ row }">
          {{ formatTime(row.start_time) }}
        </template>
      </el-table-column>

      <el-table-column
        label="执行结束时间"
        prop="end_time"
        min-width="15%"
        sortable
        show-overflow-tooltip
      >
        <template #default="{ row }">
          {{ formatTime(row.end_time) }}
        </template>
      </el-table-column>

      <el-table-column label="操作" width="140" fixed="right">
        <template #default="{ row }">
          <div class="action-buttons">
            <el-button
              type="primary"
              size="small"
              link
              @click.stop="viewDetail(row)"
            >
              <el-icon><View /></el-icon>
              详情
            </el-button>
            <el-button
              type="danger"
              size="small"
              link
              @click.stop="store.postragRetry(row)"
              v-if="row.status === 4 || row.status === 5"
            >
              <el-icon><Refresh /></el-icon>
              重试
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <el-footer style="float: right; background: #fff; width: 100%">
      <el-pagination
        :page-size="20"
        :pager-count="11"
        layout="prev, pager, next"
        :total="store.TaskListTotal"
        @current-change="store.handleCurrentChange"
        style="float: right; margin-top: 0.8%"
      />
    </el-footer>

    <!-- 在表格操作列下方添加 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="任务详情"
      width="80%"
      :before-close="() => (detailDialogVisible = false)"
    >
      <el-card>
        <!-- 基础信息 -->
        <el-descriptions border :column="4" style="margin-bottom: 20px">
          <el-descriptions-item label="用例集ID">{{
            currentDetail?.suite_id
          }}</el-descriptions-item>
          <el-descriptions-item label="用例集名称">{{
            currentDetail?.suite_name
          }}</el-descriptions-item>
          <el-descriptions-item label="版本">
            <span v-for="item in currentDetail.results" :key="index">
              {{ item.version }}
            </span>
          </el-descriptions-item>
          <el-descriptions-item label="主题">
            <span v-for="item in currentDetail.results" :key="index">
              {{ item.reference_topics }}
            </span>
          </el-descriptions-item>
          <el-descriptions-item label="执行状态">
            <el-tag :type="currentDetail?.status === 3 ? 'success' : 'danger'">
              {{ currentDetail?.status === 3 ? "成功" : "失败" }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item
            label="错误信息"
            v-if="currentDetail?.error != null"
          >
            {{ currentDetail.error }}
          </el-descriptions-item>
          <el-descriptions-item label="开始时间">{{
            formatTime(currentDetail?.start_time)
          }}</el-descriptions-item>
          <el-descriptions-item label="结束时间">{{
            formatTime(currentDetail?.end_time)
          }}</el-descriptions-item>
        </el-descriptions>

        <!-- 指标详情 -->
        <el-card title="指标详情" style="margin-bottom: 20px">
          <el-row :gutter="20">
            <el-col
              :span="6"
              v-for="(metric, index) in currentDetail?.results"
              :key="index"
            >
              <!-- <el-card class="metric-card"> -->
              <div class="metric-title">{{ metric.reference }}</div>
              <div class="metric-values">
                <div class="metric-item">
                  <span class="label">精确度</span>
                  <el-tag :type="getScoreType(metric.precision)">{{
                    metric.precision?.toFixed(2)
                  }}</el-tag>
                </div>
                <div class="metric-item">
                  <span class="label">上下文召回</span>
                  <el-tag type="success">{{
                    metric.recall?.toFixed(2)
                  }}</el-tag>
                </div>
                <div class="metric-item">
                  <span class="label">F1</span>
                  <el-tag type="success">{{ metric.f1?.toFixed(2) }}</el-tag>
                </div>
              </div>
              <!-- </el-card> -->
            </el-col>
          </el-row>
        </el-card>

        <!-- 响应对比 -->
        <el-card title="响应对比">
          <el-table :data="currentDetail?.results" style="width: 100%">
            <el-table-column label="参考内容" prop="reference" min-width="30%">
              <template #default="{ row }">
                <pre style="white-space: pre-wrap">{{ row.reference }}</pre>
              </template>
            </el-table-column>
            <el-table-column label="响应内容" prop="response" min-width="30%">
              <template #default="{ row }">
                <pre style="white-space: pre-wrap">{{ row.response }}</pre>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
        <el-table
          :data="currentDetail?.results"
          style="width: 100%; margin-top: 20px"
        >
          <el-table-column>
            <template #default="{ row }">
              <!-- 遍历当前结果项的conversation数组 -->
              <el-table :data="row.conversation" border style="width: 100%">
                <el-table-column
                  label="角色"
                  prop="role"
                  min-width="20%"
                ></el-table-column>
                <el-table-column label="对话内容" min-width="80%">
                  <template #default="{ row: conv }">
                    <pre
                      style="white-space: pre-wrap; margin: 0; padding: 8px"
                      >{{ conv.content }}</pre
                    >
                  </template>
                </el-table-column>
              </el-table>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from "vue";
import { useCounterStore } from "@/pinia/taskstore";
import { serviceList } from "@/api/projectApi";
import { suiteInProject, getResultSequence } from "@/api/suitApi";
import dayjs from "dayjs";
import { View, Refresh } from '@element-plus/icons-vue';
const store = useCounterStore();

const props = defineProps({
  data: {
    type: Array,
    required: true,
  },
  total: {
    type: Number,
    default: 0,
  },
});

const searchKey = ref("");
const filterStatus = ref([]);
const treeProps = {
  label: "label", // 指定节点显示的文本字段
  children: "children", // 指定子节点字段
  value: "value", // 指定节点值字段
  isLeaf: "isLeaf",
};

let suiteOptions = ref([]);

// 重写数据加载逻辑（适配el-tree-select的懒加载）
const loadNode = (node, resolve) => {
  // node.level 表示节点深度（0-根节点，1-一级子节点，2-二级子节点）
  if (node.level === 0) {
    // 加载第一级（项目数据）
    getserviceList().then(() => {
      resolve(suiteOptions.value); // 将项目数据作为根节点
    });
  } else if (node.level === 1) {
    // 加载第二级（套件数据，node.data是当前点击的项目节点）
    getSuiteid(node.data.value).then((suiteItems) => {
      node.data.children = suiteItems; // 将套件数据作为项目节点的子节点
      resolve(suiteItems);
    });
  } else if (node.level === 2) {
    // 加载第三级（序列号数据，node.data是当前点击的套件节点）
    getSuiteExecute(node.data.value).then((sequenceItems) => {
      node.data.children = sequenceItems; // 将序列号数据作为套件节点的子节点
      resolve(sequenceItems);
    });
  }
};

// 修改原有数据加载函数为Promise形式（适配async/await）
async function getserviceList() {
  const params = { page: 1, page_size: 100 };
  const res = await serviceList(params);
  const projectNodes = res.resp.map((item) => ({
    value: item.id,
    label: item.name,
    children: [], // 初始化为空数组，懒加载时填充
  }));
  suiteOptions.value = projectNodes; // 更新根节点数据
  return projectNodes;
}

async function getSuiteid(projectId) {
  const params = { project_id: projectId, page: 1, page_size: 100 };
  const res = await suiteInProject(params);
  const suiteNodes = res.resp.map((item) => ({
    value: item.id,
    label: item.name,
    children: [], // 初始化为空数组，懒加载时填充
  }));
  return suiteNodes;
}

async function getSuiteExecute(suiteId) {
  const params = { suite_id: suiteId };
  const res = await getResultSequence(params);
  const sequenceNodes = res.resp.map((item) => ({
    value: item.id,
    label: `序列号${item.sequence_num}：${item.suite__name}`,
    isLeaf: true,
    // 叶节点无需children（或设为undefined）
  }));
  return sequenceNodes;
}

// 格式化时间显示
const formatTime = (time) => {
  return dayjs(time).format("YYYY-MM-DD HH:mm:ss");
};

// 评分类型判断
const getScoreType = (score) => {
  if (score >= 0.8) return "success";
  if (score >= 0.5) return "warning";
  return "danger";
};

// 列表过滤计算
const filteredList = computed(() => {
  return store.taskListData.filter((item) => {
    const matchSearch =
      !searchKey.value ||
      item.test_case_name.includes(searchKey.value) ||
      item.id.toString().includes(searchKey.value);

    const matchStatus =
      filterStatus.value.length === 0 ||
      filterStatus.value.includes(item.status);

    return matchSearch && matchStatus;
  });
});

// ... 现有代码 ...

// 新增：详情对话框状态
const detailDialogVisible = ref(false);
const currentDetail = ref(null); // 存储当前选中的详情数据

// 修改viewDetail方法，触发对话框显示
const viewDetail = (row) => {
  currentDetail.value = row; // 存储当前行数据

  detailDialogVisible.value = true; // 显示对话框
};

// 执行状态文本映射

// 状态文本映射
const getStatusText = (status) => {
  const map = {
    success: "成功",
    failed: "失败",
    running: "执行中",
    pending: "待执行",
    timeout: "超时",
  };
  // 1-pending/2-running/3-completed/4-failed/5-timeout
  if (status === 1) {
    return map["pending"];
  } else if (status === 2) {
    return map["running"];
  } else if (status === 3) {
    return map["success"];
  } else if (status === 4) {
    return map["failed"];
  } else {
    return map["info"];
  }

  // return map[status] || '未知';
};

// 状态标签颜色映射
const getTagType = (status) => {
  const map = {
    completed: "success",
    failed: "danger",
    running: "primary",
    pending: "warning",
  };
  //return map[status] || 'info';
  if (status === 1) {
    return map["pending"];
  } else if (status === 2) {
    return map["running"];
  } else if (status === 3) {
    return map["success"];
  } else if (status === 4) {
    return map["failed"];
  } else {
    return map["info"];
  }
};

onMounted(async () => {
  await store.fetchTaskList();
});
</script>

<style scoped>
.execution-list-container {
  padding: 20px;
}

.filter-bar {
  margin-bottom: 20px;
  display: flex;
  gap: 10px;
}

.suite-info {
  display: flex;
  flex-direction: column;
}
.suite-id {
  color: #909399;
  font-size: 12px;
}
.suite-name {
  font-weight: 500;
}

.metric-card {
  margin-bottom: 10px;
}
.metric-title {
  font-weight: bold;
  margin-bottom: 8px;
}
.metric-values {
  display: flex;
  gap: 10px;
}
.metric-item {
  display: flex;
  align-items: center;
  gap: 5px;
}
.label {
  color: #606266;
}

.response-comparison {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  max-height: 400px;
  overflow-y: auto;
}
.response-item pre {
  white-space: pre-wrap;
  background: #f8f9fa;
  padding: 10px;
  border-radius: 4px;
}
.reference pre {
  background: #f0f9eb;
}

.error-panel {
  padding: 10px;
  background: #fef0f0;
}

.action-buttons {
  display: flex;
  gap: 8px;
  align-items: center;
}
</style>
