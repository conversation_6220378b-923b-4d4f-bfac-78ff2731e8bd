
import request from '../utils/request';



export const llmCompareResult = (params) => {
    return request({
        url: 'api/ai/llm/compare/result',
        method: 'get',
        params
    });
};

export const ragCompareResult = (params) => {
    return request({
        url: '/api/ai/rag/compare/result',
        method: 'get',
        params
    });
};


export const ragReportStatistics = (params) => {
    return request({
        url: '/api/reports/rag/statistics/',
        method: 'get',
        params
    });
};
export const ragReportStats = (params) => {
    return request({
        url: 'api/reports/rag/stats/',
        method: 'get',
        params
    });
};
export const ragReport = (params) => {
    return request({
        url: 'api/reports/rag',
        method: 'get',
        params
    });
};
// /api/reports/llm/statistics/
export const llmReportStatistics = (params) => {
    return request({
        url: 'api/reports/llm/statistics/',
        method: 'get',
        params
    });
}
// /api/reports/llm/stats/
export const llmReportStats = (params) => {
    return request({
        url: 'api/reports/llm/stats/',
        method: 'get',
        params
    });
}

// /api/reports/llm
export const llmReport = (params) => {
    return request({
        url: 'api/reports/llm',
        method: 'get',
        params
    });
}