
import request from '../utils/request';

export const requireList = (Id) => {
    return request({
        url: `/requirement/${Id}`,
        method: 'get'
    });
};

export const requireList1 = (Id,params) => {
    return request({
        url: `/requirement/${Id}`,
        method: 'get',
        params
    });
};


export const addRequire = (data) => {
    return request({
        url: '/requirement',
        method: 'post',
        data
    });
};

export const updateRequire = (data) => {
    return request({
        url: '/requirement',
        method: 'put',
        data
    });
};
export const delRequire = (params) => {
    return request({
        url: '/requirement',
        method: 'delete',
        params
    });
};
