import request from "../utils/request";

export const modelList = (params) => {
  return request({
    url: "/api/llm/config/",
    method: "get",
    params,
  });
};

export const modelDetail = (Id) => {
  return request({
    url: `/api/llm/config/${Id}`,
    method: "get",
    params,
  });
};

export const modelConfig = (data) => {
  return request({
    url: "/api/llm/config/",
    method: "post",
    data,
  });
};

export const modelUpdate = (data, Id) => {
  return request({
    url: `/api/llm/config/${Id}`,
    method: "put",
    data,
    Id,
  });
};

export const modelDelete = (Id) => {
  return request({
    url: `/api/llm/config/${Id}`,
    method: "delete",
    Id,
  });
};
