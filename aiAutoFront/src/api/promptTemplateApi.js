import request from '../utils/request';

export const templateList = (params) => {
    return request({


        url: '/prompt_template',
        method: 'get',
        params


    });
};

export const templateAdd = (data) => {
    return request({


        url: '/prompt',
        method: 'post',
        data


    });
};

export const templateEdit = (params) => {
    return request({


        url: '/prompt',
        method: 'put',
        params


    });
};
export const promptList = () => {
    return request({


        url: '/prompt',
        method: 'get'
    });
};




