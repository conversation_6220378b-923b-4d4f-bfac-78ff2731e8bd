import request from '../utils/request';

// 获取全部用户信息
export const getAllUser = (params) => {
    return request({
        url: '/manage/user',
        method: 'get',
        params
    });
};


// 创建用户
export const createUser = (data) => {
    return request({
        url: '/manage/user',
        method: 'post',
        data
    });
};


// 获取单个用户
export const getUserDetail = (id) => {
    return request({
        url: `/manage/user/${id}`,
        method: 'get',
    });
};

// 更新用户
export const updateUser = (data) => {
    return request({
        url: '/manage/user',
        method: 'put',
        data
    });
};


// 删除用户
export const deleteUser = (params) => {
    return request({
        url: '/manage/user',
        method: 'delete',
        params
    });
};


// 获取全部项目信息
export const getAllProject = (params) => {
    return request({
        url: '/project',
        method: 'get',
        params
    });
};

// 创建项目
export const createProject = (data) => {
    return request({
        url: '/project',
        method: 'post',
        data
    });
};

// 更新项目
export const updateProject = (data) => {
    return request({
        url: '/project',
        method: 'put',
        data
    });
};

export const deleteProject = (params) => {
    return request({
        url: '/project',
        method: 'delete',
        params
    });
};


// 获取全部项目成员
export const getAllProjectMember = (data) => {
    return request({
        url: '/project_member/all',
        method: 'post',
        data
    });
};


// 增加项目成员
export const addProjectMember = (data) => {
    return request({
        url: '/project_member',
        method: 'post',
        data
    });
};

export const deleteProjectMember = (params) => {
    return request({
        url: '/project_member',
        method: 'delete',
        params
    });
};


export const getAllPromptTemplate = (params) => {
    return request({
        url: '/manage/prompt_template',
        method: 'get',
        params
    });
};


export const updatePromptTemplate = (data) => {
    return request({
        url: '/manage/prompt_template',
        method: 'put',
        data
    });
};


export const addPromptTemplate = (data) => {
    return request({
        url: '/manage/prompt_template',
        method: 'post',
        data
    });
};


export const deletePromptTemplate = (params) => {
    return request({
        url: '/manage/prompt_template',
        method: 'delete',
        params
    });
};