import request from '../utils/request';
// rag评估接口

export const ragEvaluate = (data) => {
    return request({
        url: '/api/ai/rag/evaluate',
        method: 'post',
        data
    });
};

export const llmEvaluate = (data) => {
    return request({
        url: '/api/ai/llm/evaluate',
        method: 'post',
        data
    });
};


export const llmEvaluateList  = (params) => {
    return request({
        url: '/api/ai/llm/evaluate',
        method: 'get',
        params
    });
};

export const llmEvaluateDetail = (Id) => {
    return request({
        url: `/api/ai/llm/evaluate/${Id}`,
        method: 'get'
    });
};



export const suiteCase = (params) => {
    return request({
        url: '/suite/case',
        method: 'get',
        params
    });
};



export const suiteInProject = (params) => {
    return request({
        url: '/suite/in_project',
        method: 'get',
        params
    });
};


export const suiteInRequirement = (params) => {
    return request({
        url: '/suite/in_requirement',
        method: 'get',
        params
    });
};
export const addSuite = (data) => {
    return request({
        url: '/suite/',
        method: 'post',
        data
    });
};


export const getEvaluateStatus = (Id) => {
    return request({
        url: `/api/ai/rag/evaluate/status/${Id}`,
        method: 'get'
    });
};


export const ragEvaluateList= (params) => {
    return request({
        url: '/api/ai/rag/evaluate',
        method: 'get',
        params
    });
};


export const updateSuite = (data) => {
    return request({
        url: '/suite/',
        method: 'put',
        data
    });
};

export const delSuite = (params) => {
    return request({
        url: '/suite/'+'?'+params.id,
        method: 'delete',
        params
    });
}


export const postSuiteExecute = (data) => {

    return request({
        url: '/suite_execute/'+'?'+'suite_id='+data,
        method: 'post',
        data
    });
};

export const getResultSequence = (params) => {
    return request({
        url:'/suite_execute/result/sequence',
        method: 'get',
        params
    });
};

export const getResultDetail = (params) => {
    return request({
        url:'suite_execute/result/sequence/detail',
        method: 'get',
        params
    });
};

export const llmConfig = (params) => {
    return request({
        url:'/api/llm/config',
        method: 'get',
        params
    });
};

export const getUserLimitResultSequence = (data) => {
    return request({
        url:'/suite_execute/result/sequence/user',
        method: 'post',
        data
    });
};


export const getHierarchicalStructure = () => {
    return request({
        url:'/suite/hierarchical/structure',
        method: 'get',
        timeout: 30000, // 增加超时时间到30秒
        headers: {
            'Content-Type': 'application/json'
        }
    });
};
