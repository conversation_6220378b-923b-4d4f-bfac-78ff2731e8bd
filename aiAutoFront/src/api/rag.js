
import request from '../utils/request';

export const ragList = (params) => {
    return request({
        url: '/api/ai/rag/evaluate',
        method: 'get',
        params
    });
};


export const getEvaluateDetails = (Id) => {
    return request({
        url: `/api/ai/rag/evaluate/${Id}`,
        method: 'get'
    });
};

export const taskList = (params) => {
    return request({
        url: '/api/ai/evaluate/task',
        method: 'get',
        params
    });
};


export const ragRetry= (id) => {
    return request({
        url: `api/ai/rag/evaluate/retry/${id}`,
        method: 'post'

    });
};

// export const updateRequire = (data) => {
//     return request({
//         url: '/requirement',
//         method: 'put',
//         data
//     });
// };
// export const delRequire = (params) => {
//     return request({
//         url: '/requirement',
//         method: 'delete',
//         params
//     });
// };
