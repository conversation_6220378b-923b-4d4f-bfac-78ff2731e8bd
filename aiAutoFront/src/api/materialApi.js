import request from '../utils/request';






export const materialsource= (params) => {
    return request({


            url: '/material/source',
            method: 'get',
            params


    });
};

export const materialtype = (params) => {
    return request({


            url: '/material/type',
            method: 'get',
            params


    });
};
export const materialList = (params) => {
    return request({


            url: '/material/',
            method: 'get',
            params


    });
};

export const userMaterialList = (params) => {
    return request({


            url: '/material/user',
            method: 'get',
            params


    });
};


export const addMateria = (data) => {
    return request({
        url: '/material',
        method: 'post',
        data
    });
};

export const updateMateria = (data) => {
    return request({
        url: '/material',
        method: 'put',
        data
    });
};
export const delMateria = (params) => {
    return request({
        url: '/material',
        method: 'delete',
        params
    });
};


export const copyMateria = (data) => {
    return request({
        url: '/material/copy/'+'?'+'id='+data,
        method: 'post',
        data
    });
};