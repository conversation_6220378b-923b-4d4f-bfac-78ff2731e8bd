import request from '../utils/request';

export const getProjectList = (Id) => {
    return request({
        url: `/project/${Id}`,
        method: 'get'
    });
};
export const serviceList = (params) => {
    return request({
        url: '/project',
        method: 'get',
        params
    });
};
//新增项目
export const addProject = (data) => {
    return request({
        url: '/project',
        method: 'post',
        data
    });
};

export const updateProject = (data) => {
    return request({
        url: '/project',
        method: 'put',
        data
    });
};
export const delProject = (params) => {
    return request({
        url: '/project'+'?'+params.id,
        method: 'delete',
        params
    });
};

