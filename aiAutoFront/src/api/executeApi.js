import request from '../utils/request';
// rag评估接口


export const getEvaluateStatus = (Id) => {
    return request({
        url: `/api/ai/rag/evaluate/status/${Id}`,
        method: 'get'
    });
};


// export const ragEvaluateList= (params) => {
//     return request({
//         url: '/api/ai/rag/evaluate',
//         method: 'get',
//         params
//     });
// };


// export const postSuiteExecute = (data) => {

//     return request({
//         url: '/suite_execute/'+'?'+'suite_id='+data,
//         method: 'post',
//         data
//     });
// };

// 录入多轮多话
export const recordMultipleConversation = (data) => {
    return request({
        url: '/suite_execute/record/multiple/conversation',
        method: 'post',
        data
    });
};

