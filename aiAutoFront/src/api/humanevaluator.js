import request from '../utils/request';

export const humanevaluatorList = (params) => {
    return request({
        url: '/api/ai/manual/evaluate',
        method: 'get',
        params
    });
};


export const addManualDate = (data) => {
    return request({
        url: '/api/ai/manual/evaluate',
        method: 'post',
        data
    });
};

export const detailManualDate = (Id) => {
    return request({
        url: `/api/ai/manual/evaluate/${Id}`,
        method: 'get'

    });
};

export const updateManualDate = (Id,data) => {
    return request({
        url: `/api/ai/manual/evaluate/${Id}`,
        method: 'put',
        data
    });
};

export const updateRagDate = (Id,data) => {
    return request({
        url: `/api/ai/rag/evaluate/${Id}`,
        method: 'put',
        data
    });
};