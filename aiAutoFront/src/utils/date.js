import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime'
import 'dayjs/locale/zh-cn'

// 配置 dayjs
dayjs.extend(relativeTime)
dayjs.locale('zh-cn')

/**
 * 格式化日期
 * @param {string|Date} date - 日期
 * @param {string} format - 格式化模板
 * @returns {string} 格式化后的日期字符串
 */
export function formatDate(date, format = 'YYYY-MM-DD HH:mm:ss') {
  if (!date) return ''
  return dayjs(date).format(format)
}

/**
 * 获取相对时间
 * @param {string|Date} date - 日期
 * @returns {string} 相对时间字符串
 */
export function getRelativeTime(date) {
  if (!date) return ''
  return dayjs(date).fromNow()
}

/**
 * 获取日期差值
 * @param {string|Date} date1 - 日期1
 * @param {string|Date} date2 - 日期2
 * @param {string} unit - 单位 (day, hour, minute, second)
 * @returns {number} 差值
 */
export function getDateDiff(date1, date2, unit = 'day') {
  return dayjs(date1).diff(dayjs(date2), unit)
}

/**
 * 判断是否为今天
 * @param {string|Date} date - 日期
 * @returns {boolean}
 */
export function isToday(date) {
  return dayjs(date).isSame(dayjs(), 'day')
}

/**
 * 判断是否为本周
 * @param {string|Date} date - 日期
 * @returns {boolean}
 */
export function isThisWeek(date) {
  return dayjs(date).isSame(dayjs(), 'week')
}

/**
 * 判断是否为本月
 * @param {string|Date} date - 日期
 * @returns {boolean}
 */
export function isThisMonth(date) {
  return dayjs(date).isSame(dayjs(), 'month')
}

/**
 * 获取时间段描述
 * @param {string|Date} startDate - 开始日期
 * @param {string|Date} endDate - 结束日期
 * @returns {string} 时间段描述
 */
export function getDateRangeText(startDate, endDate) {
  if (!startDate || !endDate) return ''
  
  const start = dayjs(startDate)
  const end = dayjs(endDate)
  
  if (start.isSame(end, 'day')) {
    return start.format('YYYY年MM月DD日')
  }
  
  if (start.isSame(end, 'month')) {
    return `${start.format('YYYY年MM月DD日')} - ${end.format('DD日')}`
  }
  
  if (start.isSame(end, 'year')) {
    return `${start.format('MM月DD日')} - ${end.format('MM月DD日')}`
  }
  
  return `${start.format('YYYY年MM月DD日')} - ${end.format('YYYY年MM月DD日')}`
}

/**
 * 获取友好的时间描述
 * @param {string|Date} date - 日期
 * @returns {string} 友好的时间描述
 */
export function getFriendlyTime(date) {
  if (!date) return ''
  
  const target = dayjs(date)
  const now = dayjs()
  
  if (target.isSame(now, 'day')) {
    return target.format('HH:mm')
  }
  
  if (target.isSame(now.subtract(1, 'day'), 'day')) {
    return `昨天 ${target.format('HH:mm')}`
  }
  
  if (target.isSame(now, 'year')) {
    return target.format('MM-DD HH:mm')
  }
  
  return target.format('YYYY-MM-DD')
}

/**
 * 获取工作日描述
 * @param {string|Date} date - 日期
 * @returns {string} 工作日描述
 */
export function getWeekdayText(date) {
  if (!date) return ''
  
  const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
  return weekdays[dayjs(date).day()]
}

/**
 * 计算工作日
 * @param {string|Date} startDate - 开始日期
 * @param {string|Date} endDate - 结束日期
 * @returns {number} 工作日数量
 */
export function getWorkdays(startDate, endDate) {
  if (!startDate || !endDate) return 0
  
  let start = dayjs(startDate)
  const end = dayjs(endDate)
  let workdays = 0
  
  while (start.isBefore(end) || start.isSame(end, 'day')) {
    const day = start.day()
    if (day !== 0 && day !== 6) { // 不是周末
      workdays++
    }
    start = start.add(1, 'day')
  }
  
  return workdays
}

/**
 * 获取时间状态
 * @param {string|Date} date - 日期
 * @returns {string} 时间状态 (past, present, future)
 */
export function getTimeStatus(date) {
  if (!date) return 'unknown'
  
  const target = dayjs(date)
  const now = dayjs()
  
  if (target.isBefore(now, 'day')) {
    return 'past'
  } else if (target.isSame(now, 'day')) {
    return 'present'
  } else {
    return 'future'
  }
}

export default {
  formatDate,
  getRelativeTime,
  getDateDiff,
  isToday,
  isThisWeek,
  isThisMonth,
  getDateRangeText,
  getFriendlyTime,
  getWeekdayText,
  getWorkdays,
  getTimeStatus
}
