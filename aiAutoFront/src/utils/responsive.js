// 响应式设计工具类
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'

// 断点配置
export const breakpoints = {
  xs: 480,
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
  '2xl': 1536
}

// 获取当前屏幕尺寸
export function useScreenSize() {
  const width = ref(window.innerWidth)
  const height = ref(window.innerHeight)

  const updateSize = () => {
    width.value = window.innerWidth
    height.value = window.innerHeight
  }

  onMounted(() => {
    window.addEventListener('resize', updateSize)
  })

  onUnmounted(() => {
    window.removeEventListener('resize', updateSize)
  })

  return {
    width,
    height
  }
}

// 获取当前断点
export function useBreakpoint() {
  const { width } = useScreenSize()

  const currentBreakpoint = computed(() => {
    if (width.value >= breakpoints['2xl']) return '2xl'
    if (width.value >= breakpoints.xl) return 'xl'
    if (width.value >= breakpoints.lg) return 'lg'
    if (width.value >= breakpoints.md) return 'md'
    if (width.value >= breakpoints.sm) return 'sm'
    return 'xs'
  })

  const isXs = computed(() => currentBreakpoint.value === 'xs')
  const isSm = computed(() => currentBreakpoint.value === 'sm')
  const isMd = computed(() => currentBreakpoint.value === 'md')
  const isLg = computed(() => currentBreakpoint.value === 'lg')
  const isXl = computed(() => currentBreakpoint.value === 'xl')
  const is2xl = computed(() => currentBreakpoint.value === '2xl')

  const isMobile = computed(() => width.value < breakpoints.md)
  const isTablet = computed(() => width.value >= breakpoints.md && width.value < breakpoints.lg)
  const isDesktop = computed(() => width.value >= breakpoints.lg)

  return {
    currentBreakpoint,
    isXs,
    isSm,
    isMd,
    isLg,
    isXl,
    is2xl,
    isMobile,
    isTablet,
    isDesktop,
    width
  }
}

// 媒体查询工具
export function useMediaQuery(query) {
  const matches = ref(false)

  const updateMatches = () => {
    matches.value = window.matchMedia(query).matches
  }

  onMounted(() => {
    const mediaQuery = window.matchMedia(query)
    matches.value = mediaQuery.matches
    mediaQuery.addEventListener('change', updateMatches)
  })

  onUnmounted(() => {
    const mediaQuery = window.matchMedia(query)
    mediaQuery.removeEventListener('change', updateMatches)
  })

  return matches
}

// 设备检测
export function useDeviceDetection() {
  const userAgent = navigator.userAgent.toLowerCase()

  const isMobile = /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(userAgent)
  const isTablet = /ipad|android(?!.*mobile)/i.test(userAgent)
  const isDesktop = !isMobile && !isTablet

  const isIOS = /iphone|ipad|ipod/i.test(userAgent)
  const isAndroid = /android/i.test(userAgent)
  const isSafari = /safari/i.test(userAgent) && !/chrome/i.test(userAgent)
  const isChrome = /chrome/i.test(userAgent)
  const isFirefox = /firefox/i.test(userAgent)

  const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0

  return {
    isMobile,
    isTablet,
    isDesktop,
    isIOS,
    isAndroid,
    isSafari,
    isChrome,
    isFirefox,
    isTouchDevice
  }
}

// 响应式网格系统
export function useResponsiveGrid(config = {}) {
  const {
    xs = 1,
    sm = 2,
    md = 3,
    lg = 4,
    xl = 5,
    '2xl': xxl = 6
  } = config

  const { currentBreakpoint } = useBreakpoint()

  const columns = computed(() => {
    switch (currentBreakpoint.value) {
      case 'xs': return xs
      case 'sm': return sm
      case 'md': return md
      case 'lg': return lg
      case 'xl': return xl
      case '2xl': return xxl
      default: return md
    }
  })

  const gridClasses = computed(() => {
    return `grid grid-cols-${columns.value} gap-4`
  })

  return {
    columns,
    gridClasses
  }
}

// 响应式字体大小
export function useResponsiveFontSize(config = {}) {
  const {
    xs = 'text-sm',
    sm = 'text-base',
    md = 'text-lg',
    lg = 'text-xl',
    xl = 'text-2xl',
    '2xl': xxl = 'text-3xl'
  } = config

  const { currentBreakpoint } = useBreakpoint()

  const fontSize = computed(() => {
    switch (currentBreakpoint.value) {
      case 'xs': return xs
      case 'sm': return sm
      case 'md': return md
      case 'lg': return lg
      case 'xl': return xl
      case '2xl': return xxl
      default: return md
    }
  })

  return {
    fontSize
  }
}

// 响应式间距
export function useResponsiveSpacing(config = {}) {
  const {
    xs = 'p-2',
    sm = 'p-4',
    md = 'p-6',
    lg = 'p-8',
    xl = 'p-10',
    '2xl': xxl = 'p-12'
  } = config

  const { currentBreakpoint } = useBreakpoint()

  const spacing = computed(() => {
    switch (currentBreakpoint.value) {
      case 'xs': return xs
      case 'sm': return sm
      case 'md': return md
      case 'lg': return lg
      case 'xl': return xl
      case '2xl': return xxl
      default: return md
    }
  })

  return {
    spacing
  }
}

// 响应式容器
export function useResponsiveContainer() {
  const { isMobile, isTablet, isDesktop } = useBreakpoint()

  const containerClasses = computed(() => {
    const classes = ['mx-auto']

    if (isMobile.value) {
      classes.push('px-4', 'max-w-full')
    } else if (isTablet.value) {
      classes.push('px-6', 'max-w-4xl')
    } else {
      classes.push('px-8', 'max-w-7xl')
    }

    return classes.join(' ')
  })

  return {
    containerClasses,
    isMobile,
    isTablet,
    isDesktop
  }
}

// 响应式侧边栏
export function useResponsiveSidebar() {
  const { isMobile } = useBreakpoint()
  const isCollapsed = ref(false)

  // 移动端默认收起
  watch(isMobile, (newValue) => {
    if (newValue) {
      isCollapsed.value = true
    }
  }, { immediate: true })

  const toggleSidebar = () => {
    isCollapsed.value = !isCollapsed.value
  }

  const sidebarClasses = computed(() => {
    const classes = ['transition-all', 'duration-300']

    if (isMobile.value) {
      classes.push(
        'fixed', 'left-0', 'top-0', 'h-full', 'z-50',
        isCollapsed.value ? '-translate-x-full' : 'translate-x-0'
      )
    } else {
      classes.push(
        'relative',
        isCollapsed.value ? 'w-16' : 'w-64'
      )
    }

    return classes.join(' ')
  })

  const overlayClasses = computed(() => {
    if (!isMobile.value || isCollapsed.value) return 'hidden'
    return 'fixed inset-0 bg-black bg-opacity-50 z-40'
  })

  return {
    isCollapsed,
    toggleSidebar,
    sidebarClasses,
    overlayClasses,
    isMobile
  }
}

// 响应式表格
export function useResponsiveTable() {
  const { isMobile } = useBreakpoint()

  const tableClasses = computed(() => {
    if (isMobile.value) {
      return 'block overflow-x-auto whitespace-nowrap'
    }
    return 'table w-full'
  })

  const shouldShowColumn = (priority = 1) => {
    const { currentBreakpoint } = useBreakpoint()

    const priorityMap = {
      1: ['xs', 'sm', 'md', 'lg', 'xl', '2xl'], // 总是显示
      2: ['sm', 'md', 'lg', 'xl', '2xl'],       // 小屏幕及以上
      3: ['md', 'lg', 'xl', '2xl'],             // 中等屏幕及以上
      4: ['lg', 'xl', '2xl'],                   // 大屏幕及以上
      5: ['xl', '2xl']                          // 超大屏幕及以上
    }

    return priorityMap[priority]?.includes(currentBreakpoint.value) ?? true
  }

  return {
    tableClasses,
    shouldShowColumn,
    isMobile
  }
}

// 导出所有工具
export default {
  breakpoints,
  useScreenSize,
  useBreakpoint,
  useMediaQuery,
  useDeviceDetection,
  useResponsiveGrid,
  useResponsiveFontSize,
  useResponsiveSpacing,
  useResponsiveContainer,
  useResponsiveSidebar,
  useResponsiveTable
}
