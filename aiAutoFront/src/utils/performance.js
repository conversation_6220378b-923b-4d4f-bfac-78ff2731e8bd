// 性能优化工具类
import { nextTick } from 'vue'

// 防抖函数
export function debounce(func, wait, immediate = false) {
  let timeout
  return function executedFunction(...args) {
    const later = () => {
      timeout = null
      if (!immediate) func(...args)
    }
    const callNow = immediate && !timeout
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
    if (callNow) func(...args)
  }
}

// 节流函数
export function throttle(func, limit) {
  let inThrottle
  return function executedFunction(...args) {
    if (!inThrottle) {
      func.apply(this, args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}

// 延迟执行
export function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms))
}

// 异步组件加载
export function asyncComponent(loader) {
  return {
    component: loader,
    loading: {
      template: `
        <div class="flex items-center justify-center p-8">
          <div class="loading-spinner"></div>
          <span class="ml-2 text-gray-600">加载中...</span>
        </div>
      `
    },
    error: {
      template: `
        <div class="flex items-center justify-center p-8 text-red-600">
          <span>组件加载失败</span>
        </div>
      `
    },
    delay: 200,
    timeout: 10000
  }
}

// 图片懒加载
export function lazyLoad(el, binding) {
  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        const img = entry.target
        img.src = binding.value
        img.classList.remove('lazy')
        observer.unobserve(img)
      }
    })
  })
  
  el.classList.add('lazy')
  observer.observe(el)
}

// 虚拟滚动
export function useVirtualScroll(options = {}) {
  const {
    itemHeight = 50,
    containerHeight = 400,
    buffer = 5
  } = options

  const visibleCount = Math.ceil(containerHeight / itemHeight)
  const totalHeight = ref(0)
  const scrollTop = ref(0)
  const startIndex = ref(0)
  const endIndex = ref(0)

  const updateVisibleRange = () => {
    startIndex.value = Math.max(0, Math.floor(scrollTop.value / itemHeight) - buffer)
    endIndex.value = Math.min(
      totalHeight.value / itemHeight,
      startIndex.value + visibleCount + buffer * 2
    )
  }

  const onScroll = (event) => {
    scrollTop.value = event.target.scrollTop
    updateVisibleRange()
  }

  return {
    startIndex,
    endIndex,
    onScroll,
    updateVisibleRange
  }
}

// 内存泄漏检测
export function memoryLeakDetector() {
  const listeners = new Set()
  const timers = new Set()
  const observers = new Set()

  const addListener = (element, event, handler) => {
    element.addEventListener(event, handler)
    listeners.add({ element, event, handler })
  }

  const addTimer = (timer) => {
    timers.add(timer)
  }

  const addObserver = (observer) => {
    observers.add(observer)
  }

  const cleanup = () => {
    // 清理事件监听器
    listeners.forEach(({ element, event, handler }) => {
      element.removeEventListener(event, handler)
    })
    listeners.clear()

    // 清理定时器
    timers.forEach(timer => {
      clearTimeout(timer)
      clearInterval(timer)
    })
    timers.clear()

    // 清理观察器
    observers.forEach(observer => {
      observer.disconnect()
    })
    observers.clear()
  }

  return {
    addListener,
    addTimer,
    addObserver,
    cleanup
  }
}

// 性能监控
export function performanceMonitor() {
  const metrics = {
    loadTime: 0,
    renderTime: 0,
    memoryUsage: 0,
    errors: []
  }

  // 页面加载时间
  const measureLoadTime = () => {
    if (performance.timing) {
      metrics.loadTime = performance.timing.loadEventEnd - performance.timing.navigationStart
    }
  }

  // 渲染时间
  const measureRenderTime = async (callback) => {
    const start = performance.now()
    await callback()
    await nextTick()
    metrics.renderTime = performance.now() - start
  }

  // 内存使用情况
  const measureMemoryUsage = () => {
    if (performance.memory) {
      metrics.memoryUsage = {
        used: performance.memory.usedJSHeapSize,
        total: performance.memory.totalJSHeapSize,
        limit: performance.memory.jsHeapSizeLimit
      }
    }
  }

  // 错误收集
  const collectError = (error) => {
    metrics.errors.push({
      message: error.message,
      stack: error.stack,
      timestamp: Date.now()
    })
  }

  // 生成报告
  const generateReport = () => {
    return {
      ...metrics,
      timestamp: Date.now(),
      userAgent: navigator.userAgent,
      url: window.location.href
    }
  }

  return {
    measureLoadTime,
    measureRenderTime,
    measureMemoryUsage,
    collectError,
    generateReport,
    metrics
  }
}

// 代码分割
export function createChunkLoader() {
  const loadedChunks = new Set()
  const loadingChunks = new Map()

  const loadChunk = async (chunkName, loader) => {
    if (loadedChunks.has(chunkName)) {
      return Promise.resolve()
    }

    if (loadingChunks.has(chunkName)) {
      return loadingChunks.get(chunkName)
    }

    const promise = loader().then(() => {
      loadedChunks.add(chunkName)
      loadingChunks.delete(chunkName)
    })

    loadingChunks.set(chunkName, promise)
    return promise
  }

  return {
    loadChunk,
    isLoaded: (chunkName) => loadedChunks.has(chunkName),
    isLoading: (chunkName) => loadingChunks.has(chunkName)
  }
}

// 缓存管理
export function createCache(maxSize = 100) {
  const cache = new Map()
  const accessOrder = []

  const get = (key) => {
    if (cache.has(key)) {
      // 更新访问顺序
      const index = accessOrder.indexOf(key)
      if (index > -1) {
        accessOrder.splice(index, 1)
      }
      accessOrder.push(key)
      return cache.get(key)
    }
    return null
  }

  const set = (key, value) => {
    if (cache.has(key)) {
      cache.set(key, value)
      return
    }

    if (cache.size >= maxSize) {
      // 删除最少使用的项
      const lru = accessOrder.shift()
      cache.delete(lru)
    }

    cache.set(key, value)
    accessOrder.push(key)
  }

  const clear = () => {
    cache.clear()
    accessOrder.length = 0
  }

  return {
    get,
    set,
    clear,
    size: () => cache.size
  }
}

// 资源预加载
export function preloadResources(resources) {
  const promises = resources.map(resource => {
    return new Promise((resolve, reject) => {
      if (resource.type === 'image') {
        const img = new Image()
        img.onload = resolve
        img.onerror = reject
        img.src = resource.url
      } else if (resource.type === 'script') {
        const script = document.createElement('script')
        script.onload = resolve
        script.onerror = reject
        script.src = resource.url
        document.head.appendChild(script)
      } else if (resource.type === 'style') {
        const link = document.createElement('link')
        link.rel = 'stylesheet'
        link.onload = resolve
        link.onerror = reject
        link.href = resource.url
        document.head.appendChild(link)
      }
    })
  })

  return Promise.allSettled(promises)
}

export default {
  debounce,
  throttle,
  delay,
  asyncComponent,
  lazyLoad,
  useVirtualScroll,
  memoryLeakDetector,
  performanceMonitor,
  createChunkLoader,
  createCache,
  preloadResources
}
