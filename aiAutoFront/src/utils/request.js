import axios from 'axios';
import { ElMessage, ElNotification } from 'element-plus';
// import router, { resetRouter } from '@/router';

const url = 'http://127.0.0.1:8080';


const service = axios.create({

    // baseURL:'/project'+url,
    baseURL: import.meta.env.VITE_BASE_API,
    timeout: 10000,
    headers: {
        'Content-Type': 'application/json'
    }
}
);

service.interceptors.request.use(
    (config) => {
        const authorization = JSON.parse(localStorage.getItem('token'))
        if (authorization) {
            config.headers.Authorization =authorization
        }


        return config;
    },
    (error) => {
        return Promise.reject(error);
    }
);

// 拦截响应
service.interceptors.response.use(
    (response) => {
        // debugger
        // console.log(response,"[][][][")

        const { status, data, request, headers, config } = response;
        if(status === 200){
            return data;
        }

        if (status === 200 && typeof data === 'string') {
            return data;
        }
        if (status === 200 && data.success) return data;
        if (status === 200 && request.responseType === 'blob') {
            return data;
        }
        if (status === 200 && request.responseType === 'arraybuffer') {
            return response;
        }
        if (status === 200 && config.responseType === 'stream') {
            return response;
        }
        ElMessage.error(data.msg);
        return Promise.reject(data.msg);
    },
    (error) => {
        const {
            response,
            response: { status, config, data, statusText }
        } = error;


        const redirect = encodeURIComponent(window.location.href);
        if (response && (status === 401 || status === 403)) {
            // 清除token
            //store.dispatch('app/clearToken');
            localStorage.removeItem('token');
              router.replace({
              path: '/ssoLogin'
            })
            ElNotification({
                title: `${status} 获取权限失败！`,
                message: '获取用户信息失败，请重新登陆！',
                type: 'error'
            });
        //   if(error.response.status ===  401){

        // localStorage.removeItem('token');
        // router.replace({
        //     path: '/login'
        // })

            resetRouter();
            // 代码不要往后执行了
            return Promise.reject(error);
        }

        if (response && status === 404) {
            router.push(`/error/404?redirect=${redirect}`);
            ElNotification({
                title: `${status} 接口未定义！`,
                message: `${config.url}`,
                type: 'error'
            });
            return Promise.reject(error);
        }

        if (response && status === 504) {
            ElNotification({
                title: `${status} 接口超时！`,
                message: `${config.url}`,
                type: 'error'
            });
            resetRouter();
            return Promise.reject(error);
        }

        // console.dir(error) // 可在此进行错误上报
        ElMessage.closeAll();

        try {
            ElMessage({
                message: data.msg || statusText,
                type: 'error'
            });
        } catch (err) {
            ElMessage({
                message: data.msg || statusText,
                type: 'error'
            });
        }

        return Promise.reject(error);
    }
);

export default service;
