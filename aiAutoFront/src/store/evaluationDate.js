import { onMounted, ref,defineProps,watch ,reactive} from "vue";
import { ragList } from '@/api/rag';
import dayjs from 'dayjs';
import {llmList } from '@/api/llm';

export default {
  namespaced: true,
   state: {
 testData:'测试获取数据！！！',

 dialogVisible :ref(false),


 activeName01 : ref('first'),
 frameName : ref('frameEvaluate'),

 ragListTotal:ref(0),
 ragListPage:ref(1),

 updateForm : ref({

  reference: "",
  remark: "",
  score: 0

}),

updateVisible:ref(false),

ragtype:ref(),
 ragversion:ref(),
ragsuit_id:ref(),
 ragtest_case_id:ref(),
 ragkeyword:ref(),
 updateid:ref(),
 detailVisible02:ref(false),

detailData:ref({
id: 0,
reference: "",
remark: "",
response: "",
score: 0,
suit_id: 0,
test_case_id: 0,
user_input: "",
version:"",
executor:''
})

 }
}