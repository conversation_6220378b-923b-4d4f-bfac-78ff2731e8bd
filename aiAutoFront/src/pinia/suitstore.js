// stores/counter.js
import * as echarts from "echarts";
import { defineStore } from "pinia";
import {
  suiteCase,
  suiteInProject,
  ragEvaluate,
  updateSuite,
  getEvaluateStatus,
  llmEvaluate,
  llmEvaluateDetail,
  addSuite,
  delSuite,
  postSuiteExecute,
  llmConfig,
  suiteInRequirement,
} from "../api/suitApi";
import { getResultSequence } from "@/api/suitApi";
import { ElMessage, ElNotification, ElMessageBox } from "element-plus";

import {
  ragReportStatistics,
  ragReportStats,
  llmReportStats,
  llmReportStatistics,
} from "@/api/reports";
import { serviceList } from "@/api/projectApi";
import { requireList, requireList1 } from "@/api/requireApi";
import { userMaterialList } from "@/api/materialApi";
import dayjs from "dayjs";
import { addManualDate } from "@/api/humanevaluator";
export const suiteStore = defineStore("suitestore", {
  state: () => ({
    suiteProjectList: [],
    casePlaceholder:"",
    executeProject:true,
    ragSequenceinput:false,
    caseVersionPlaceholder:'',
    suiteEvaluateData:{},
    suiteEvaluateData1:{},
    suitePlaceholder:'',
    suiteNamePlaceholder:'',
    llm_metric_label: [],
    llm_metric: [],
    llm_metric_stats: [],
    myElementPie: false,
    elementline: false,
    llmelementline: false,
    rag_metric_stats: [],
    selectedAnalysisMetric: null,
    analysisMetrics: [
      { label: "上下文查准率", value: "context_precision" },
      { label: "上下文召回率", value: "context_recall" },
      { label: "上下文实体召回率", value: "context_entity_recall" },
      { label: "忠实度", value: "faithfulness" },
      { label: "答案相关性", value: "answer_relevancy" },
    ],
    llmAnalysisMetric: null,
    llmAnalysisMetrics: [
      { label: "AI回答推断得分", value: "agent_goal_accuracy" },
      { label: "AI回答准确率", value: "precision" },
      { label: "AI回答召回率", value: "recall" },
      { label: "AI回答F1", value: "f1" },
    ],
    chartreport: false,
    reportCards: [],
    rag_metric: null,
    rag_metric_label: [],
    optionsId: "",
    editOptionsId: "",
    suiteOptions: [],
    treeProps: {
      label: "label", // 指定节点显示的文本字段
      children: "children", // 指定子节点字段
      value: "value", // 指定节点值字段
      isLeaf: "isLeaf",
    },

    projectnode: null,
    suiteItemLength: 0,
    // 重写数据加载逻辑（适配el-tree-select的懒加载）
    pgDialogVisible:false,

    ExecuteSuccess: false,
    projectId: null,
    RequireId: null,
    currentPage: 1,
    suite_page: 1,
    pageSize: 20,
    suite_total: 0,
    rag_task_id: null,
    rag_start_time: null,
    rag_status: null,
    llm_task_id: null,
    llm_start_time: null,
    llm_status: null,
    llmRespDetailConversationArray: [],
    llmRespDetail: [],
    llmRespDetailConversation: [],
    llmStatusDialog: false,
    get_llm_suite_id: null,
    llmModelData: [
      {
        label: "LLM",
        value: 2,
      },
    ],

    llmFormData: {
      type_id: null,
      model_id: null,
      suite_sequence_id: null,
      project_id: null,
      suite_id: null,
    },
    llm_suite_select:true,
    llm_suite_input:false,
    llm_suite_name:'',
    dialoghumanEvaluation: false,
     executeSuiteShow:false,
     executeSuiteSelectShow: true,

     // 新增：执行评估相关状态
     executeRagTestShow: false,
     executeLlmTestShow: false,
     executeRagOptionspg1: true,
     executeLlmOptionspg1: true,
     executeRagRengongval: false,
     executeLlmRengongval: false,
     executeRagValue90: false,
     executeLlmValue90: false,
     executeRagSuiteName: '',
     executeLlmSuiteName: '',
     executeRagCaseOptions: [],
     executeLlmCaseOptions: [],
    manualForm: {
      project_id: null,
      test_case_id: null,
      suite_id: null,
      remark: null,
      score: null,
      response: null,
      reference: null,
      user_input: null,
      version: null,
    },

    // 新增：执行评估表单数据
    executeRagFormdata: {
      model_id: null,
      embedding_model_id: null,
      suite_sequence_id: null,
      suite_id: null
    },
    executeLlmFormData: {
      type_id: null,
      suite_sequence_id: null,
      suite_id: null,
    },
    executeManualForm: {
      project_id: null,
      test_case_id: null,
      suite_id: null,
      remark: null,
      score: null,
      response: null,
      reference: null,
      user_input: null,
      version: null,
    },
    Sequencelength: 0,
    suiteEvaluateDialog: false,
    editDialogVisible: false,
    materialListData: [],
    materialListDataId: null,
    ruleFormEdit: {
      name: "",
      version: "",
      suite_type: 0,
      requirement: 0,
      material: [],
      description: "",
      projectDataId: null,
      requirement__name: null,
      materialListDataId: null,
    },
    ruleForm: {
      name: "",
      version: "",
      suite_type: 0,
      requirement: 0,
      material: [],
      description: "",
      projectDataId: null,
      requirement_list_id: null,
      materialListDataId: null

    },
    projectData: [],
    get_project_id: null,
    projectDataId: null,
    requirement_list_id: null,
    requirement_list: [],
    addDialogVisible: false,
    ragStatus: {},
    ragStatus1: null,
    test001dialog: false,
    llmcard: false,
    llmTestShow: false,
    pgStatu: false,
    value90: false,
    ragResp: [],
    newRagResp: [],
    optionspg1: true,
    llmoptionspg1: false,
    rengongval: false,
    pgcard: false,
    ragTestShow: false,

    checked3: false,
    checked4: false,
    test_case_id: null,
    formLabelAlign: {
      user_input: "",
       response: "",
      reference: "",
      remark: "",
      score: null,
     project_id: null,
      version: "",
     suite_id: null,
     test_case_id: null
    },
    newLlmResp: [],
    suite_sequence_input:'',
    get_rag_suite_id: null,
    suiteDisabled:false,
    ragProject:true,
    manual_project:true,
    ragSuite:true,

     llm_suite_select:true,

     llm_project_select:true,
    ragFormdata: {
      type_id: 1,
      embedding_model_id: null,
      suite_sequence_id: null,
      model_id: null,
      project_id: null,
      suite_id: null,
    },
    modelData:[],
    formLabelAlign3: {
      project_id: null,
      user_input: "",
      response: "",
      reference: "",
      remark: "",
      score: "",
      version: "",
      suit_id: "",
      test_case_id: "",
    },
    caseOptions: [],

    suiteColumns: [
      {
        prop: "id",
        label: "ID",
      },

      {
        prop: "name",
        label: "用例集",
      },
      {
        prop: "suite_type",
        label: "类型",
      },
      {
        prop: "requirement__name",
        label: "需求名称",
      },
      {
        prop: "version",
        label: "版本",
      },
      {
        prop: "creator__username",
        label: "创建人",
      },

      { prop: "create_time", label: "创建时间" },

      { prop: "update_time", label: "更新时间" },

      {
        prop: "description",
        label: "描述",
      },
    ],

    materialColumns: [
      {
        prop: "id",
        label: "ID",
      },

      {
        prop: "content_question",
        label: "问题",
      },
      {
        prop: "content_answer",
        label: "回答",
      },
      {
        prop: "content",
        label: "上下文",
      },
      {
        prop: "type__name",
        label: "类型",
      },
      {
        prop: "source__name",
        label: "来源",
      },

      { prop: "creator__username", label: "创建人" },

      {
        prop: "create_time",
        label: "创建时间",
      },
      { prop: "update_time", label: "更新时间" },
    ],
    updateForm: [
      {
        prop: "name",
        label: "名称",
      },

      {
        prop: "requirement__name",
        label: "需求",
      },
      {
        prop: "version",
        label: "版本",
      },
      {
        prop: "material",
        label: "物料",
      },
      {
        prop: "suite_type",
        label: "框架类型",
      },
      {
        prop: "description",
        label: "描述",
      },
    ],

    addForm: [
      {
        prop: "name",
        label: "名称",
      },

      {
        prop: "requirement",
        label: "需求",
      },
      {
        prop: "version",
        label: "版本",
      },
      {
        prop: "material",
        label: "物料",
      },
      {
        prop: "suite_type",
        label: "框架类型",
      },
      {
        prop: "description",
        label: "描述",
      },
    ],
    optionspg: [
      {
        label: "RAGS",
        value: 0,
      },
      {
        label: "LLM",
        value: 1,
      },
    ],

    dialogMaterialVisible: false,
    count: 100,
    SequenceData: [],
    SequenceData_id: null,
    Case: [],
    formLabelAlign3Options: [],
    list333: [],
    materialTableData: [],
    dialogreport: false,
    props_suite_id: null,
  }),
  actions: {
    getllmConfig(){
      const params = { page: 1, page_size: 100 };
      llmConfig(params).then((res) => {
        this.modelData = res.resp.map((item) => {
          return {
            label: item.model_name,
            value: item.id,
          };
        })

      })
    },
    // 重写数据加载逻辑（适配el-tree-select的懒加载）
    loadNode(node, resolve) {
      // node.level 表示节点深度（0-根节点，1-一级子节点，2-二级子节点）
      if (node.level === 0) {
        // 加载第一级（项目数据）
        this.getserviceList().then(() => {
          resolve(this.suiteOptions); // 将项目数据作为根节点
        });
      } else if (node.level === 1) {
        this.projectnode = node.data.value;
        // 加载第二级（套件数据，node.data是当前点击的项目节点）
        this.getSuiteid1(node.data.value).then((suiteItems) => {
          this.suiteItemLength = suiteItems.length;
          if (this.suiteItemLength === 0) {
            ElMessage.error({
              message: `当前项目下没有用例集，请选择其他项目!!`,
              type: "error",
              center: true,
            });
          }
          node.data.children = suiteItems; // 将套件数据作为项目节点的子节点

          resolve(suiteItems);
        });
      }
    },

    async getserviceList() {
      const params = { page: 1, page_size: 100 };
      const res = await serviceList(params);
      const projectNodes = res.resp.map((item) => ({
        value: item.id,
        label: item.name,
        children: [], // 初始化为空数组，懒加载时填充
      }));
      this.suiteOptions = projectNodes; // 更新根节点数据
      return projectNodes;
    },
    async getSuiteid1(projectId) {
      const res = await requireList1(projectId);
      const suiteNodes = res.resp.map((item) => ({
        value: item.id,
        label: item.name,
        children: [], // 初始化为空数组，懒加载时填充
        isLeaf: true,
      }));
      return suiteNodes;
    },

    suiteEvaluate() {
      this.suiteEvaluateDialog = true;
      this.ExecuteSuccess = false;
      // this.suiteExecute()
        // getHierarchicalStructure();

  //   // 将接口返回值赋值给 counter.suiteProjectList
  //   counter.suiteProjectList = res.resp;

    },

    async getRagReportStatistics(row, val) {
      let params = {
        suite_id: row,
      };

      await ragReportStatistics(params).then((res) => {
        if (res.success) {
          const labelMap = {
            requirement_count: "需求个数",
            suite_count: "测试套件个数",
            test_case_count: "测试用例个数",
            context_precision: "上下文查准率",
            context_recall: "上下文召回率",
            context_entity_recall: "上下文实体召回率",
            faithfulness: "忠实度",
            answer_relevancy: "答案相关性",
            // 可根据实际返回的字段扩展其他映射
          };
          this.rag_metric = Object.entries(res.resp).map(([key, value]) => ({
            label: labelMap[key] || key, // 优先使用中文映射，无匹配时保留原key
            value: value,
          }));
          for (let i = 0; i < this.rag_metric.length; i++) {
            this.rag_metric_label.push(this.rag_metric[i].label);
          }

          const trendChart = echarts.init(document.getElementById(val));
          trendChart.setOption({
            title: { text: "RAG指标统计0" },
            tooltip: {
              trigger: "axis",
              axisPointer: {
                type: "shadow",
              },
            },
            legend: {},
            grid: {
              left: "3%",
              right: "4%",
              bottom: "3%",
              containLabel: true,
            },
            xAxis: {
              type: "category",
              data: ["0.6以下", "0.6-0.7", "0.7-0.8", "0.8-0.9", "0.9以上"],
            },
            yAxis: { type: "value" },
            series: [
              {
                name: this.rag_metric_label[0],
                type: "bar",
                data: Object.values(this.rag_metric[0].value),
                stack: "sales", // 堆叠组标识符（需一致）
                barGap: "20%", // 同一类目下堆叠块间距（默认值）
                barWidth: "80%",

                itemStyle: {
                  normal: {
                    borderRadius: [15, 15, 15, 15],
                    color: "#574ad3",
                    borderColor: "#fff",
                    borderWidth: 10,
                  },
                },
              },
              {
                name: this.rag_metric_label[1],
                type: "bar",
                data: Object.values(this.rag_metric[1].value),
                stack: "sales", // 同一堆叠组
                borderRadius: [30, 30, 0, 0],
                barWidth: "80%",
                itemStyle: {
                  normal: {
                    borderRadius: [15, 15, 15, 15],
                    color: "#887cfc",
                    borderColor: "#fff",
                    borderWidth: 10,
                  },
                },
              },
              {
                name: this.rag_metric_label[2],
                type: "bar",
                data: Object.values(this.rag_metric[2].value),

                stack: "sales", // 同一堆叠组
                barGap: "20%",
                barWidth: "80%",
                itemStyle: {
                  normal: {
                    borderRadius: [15, 15, 15, 15],
                    color: "#4b96fd",
                    borderColor: "#fff",
                    borderWidth: 10,
                  },
                },
              },
              {
                name: this.rag_metric_label[3],
                type: "bar",
                data: Object.values(this.rag_metric[3].value),

                stack: "sales", // 同一堆叠组
                barGap: "20%",
                barWidth: "80%",
                itemStyle: {
                  normal: {
                    borderRadius: [15, 15, 15, 15],
                    color: "#2fb4b2",
                    borderColor: "#fff",
                    borderWidth: 10,
                  },
                },
              },
              {
                name: this.rag_metric_label[4],
                type: "bar",
                data: Object.values(this.rag_metric[4].value),
                borderRadius: [30, 30, 0, 0],
                stack: "sales", // 同一堆叠组
                barGap: "20%",
                barWidth: "80%",
                itemStyle: {
                  normal: {
                    borderRadius: [15, 15, 15, 15],
                    color: "#6fdfe2",
                    borderColor: "#fff",
                    borderWidth: 10,
                  },
                },
              },
            ],
          });
          this.elementline = true;
        }
      });
    },

    async getRagReportStats(row, val, metricVal) {
      let params = {
        metric: metricVal ? metricVal : "context_precision",
        suite_id: row,
      };
      await ragReportStats(params).then((res) => {
        if (res.resp.length > 0) {
          this.rag_metric_stats = res.resp.stats;
        }

        this.getanalysisPie(val);
      });
    },
    getanalysisPie(val) {
      let data = [];
      if (this.rag_metric_stats.length > 0) {
        data = this.rag_metric_stats;
      } else {
        data = [
          {
            value: 0,
            name: "",
          },
          {
            value: 0,
            name: "",
          },
          {
            value: 0,
            name: "",
          },
          {
            value: 0,
            name: "",
          },
          {
            value: 0,
            name: "",
          },
        ];

      }
      const analysisPie = echarts.init(document.getElementById(val));
      analysisPie.setOption({
        title: { text: "RAG指标占比" },

        tooltip: {
          trigger: "item",
        },
        legend: {
          top: "12%",
          left: "center",
        },
        series: [
          {
            top: "13%",
            // name: 'Access From',
            type: "pie",
            radius: ["40%", "70%"],
            avoidLabelOverlap: false,
            padAngle: 5,
            itemStyle: {
              borderRadius: 10,
            },
            label: {
              show: false,
              position: "center",
            },
            emphasis: {
              label: {
                show: true,
                fontSize: 40,
                fontWeight: "bold",
              },
            },
            labelLine: {
              show: false,
            },
            data: [
              {
                value: data[0].value,
                name: "小于0.6",
                itemStyle: {
                  color: "#468cf2",
                },
              },
              {
                value: data[1].value,
                name: "0.6-0.7",
                itemStyle: {
                  color: "#aac4e9",
                },
              },
              {
                value: data[2].value,
                name: "0.7-0.8",
                itemStyle: {
                  color: "#9fd5e5",
                },
              },

              {
                value: data[3].value,
                name: "0.8-0.9",
                itemStyle: {
                  color: "#ffb8f5",
                },
              },

              {
                value: data[4].value,
                name: "大于0.9",
                itemStyle: {
                  color: "#6d5ecf",
                },
              },
            ],
          },
        ],
      });
    },

    async getllmReportStats(row, val, metricval) {
      let params = {
        metric: metricval ? metricval : "precision",
        suite_id: row,
      };
      await llmReportStats(params).then((res) => {
        if (res.length > 0) {
          this.llm_metric_stats = res.resp.stats;
        }

        this.getllmReportStatsPie(val);
      });
    },
    getllmReportStatsPie(val) {
      const analysisPie = echarts.init(document.getElementById(val));
      let data = [];
      if (this.llm_metric_stats.length > 0) {
        data = this.llm_metric_stats;
      } else {
        data = [
          {
            value: 0,
            name: "",
          },
          {
            value: 0,
            name: "",
          },
          {
            value: 0,
            name: "",
          },
          {
            value: 0,
            name: "",
          },
          {
            value: 0,
            name: "",
          },
        ];
      }
      analysisPie.setOption({
        title: { text: "LLM指标占比" },
        tooltip: {
          trigger: "item",
        },
        legend: {
          top: "12%",
          left: "center",
        },
        series: [
          {
            top: "13%",
            name: "Access From",
            type: "pie",
            radius: ["40%", "70%"],
            avoidLabelOverlap: false,
            padAngle: 5,
            itemStyle: {
              borderRadius: 10,
            },
            label: {
              show: false,
              position: "center",
            },
            emphasis: {
              label: {
                show: true,
                fontSize: 40,
                fontWeight: "bold",
              },
            },
            labelLine: {
              show: false,
            },
            data: [
              {
                value: data[0].value,
                name: "小于0.6",
                itemStyle: {
                  color: "#468cf2",
                },
              },
              {
                value: data[1].value,
                name: "0.6-0.7",
                itemStyle: {
                  color: "#aac4e9",
                },
              },
              {
                value: data[2].value,
                name: "0.7-0.8",
                itemStyle: {
                  color: "#9fd5e5",
                },
              },

              {
                value: data[3].value,
                name: "0.8-0.9",
                itemStyle: {
                  color: "#ffb8f5",
                },
              },

              {
                value: data[4].value,
                name: "大于0.9",
                itemStyle: {
                  color: "#6d5ecf",
                },
              },
            ],
          },
        ],
      });
    },

    async getllmReportStatistics(row, val) {
      let params = {
        suite_id: row,
      };
      await llmReportStatistics(params).then((res) => {
        if (res.success) {
          const labelMap = {
            requirement_count: "需求个数",
            suite_count: "测试套件个数",
            test_case_count: "测试用例个数",
            agent_goal_accuracy: "AI回答推断得分",
            precision: "AI回答准确率",
            recall: "AI回答召回率",
            f1: "AI回答F1",
          };
          this.llm_metric = Object.entries(res.resp).map(([key, value]) => ({
            label: labelMap[key] || key, // 优先使用中文映射，无匹配时保留原key
            value: value,
          }));
          for (let i = 0; i < this.llm_metric.length; i++) {
            this.llm_metric_label.push(this.llm_metric[i].label);
          }
          this.getllmChart(val);
        }
      });
    },
    getllmChart(val) {
      const trendChart = echarts.init(document.getElementById(val));
      trendChart.setOption({
        title: { text: "LLM指标统计" },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
        },
        legend: {},
        grid: {
          left: "3%",
          right: "4%",
          bottom: "3%",
          containLabel: true,
        },
        xAxis: {
          type: "category",
          data: ["0.6以下", "0.6-0.7", "0.7-0.8", "0.8-0.9", "0.9以上"],
        },
        yAxis: { type: "value" },
        series: [
          {
            name: this.llm_metric_label[0],
            type: "bar",
            data: Object.values(this.llm_metric[0].value),
            stack: "sales", // 堆叠组标识符（需一致）
            barGap: "20%", // 同一类目下堆叠块间距（默认值）
            barWidth: "80%",

            itemStyle: {
              normal: {
                borderRadius: [15, 15, 15, 15],
                color: "#574ad3",
                borderColor: "#fff",
                borderWidth: 10,
              },
            },
          },
          {
            name: this.llm_metric_label[1],
            type: "bar",
            data: Object.values(this.llm_metric[1].value),
            stack: "sales", // 同一堆叠组
            borderRadius: [30, 30, 0, 0],
            barWidth: "80%",
            itemStyle: {
              normal: {
                borderRadius: [15, 15, 15, 15],
                color: "#887cfc",
                borderColor: "#fff",
                borderWidth: 10,
              },
            },
          },
          {
            name: this.llm_metric_label[2],
            type: "bar",
            data: Object.values(this.llm_metric[2].value),

            stack: "sales", // 同一堆叠组
            barGap: "20%",
            barWidth: "80%",
            itemStyle: {
              normal: {
                borderRadius: [15, 15, 15, 15],
                color: "#4b96fd",
                borderColor: "#fff",
                borderWidth: 10,
              },
            },
          },
          {
            name: this.llm_metric_label[3],
            type: "bar",
            data: Object.values(this.llm_metric[3].value),

            stack: "sales", // 同一堆叠组
            barGap: "20%",
            barWidth: "80%",
            itemStyle: {
              normal: {
                borderRadius: [15, 15, 15, 15],
                color: "#2fb4b2",
                borderColor: "#fff",
                borderWidth: 10,
              },
            },
          },
        ],
      });
      this.llmelementline = true;
    },

    // 状态文本映射
    getStatusText(status) {
      const map = {
        success: "成功",
        failed: "失败",
        running: "执行中",
        pending: "待执行",
        info: "超时",
      };
      // 1-pending/2-running/3-completed/4-failed/5-timeout
      if (status === 1) {
        return map["pending"];
      } else if (status === 2) {
        return map["running"];
      } else if (status === 3) {
        return map["success"];
      } else if (status === 4) {
        return map["failed"];
      } else {
        return map["info"];
      }

      // return map[status] || '未知';
    },

    // 状态标签颜色映射
    getTagType(status) {
      const map = {
        success: "success",
        failed: "danger",
        running: "primary",
        pending: "warning",
      };
      //return map[status] || 'info';
      if (status === 1) {
        return map["pending"];
      } else if (status === 2) {
        return map["running"];
      } else if (status === 3) {
        return map["success"];
      } else if (status === 4) {
        return map["failed"];
      } else {
        return map["info"];
      }
    },

    // 时间格式化（使用dayjs）
    formatTime(time) {
      return dayjs(time).format("YYYY-MM-DD HH:mm:ss");
    },

    reportDialog(row) {
      this.props_suite_id = row.id;

      this.dialogreport = true;
    },



    postManualDate(val) {

      // 获取项目ID
      let project_id = 0;
      if (this.suiteEvaluateData.id && this.suiteProjectList.length > 0) {
        project_id = this.findFirstLevelId(this.suiteProjectList, this.suiteEvaluateData.name) || 0;
      }

      // 重新组合数据
      const combinedData = this.combineManualData(val, this.suiteEvaluateData.id, this.suiteEvaluateData.version, project_id);

      // 继续原有逻辑
      this.dialoghumanEvaluation = true;
      this.ragTestShow = false;
      this.llmTestShow = false;

      // 创建符合接口要求的数组格式数据
      const requestData = [];

      // 如果 combinedData 是数组，直接使用
      if (Array.isArray(combinedData)) {
        requestData.push(...combinedData);
      } else {
        // 如果不是数组，转换为数组格式
        requestData.push(combinedData);
      }

      // 确保每个元素都符合接口格式
      const formattedData = requestData.map(item => ({
        user_input: item.user_input || "",
        response: item.response || "",
        reference: item.reference || "",
        remark: item.remark || "",
        score: Number(item.score) || 0,
        project_id: Number(item.project_id) || 0,
        version: item.version || "",
        suite_id: Number(item.suite_id) || 0,
        test_case_id: Number(item.test_case_id) || 0
      }));


      addManualDate(formattedData).then((res) => {
        if (res.success) {
          ElMessage.success('人工评估提交成功');
        }
      }).catch((error) => {
        console.error('原有人工评估提交失败:', error);
        ElMessage.error('人工评估提交失败');
      });
    },

    // 新增数据重组方法
    combineManualData(val, suite_id, version, project_id) {
      // 确保 caseOptions 存在且有数据
      if (!this.caseOptions || this.caseOptions.length === 0) {
        console.warn('caseOptions 为空，无法重组数据');
        return [];
      }

      // 重组数据格式
      const combinedData = this.caseOptions.map((caseOption) => {
        return {
          user_input: caseOption.user_input || "",
          response: val.response || "",
          reference: caseOption.reference || "",
          remark: val.remark || "",
          score: Number(val.score) || 0,
          project_id: project_id,
          version: version || "",
          suite_id: suite_id || 0,
          test_case_id: caseOption.value || 0
        };
      });

      return combinedData;
    },
    suiteRagTestShow() {
      this.executeRagTestShow = true;

      // 清除上一次的数据
      this.clearExecuteRagHumanData();

      if(Object.keys(this.suiteEvaluateData).length > 0){

        // 设置用例集信息
        this.executeRagSuiteName = this.suiteEvaluateData.name || '未知用例集';

        // 设置表单数据
        this.executeRagFormdata.suite_id = this.suiteEvaluateData.id;
        this.executeRagFormdata.project_id = this.suiteEvaluateData.project_id;

        // 主动加载执行用例数据
        if (this.suiteEvaluateData.id) {
          this.getSuiteExecute(this.suiteEvaluateData.id);
        }

        // 重置表单字段
        this.executeRagFormdata.model_id = null;
        this.executeRagFormdata.embedding_model_id = null;
        this.executeRagFormdata.suite_sequence_id = null;

        // 重置状态
        this.executeRagOptionspg1 = true;
        this.executeRagRengongval = false;
        this.executeRagValue90 = false;
      }

      this.suiteEvaluateDialog = false;
    },

    suiteLlmTestShow() {
      this.executeLlmTestShow = true;

      // 清除上一次的数据
      this.clearExecuteLlmHumanData();

      if(Object.keys(this.suiteEvaluateData).length > 0){

        // 设置用例集信息
        this.executeLlmSuiteName = this.suiteEvaluateData.name || '未知用例集';

        // 设置表单数据
        this.executeLlmFormData.suite_id = this.suiteEvaluateData.id;

        // 主动加载执行用例数据
        if (this.suiteEvaluateData.id) {
          this.getSuiteExecute(this.suiteEvaluateData.id);
        }

        // 重置表单字段
        this.executeLlmFormData.type_id = null;

        this.executeLlmFormData.suite_sequence_id = null;

        // 重置状态
        this.executeLlmOptionspg1 = true;
        this.executeLlmRengongval = false;
        this.executeLlmValue90 = false;
      }

      this.suiteEvaluateDialog = false;
    },

    handleEdit(val) {

      this.editDialogVisible = true;
      this.ruleFormEdit = Object.assign({}, val);
    },

    postaddSuite() {

      let params = {
        name: this.ruleForm.name,
        version: this.ruleForm.version,
        suite_type: this.ruleForm.suite_type,
        requirement: this.ruleForm.requirement,
        material: this.ruleForm.material,
        description: this.ruleForm.description,
      };


      addSuite(params).then((res) => {
        this.addDialogVisible = false;
        // 刷新列表
        this.requestSuiteInProject();
      });
    },
    EvaluateStatus(val) {
      // let id=3
      let id = val;
      this.pgStatu = true;
      this.pgcard = false;

      getEvaluateStatus(val).then((res) => {
        this.ragStatus = res.resp;
        this.test001dialog = true;

        const labelMap = {
          requirement_count: "需求个数",
          suite_count: "测试套件个数",
          test_case_count: "测试用例个数",
          context_precision: "上下文查准率",
          context_recall: "上下文召回率",
          context_entity_recall: "上下文实体召回率",
          faithfulness: "忠实度",
          answer_relevancy: "答案相关性",
          // 可根据实际返回的字段扩展其他映射
        };

        this.ragStatus.results = res.resp.results.map((item) => ({
          ...item, // 保留原有字段
          material: {
            // 新增material对象
            answer_relevancy: {
              label: labelMap.answer_relevancy,
              value: item.answer_relevancy,
            },
            context_entity_recall: {
              label: labelMap.context_entity_recall,
              value: item.context_entity_recall,
            },
            context_precision: {
              label: labelMap.context_precision,
              value: item.context_precision,
            },
            context_recall: {
              label: labelMap.context_recall,
              value: item.context_recall,
            },
          },
        }));
      });
    },
    handleClickStatus(val) {
      this.pgStatu = true;
      this.pgcard = false;
      this.llmStatusDialog = true;
      this.llmcard = false;
      this.requestLlmEvaluate();
    },

    ragchecked4() {

      this.executeSuiteShow=false,
      this.llmTestShow = true;
      this.executeProject=true
      this.get_project_id=null;
      this.suitePlaceholder='请选择用例集';
      this.suiteDisabled=false;
      this.llmProject= true;
      this.llmSuite= true;
      this.llmSequenceinput=false
      this.llmFormData.suite_sequence_id=null
      this.get_llm_suite_id=null
      this.manualForm={}
      this.value90 = false;
      this.optionspg1 = true;
      this.rengongval = false;

      // 清除人工评估相关数据
      this.clearHumanEvaluationData();
      this.llmFormData.type_id =null
      this.SequenceData={}
      this.formLabelAlign3Options={}

    },
    handleClose111() {
      this.checked3 = false;
      this.checked4 = false;
      this.llmTestShow = false;
      this.ragTestShow = false;
      this.get_llm_suite_id=null
    },
    value90change(val) {

      this.SequenceData={}
      this.formLabelAlign3Options={}



       if (this.suiteEvaluateData.id && this.suiteProjectList.length > 0) {

        const firstLevelId = this.findFirstLevelId(this.suiteProjectList, this.suiteEvaluateData.name);
      }
      if (val) {
        this.optionspg1 = false;
        // this.llmoptionspg1 = false;
        this.rengongval = true;
      } else {
        this.optionspg1 = true;
        this.rengongval = false;
      }
    },

 findFirstLevelId(projectList, targetSuiteName) {


  if (!projectList || !Array.isArray(projectList) || projectList.length === 0) {
    return null;
  }

  if (!targetSuiteName) {
    return null;
  }

  for (const project of projectList) {

    // 检查项目下的需求
    if (project.children && Array.isArray(project.children) && project.children.length > 0) {

      for (const requirement of project.children) {

        // 检查需求下的用例集
        if (requirement.children && Array.isArray(requirement.children) && requirement.children.length > 0) {

          for (const suite of requirement.children) {


            // 支持多种匹配方式
            if (suite.label === targetSuiteName ||
                suite.name === targetSuiteName ||
                suite.id === targetSuiteName ||
                String(suite.id) === String(targetSuiteName)) {

              return project.id;
            }
          }
        }
      }
    }
  }

  return null; // 未找到匹配的用例集
},

    ragchecked3(val) {

      this.executeSuiteShow=false,
      this.ragTestShow = true;
      this.executeProject=true
      this.get_project_id=null;
      this.suitePlaceholder='请选择用例集';
      this.suiteDisabled=false;
      this.ragProject= true;
      this.ragSuite= true;
      this.ragSequenceinput=false

      // 清空 RAG 表单数据
      this.ragFormdata = {
        type_id: 1,
        embedding_model_id: null,
        suite_sequence_id: null,
        model_id: null,
        project_id: null,
        suite_id: null,
      };

      this.get_rag_suite_id=null
      this.manualForm={}
      this.value90 = false;
      this.optionspg1 = true;
      this.rengongval = false;

      // 清除人工评估相关数据
      this.clearHumanEvaluationData();
      this.SequenceData={}
      this.formLabelAlign3Options={}


    },

    llmchecked3(val) {
      this.executeSuiteShow = false;
      this.llmTestShow = true;
      this.executeProject = true;
      this.suitePlaceholder = '请选择用例集';
      this.suiteDisabled = false;
      this.llmProject = true;
      this.llmSuite = true;
      this.llmSequenceinput = false;

      // 清空 LLM 表单数据
      this.llmFormData = {
        type_id: null,
        model_id: null,
        suite_sequence_id: null,
        project_id: null,
        suite_id: null,
      };

      // 清空旧的变量（为了兼容性）
      this.get_project_id = null;
      this.get_llm_suite_id = null;

      this.manualForm = {};
      this.value90 = false;
      this.optionspg1 = true;
      this.rengongval = false;

      // 清除人工评估相关数据
      this.clearHumanEvaluationData();
      this.SequenceData = [];
      this.formLabelAlign3Options = [];
    },

    // 新增：清除人工评估数据的方法
    clearHumanEvaluationData() {


      // 清除人工评估开关状态
      this.value90 = false;
      this.rengongval = false;
      this.optionspg1 = true;

      // 清除人工评估表单数据
      this.manualForm = {
        project_id: null,
        test_case_id: [],
        suite_id: null,
        remark: '',
        score: null,
        response: '',
        reference: null,
        user_input: null,
        version: null,
      };

      // 清除用例选项数据
      this.caseOptions = [];

      // 清除用例集相关数据（这是关键！）
      this.suiteEvaluateData = {};
      this.suite_sequence_input = '';

      // 清除项目选择状态
      this.manual_project = false;

      // 清除人工评估弹窗状态
      this.dialoghumanEvaluation = false;

      // 清除执行评估相关数据（这些是从"执行>评估>开启人工评估"来的数据）
      this.SequenceData = {};
      this.formLabelAlign3Options = {};

      // 清除序列长度
      this.Sequencelength = 0;

      // 清除执行成功状态
      this.ExecuteSuccess = false;

      // 清除评估弹窗状态
      this.suiteEvaluateDialog = false;

    },

    // 新增：执行RAG评估相关方法
    executeRagEvaluate() {


      // 这里调用RAG评估接口
      // ragEvaluate(this.executeRagFormdata).then((res) => {
      //   if (res.success) {
      //     ElMessage.success('RAG评估开始执行');
      //     this.executeRagTestShow = false;
      //   }
      // });

      ElMessage.success('RAG评估功能开发中...');
    },

    executeRagValue90change(val) {

      if (val) {
        this.executeRagOptionspg1 = false;
        this.executeRagRengongval = true;

        // 清除上一次的数据
        this.clearExecuteRagHumanData();

        // 设置人工评估表单的用例执行值
        if (this.executeRagFormdata.suite_sequence_id) {
          this.executeManualForm.test_case_id = this.executeRagFormdata.suite_sequence_id;
        }

        // 设置其他必要的表单数据
        this.executeManualForm.suite_id = this.executeRagFormdata.suite_id;
        this.executeManualForm.project_id = this.executeRagFormdata.project_id;

        // 调用 materialDialog 接口获取 content_answer 和 content_question
        if (this.executeRagFormdata.suite_id) {
          this.executeRagMaterialDialog(this.executeRagFormdata.suite_id);
        }

      } else {
        this.executeRagOptionspg1 = true;
        this.executeRagRengongval = false;

        // 清空人工评估表单数据
        this.clearExecuteRagHumanData();
      }
    },

    // 新增：清除执行RAG人工评估数据的方法
    clearExecuteRagHumanData() {

      // 清空人工评估表单数据
      this.executeManualForm = {
        project_id: null,
        test_case_id: null,
        suite_id: null,
        remark: null,
        score: null,
        response: null,
        reference: null,
        user_input: null,
        version: null,
      };

      // 清空执行RAG相关的 caseOptions 数据
      this.executeRagCaseOptions = [];

    },

    // 新增：执行RAG评估专用的 materialDialog 方法
    executeRagMaterialDialog(suite_id) {

      let params = {
        suite_id: suite_id,
        page: 1,
        page_size: 10,
      };

      suiteCase({ ...params }).then((res) => {

        // 为执行RAG评估创建专用的 caseOptions
        this.executeRagCaseOptions = res.resp.map((item) => {
          const caseOption = {
            user_input: item.content_answer || "",    // content_answer 传递给 user_input
            reference: item.content_question || "",   // content_question 传递给 reference
            label: item.content,
            value: item.id,
          };
          return caseOption;
        });


      }).catch((error) => {
        console.error('执行RAG评估 materialDialog 接口调用失败:', error);
      });
    },

    // 新增：执行LLM评估相关方法
    executeLlmEvaluate() {

      // 这里调用LLM评估接口
      // llmEvaluate(this.executeLlmFormData).then((res) => {
      //   if (res.success) {
      //     ElMessage.success('LLM评估开始执行');
      //     this.executeLlmTestShow = false;
      //   }
      // });

      ElMessage.success('LLM评估功能开发中...');
    },

    executeLlmValue90change(val) {

      if (val) {
        this.executeLlmOptionspg1 = false;
        this.executeLlmRengongval = true;

        // 清除上一次的数据
        this.clearExecuteLlmHumanData();

        // 设置人工评估表单的用例执行值
        if (this.executeLlmFormData.suite_sequence_id) {
          this.executeManualForm.test_case_id = this.executeLlmFormData.suite_sequence_id;
        }

        // 设置其他必要的表单数据
        this.executeManualForm.suite_id = this.executeLlmFormData.suite_id;
        this.executeManualForm.project_id = this.executeLlmFormData.project_id;

        // 调用 materialDialog 接口获取 content_answer 和 content_question
        if (this.executeLlmFormData.suite_id) {
          this.executeLlmMaterialDialog(this.executeLlmFormData.suite_id);
        }

      } else {
        this.executeLlmOptionspg1 = true;
        this.executeLlmRengongval = false;

        // 清空人工评估表单数据
        this.clearExecuteLlmHumanData();
      }
    },

    // 新增：清除执行LLM人工评估数据的方法
    clearExecuteLlmHumanData() {

      // 清空人工评估表单数据
      this.executeManualForm = {
        project_id: null,
        test_case_id: null,
        suite_id: null,
        remark: null,
        score: null,
        response: null,
        reference: null,
        user_input: null,
        version: null,
      };

      // 清空执行LLM相关的 caseOptions 数据
      this.executeLlmCaseOptions = [];

    },

    // 新增：执行LLM评估专用的 materialDialog 方法
    executeLlmMaterialDialog(suite_id) {

      let params = {
        suite_id: suite_id,
        page: 1,
        page_size: 10,
      };

      suiteCase({ ...params }).then((res) => {

        // 为执行LLM评估创建专用的 caseOptions
        this.executeLlmCaseOptions = res.resp.map((item) => {
          const caseOption = {
            user_input: item.content_answer || "",    // content_answer 传递给 user_input
            reference: item.content_question || "",   // content_question 传递给 reference
            label: item.content,
            value: item.id,
          };
          return caseOption;
        });


      }).catch((error) => {
        console.error('执行LLM评估 materialDialog 接口调用失败:', error);
      });
    },



    // 新增：执行评估的人工评估提交方法（统一处理RAG和LLM）
    executePostManualDate(val) {


      // 判断当前是RAG评估还是LLM评估
      const isRagEvaluation = this.executeRagTestShow;
      const isLlmEvaluation = this.executeLlmTestShow;


      // 根据评估类型选择对应的数据源和方法
      let caseOptions, formData, materialDialogMethod;

      if (isRagEvaluation) {
        caseOptions = this.executeRagCaseOptions;
        formData = this.executeRagFormdata;
        materialDialogMethod = () => this.executeRagMaterialDialog(formData.suite_id);
      } else if (isLlmEvaluation) {
        caseOptions = this.executeLlmCaseOptions;
        formData = this.executeLlmFormData;
        materialDialogMethod = () => this.executeLlmMaterialDialog(formData.suite_id);
      } else {
        console.error('无法确定当前评估类型');
        return;
      }

      // 检查 caseOptions 是否已加载
      if (!caseOptions || caseOptions.length === 0) {
        console.warn('caseOptions 数据未加载，重新调用 materialDialog 接口');
        if (formData.suite_id) {
          materialDialogMethod();
          // 延迟提交，等待数据加载
          setTimeout(() => {
            this.executePostManualDate(val);
          }, 1000);
          return;
        }
      }

      // 获取项目ID
      let project_id = 0;
      if (this.suiteEvaluateData.id && this.suiteProjectList.length > 0) {
        project_id = this.findFirstLevelId(this.suiteProjectList, this.suiteEvaluateData.name) || 0;
      }

      // 获取test_case_id，可能是单个值或数组
      let testCaseIds = [];
      if (val.test_case_id) {
        testCaseIds = Array.isArray(val.test_case_id) ? val.test_case_id : [val.test_case_id];
      } else if (formData.suite_sequence_id) {
        testCaseIds = Array.isArray(formData.suite_sequence_id)
          ? formData.suite_sequence_id
          : [formData.suite_sequence_id];
      }


      // 如果没有test_case_id，创建单个记录
      if (testCaseIds.length === 0) {
        testCaseIds = [0];
      }

      // 为每个test_case_id创建一个独立的记录，使用 materialDialog 获取的数据
      const requestData = testCaseIds.map(testCaseId => {

        // 查找对应的 caseOption 数据
        const caseOption = caseOptions.find(option => {
          return option.value == testCaseId; // 使用 == 而不是 === 来处理类型转换
        });


        // 如果没有找到对应的 caseOption，使用第一个可用的数据
        const fallbackOption = caseOption || (caseOptions.length > 0 ? caseOptions[0] : null);

        const recordData = {
          user_input: fallbackOption ? fallbackOption.user_input : "",     // 来自 content_answer
          response: val.response || "",                                     // 用户填写的模型回答
          reference: fallbackOption ? fallbackOption.reference : "",       // 来自 content_question
          remark: val.remark || "",                                        // 用户填写的备注
          score: Number(val.score) || 0,                                   // 用户填写的得分
          project_id: project_id,
          version: this.suiteEvaluateData.version || "",
          suite_id: this.suiteEvaluateData.id || 0,
          test_case_id: Number(testCaseId) || 0
        };

        return recordData;
      });


      // 打开独立的人工评估弹窗
      this.dialoghumanEvaluation = true;
      this.executeRagTestShow = false;
      this.executeLlmTestShow = false;

      // 使用拆分后的数组格式数据调用接口
      addManualDate(requestData).then((res) => {
        if (res.success) {
          ElMessage.success('人工评估提交成功');
        }
      }).catch((error) => {
        console.error('执行评估提交失败:', error);
        ElMessage.error('人工评估提交失败');
      });
    },

    savepg022() {
      if (this.Sequencelength > 0||Object.keys(this.suiteEvaluateData).length>0) {
        this.pgcard = true;
        this.ragTestShow = false;
        this.checked3 = false;
        this.requestRagEvaluate();
      } else {

        ElMessage.error("用例集合下暂无执行数据，请先执行再进行评估！");
      }
    },

    getSuiteid(val) {
if(val!= null){
  let params = {
    project_id: val,
    // project_id: val ? val : 8,
    page: 1,
    page_size: 100,
  };
  suiteInProject({ ...params }).then((res) => {
    if(res.resp.length>0){
      this.formLabelAlign3Options = res.resp.map((item) => {
        return {
          label: item.name,
          value: item.id,
        };
      });
    }else{
      ElMessage.warning('当前项目下无用例集！')
    }

  });
}else{
  ElMessage.error('请先选择项目！')
}

    },
    handleCurrentChange(page) {
      this.suite_page = page;
      this.currentPage = page;
      this.requestSuiteInProject();
    },

    handleSizeChange(size) {
      this.pageSize = size;
      this.suite_page = 1; // 重置到第一页
      this.currentPage = 1;
      this.requestSuiteInProject();
    },

    //suiteInRequirement

    async requestSuiteInProject(val) {
      // if (this.projectId != null && this.RequireId != null) {
      //   ElMessage.error("请选择项目或者需求其中一项进行查询！");
      // }



      let params = {
        requirement_id: val ? val : 1,
        page: this.suite_page,
        page_size: this.pageSize,
      };

      await suiteInRequirement({ ...params }).then((res) => {
        this.Case = res.resp.map((item) => {
          if (item.suite_type == 0) {
            item.suite_type = "RAGS";
          } else {
            item.suite_type = "LLM";
          }
          return {
            ...item,
            // requirement__name:item.project__name+'/'+item.requirement__name,
            create_time: dayjs(item.create_time).format("YYYY-MM-DD HH:mm:ss"),
            update_time: dayjs(item.update_time).format("YYYY-MM-DD HH:mm:ss"),
          };
        });
        this.suite_total = res.total;
      });
    },

    getSuiteExecute(val) {
      let params = {
        suite_id: val,
      };

      getResultSequence(params).then((res) => {
        this.Sequencelength = res.resp.length;
        if (this.Sequencelength == 0) {
          ElMessage.error("用例集合下暂无执行数据，请先执行再进行评估！");
        }
        this.SequenceData = res.resp.map((item) => {
          return {
            label: item.suite__name,
            value: item.id,
            num: item.sequence_num,
          };
        });
      });
    },

    materialDialog(row) {
      let params = {
        suite_id: row.id ? row.id : row,
        page: 1,
        page_size: 10,
      };
      if (row.id) {
        this.dialogMaterialVisible = true;
      }

      suiteCase({ ...params }).then((res) => {
        this.casePlaceholder=res.resp[0].content
        this.materialTableData = res.resp.map((item) => {
          return {
            ...item,
            create_time: dayjs(item.create_time).format("YYYY-MM-DD HH:mm:ss"),
            update_time: dayjs(item.update_time).format("YYYY-MM-DD HH:mm:ss"),
          };
        });
        this.caseOptions = res.resp.map((item) => {
          return {
            user_input:item.content_answer,
            reference:item.content_question,
            label: item.content,
            value: item.id,
          };
        });
      });
    },
    openpgDialog(val){
       this.pgDialogVisible = true;
    },
    requestRagEvaluate() {
      this.pgcard=true;
      this.ragTestShow=false


      ragEvaluate(this.ragFormdata).then((res) => {
        this.rag_task_id=res.resp.task_id
        // this.ragResp.push(res.resp);
        // this.newRagResp = this.ragResp.map((item) => {
        //   return {
        //     ...item,
        //     start_time: dayjs(item.start_time).format("YYYY-MM-DD HH:mm:ss"),
        //   };
        // });
        this.rag_start_time = dayjs(res.resp.start_time).format("YYYY-MM-DD HH:mm:ss")
        this.rag_status = res.resp.status
         ElMessage({
    showClose: true,
    type: 'success',
    message: res.resp.message,
  })
      });
    },

    postllmEvaluate() {


      llmEvaluate(this.llmFormData).then((res) => {
        let aaa = [];
        aaa.push(res.resp);
        this.newLlmResp = aaa.map((item) => {
          return {
            ...item,
            // requirement__name:item.project__name+'/'+item.requirement__name,
            start_time: dayjs(item.start_time).format("YYYY-MM-DD HH:mm:ss"),
          };
        });

        this.llm_task_id = this.newLlmResp[0].task_id;
        this.llm_start_time = this.newLlmResp[0].start_time;
        this.llm_status = this.newLlmResp[0].status;
      });
    },
    requestLlmEvaluate() {
      let data = {};

      // llmEvaluate(this.llmFormData).then((res) => {
      llmEvaluateDetail(3).then((res) => {
        this.llmRespDetail = res.resp;

        this.llmRespDetailConversation = res.resp.conversation;

        const fixedQuotes = this.llmRespDetailConversation.replace(/'/g, '"');

        // 2. 解析为JSON数组
        const parsedArray = JSON.parse(fixedQuotes);

        // 3. 恢复content字段中的换行符
        this.llmRespDetailConversationArray = parsedArray.map((item) => {
          if (item.content && typeof item.content === "string") {
            return {
              ...item,
              content: item.content.replace(/\\n/g, "\n"),
            };
          }
          return item;
        });

        // 最终结果
      });
    },

    getProjectId() {
      let params = {
        page: 1,
        page_size: 100,
      };
      serviceList(params).then((res) => {
        this.projectData = res.resp.map((item) => {
          return {
            label: item.name,
            value: item.id,
          };
        });
        // 新增：项目数据获取完成后，执行需求列表请求
      });
    },
    getrequireList(val) {
      //this.projectDataId
      requireList(val).then((res) => {
        this.requirement_list = res.resp.map((item) => {
          return {
            label: item.name,
            value: item.id,
          };
        });
      });
    },
    getmaterialList(val) {
      let params = {
        // project_id:this.projectDataId,
        project_id: val,
      };
      if (this.suiteItemLength > 0) {
        userMaterialList(params).then((res) => {
          this.materialListData = res.resp.map((item) => {
            return {
              label: item.content,
              value: item.id,
            };
          });
        });
      } else {
        ElMessage.error({
          message: `请选择其他项目!`,
          type: "error",
          center: true,
        });
      }
    },
    submitEditForm() {
      let params = {
        name: this.ruleFormEdit.name,
        version: this.ruleFormEdit.version,
        suite_type: this.ruleFormEdit.suite_type,
        requirement: this.editOptionsId,
        material: [this.materialListDataId],
        description: this.ruleFormEdit.description,
        id: this.ruleFormEdit.id,
      };
      updateSuite(params).then((res) => {
        this.editDialogVisible = false;
        this.requestSuiteInProject();
      });
    },
    async delSuiteDate(val) {
      try {
        await ElMessageBox.confirm(
          `确定要删除用例集 "${val.name || '此项'}" 吗？此操作不可撤销。`,
          '删除确认',
          {
            confirmButtonText: '确定删除',
            cancelButtonText: '取消',
            type: 'warning',
            confirmButtonClass: 'el-button--danger',
          }
        );



        // 执行删除
        const res = await delSuite({ id: val.id });
        if (res.success !== false) {
          ElMessage.success('删除成功');
          this.requestSuiteInProject(val.requirement_id);
        } else {
          ElMessage.error(res.msg || '删除失败');
        }
      } catch (error) {
        if (error !== 'cancel') {
          ElMessage.error('删除操作失败');
        }
      }
    },
    async suiteExecute(val) {
      this.ExecuteSuccess = true;
      this.suiteEvaluateData=val
      const suiteId = val.id;

      const resp = await postSuiteExecute(suiteId);

      if (resp.success) {

        this.suiteEvaluateData=val
        ElMessage({
          message: "用例集执行成功,可在任务列表>执行列表，查看执行结果",
          type: "success",
        });

      } else {
        ElNotification({
          title: "失败",
          message: "用例集执行失败,可在任务列表>执行列表，查看执行结果",
          type: "error",
        });
      }
    },

    // RAG状态映射方法
    getRagStatusText(status) {
      const statusMap = {
        1: '待执行',
        2: '执行中',
        3: '已完成',
        4: '执行失败',
        5: '执行超时'
      };
      return statusMap[status] || '未知状态';
    },

    // RAG状态类型映射方法（用于标签颜色）
    getRagStatusType(status) {
      const typeMap = {
        1: 'info',      // 待执行 - 灰色
        2: 'warning',   // 执行中 - 橙色
        3: 'success',   // 已完成 - 绿色
        4: 'danger',    // 执行失败 - 红色
        5: 'danger'     // 执行超时 - 红色
      };
      return typeMap[status] || 'info';
    },
  },
});
