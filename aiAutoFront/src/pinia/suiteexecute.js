import { ref} from "vue";
import dayjs from 'dayjs';
import { defineStore } from 'pinia'
import{suiteInProject,getHierarchicalStructure,getResultDetail,getUserLimitResultSequence} from '@/api/suitApi'
import{serviceList} from '@/api/projectApi'

export const suiteExecuteStore = defineStore('suiteexecutstore', {
  state: () => ({
    detailData:[],
    detailDialogVisible:false,
    projectvalue:null,
    suitevalue:null,
    projectoptions:[],
    suiteoptions:[],
    sequenceCurrentPage :1,
    pageSize :5,
    sequenceTotal:0,
    sequencePageSize:20,
    sequenceID:0,
    sequencePageCount:0,

    // 表格数据（示例数据，实际应通过API获取）
    tableData :[],

    // 级联选择-选项
    suiteSelectOptions:[],

    // 级联选择-选项
    suiteSelected:[],

    // 及联选择-多选
    props: { multiple: true },

    // 查询数据处理分类
    filterProject: [],
    filterRequirement: [],
    filterSuite: [],

    // 详情分页
    detailTotal:0,
    detailCurrentPage:1,
    detailPageSize:10,
    detailTitle:"详情",
    detailPageCount:0,
  }),

  actions: {
    getProjectList() {
      let params = {
        page: 1,
        page_size: 100
      };

      //请求物料接口
      serviceList({...params}).then((res) => {
        this.projectoptions=res.resp.map(item => {
          return {
            label:item.name,
            value:item.id
          }
        })
      });
    },

    getSuiteList() {
      let params = {
        project_id: this.projectvalue?this.projectvalue:8,
      };

      //请求物料接口
      suiteInProject(params).then((res) => {
        this.suiteoptions=res.resp.map(item => {
          return {
          label:item.name,
          value:item.id
          }
        })
      });
    },

    handleSequenceCurrentChange(val){
      this.sequenceCurrentPage=val
      this.getSequenceList()
    },

    handleSequenceSizeChange(val){
      this.sequencePageSize=val
      this.getSequenceList()
    },

    handleDetailCurrentChange(val){
      this.detailCurrentPage=val
      this.getSequenceDetail()
    },

    handleDetailSizeChange(val){
      this.detailPageSize=val
      this.getSequenceDetail()
    },

    async getSequenceList() {
      let params = {
        "suite_ids": this.filterSuite,
        "req_ids": this.filterRequirement,
        "project_ids": this.filterProject,
        "page": this.sequenceCurrentPage,
        "page_size": this.sequencePageSize,
      };

      await getUserLimitResultSequence(params).then((res) => {
        this.tableData=res.resp;
        this.sequenceTotal=res.total
        this.sequencePageCount=res.total_page
      });
    },

    // 执行状态文本映射
    getSuiteType(state)  {
      const map = { 0: 'RAGS', 1: 'LLM' };
      return map[state] || '未知';
    },

    timeFormat(time){
      return dayjs(time).format('YYYY-MM-DD HH:mm:ss')
    },

    openSequenceDetail(row){
      this.sequenceID=row.id
      this.detailTitle = row.suite__name
        + ' / '+  this.getSuiteType(row.suite__suite_type)
        + ' / ' + this.timeFormat(row.exec_start_time)
      this.getSequenceDetail()
    },

    async getSequenceDetail(){
      let params = {
        "sequence_id": this.sequenceID,
        "page": this.detailCurrentPage,
        "page_size": this.detailPageSize
      };

      await getResultDetail(params).then((res) => {
        this.detailDialogVisible=true
        this.detailData=res.resp;
        this.detailTotal=res.total
        this.detailPageCount=res.total_page
      });
    },

    //获取项目-需求-用例集结构
    async getAllHierarchicalStructure(){
      let params = {}

      await getHierarchicalStructure(params).then((res) => {
        this.suiteSelectOptions = res.resp
      });
    },

    //查询数据
    filterSequence(){
      this.suiteSelected.forEach(elment =>{
        if(elment.length===1){
          this.filterProject.push(elment[0])
        }

        if(elment.length===2){
          this.filterRequirement.push(elment[1])
        }

        if(elment.length===3){
          this.filterSuite.push(elment[2])
        }
      })

      this.getSequenceList()
    },

  // 时间格式化方法
    formatTime (row, column) {
      const time = row[column.property];
      return time ? dayjs(time).format('YYYY-MM-DD HH:mm:ss') : '--';
    },

    // 执行状态文本映射
    getStatusText  (state)  {
      const map = { 0: '执行中', 1: '已完成' };
      return map[state] || '未知';
    },

    // 执行状态标签样式映射
    getStatusType  (state) {
      const map = { 0: 'primary', 1: 'success'};
      return map[state] || 'default';
    },


    // 重置按钮事件
    handleReset () {
      this.suiteSelectClear()
    },

    // 级联多选清空选项
    suiteSelectClear(){
      this.suiteSelected = [];
      this.filterSuite = [];
      this.filterRequirement = [];
      this.filterProject = [];
      this.getSequenceList()
    },

    // 分页切换事件
    handlePageChange (page)  {
      queryParams.value.page = page;
      handleQuery();
    },

    detailHandleClose(){
      this.detailDialogVisible=false
      this.detailCurrentPage=1
      this.detailPageSize=10
    },

      // 查看详情事件（示例）
    handleViewDetail (row) {
    }
  }
})