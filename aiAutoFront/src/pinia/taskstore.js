import { defineStore } from 'pinia'
import { taskList,ragRetry } from '../api/rag'
import { ElMessage } from 'element-plus'
import dayjs from 'dayjs'

export const useCounterStore = defineStore('TaskList', {
  state: () => ({
    taskListData: [], // 存储接口返回的任务列表数据
    pollingTimer: null, // 存储定时器ID，用于清除定时任务
    TaskListTotal:null,
    TaskListPage:1,
    suiteValue:null,
    filterStatusvalue:null
  }),

  actions: {
    postragRetry(val) {
      ragRetry(val.id).then((res) => {
        ElMessage.warning(`评估重试：${res.msg}`)

      })
    },

     handleCurrentChange(val) {
       this.TaskListPage=val;
       this.fetchTaskList();
     },

    // 核心：获取任务列表的接口调用方法
    async fetchTaskList() {
      try {
        let params = {
          page: this.TaskListPage ,
          size: 20,
          suite_sequence_id:this.suiteValue,
          status:this.filterStatusvalue

        }
        console.log('请求了-----');

        const res = await taskList({...params}) // 调用接口（假设taskList是已封装的axios请求函数）
        if (res.success) {
          // 处理时间格式（示例）
          this.taskListData = res.resp.map(item => ({
            ...item,
            createTime: dayjs(item.createTime).format('YYYY-MM-DD HH:mm:ss')
          }))
          this.TaskListTotal=res.total;
        }
      } catch (error) {
        console.error('获取任务列表失败:', error)
      }
    },

    // 启动定时刷新（间隔5秒）
    startPolling(interval = 2000) {
      // 避免重复创建定时器
      if (this.pollingTimer) return
      // 立即执行一次获取数据
      this.fetchTaskList()
      // 设置定时器
      this.pollingTimer = setInterval(() => {
        this.fetchTaskList()
      }, interval)
    },

    // 停止定时刷新（建议在组件卸载时调用）
    stopPolling() {
      if (this.pollingTimer) {
        clearInterval(this.pollingTimer)
        this.pollingTimer = null
      }
    }
  }
})