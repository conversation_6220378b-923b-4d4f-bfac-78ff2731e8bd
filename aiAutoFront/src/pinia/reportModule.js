import { ref } from "vue";
import dayjs from "dayjs";
import { defineStore } from "pinia";
import {
  ragReportStatistics,
  ragReportStats,
  ragReport,
  ragCompareResult,
  llmReportStatistics,
  llmReportStats,
  llmReport,
  llmCompareResult,
} from "@/api/reports";
import * as echarts from "echarts";
import { serviceList } from "@/api/projectApi";
import { suiteInProject, getHierarchicalStructure } from "@/api/suitApi";
import { requireList1 } from "@/api/requireApi";
import { ElMessage } from "element-plus";

export const reportStore = defineStore("reportstore", {
  state: () => ({


llm_stats_project_list_id:null,
    suiteValue:null,
    llm_project_list_id: null,
    llm_suite_id: null,
    llm_requirement_id: null,
    llm_input_version: null,
    selectedAnalysisMetric: null,
    analysisMetrics: [
      { label: "上下文查准率", value: "context_precision" },
      { label: "上下文召回率", value: "context_recall" },
      { label: "上下文实体召回率", value: "context_entity_recall" },
      { label: "忠实度", value: "faithfulness" },
      { label: "答案相关性", value: "answer_relevancy" },
    ],
    detailDialogVisible: false,
    currentDetail: [],

  ragCheckOPtionsValue:null,
    llmCheckOPtionsValue:null,

    input_version: "",
    llm_input_version:"",
    suite_list: [],
    require_list: [],
    requirement_id: null,
    suite_id: null,
    llm_metric_stats: [],
    llm_report_data: [],
    execution_times_before: null,
    execution_times_after: null,
    suite_list_id: null,

    project_list_id: null,
    project_list: [],
    rag_collect: {},
    llm_collect: {},
    rag_collect_data: [],
    rag_metric: null,
    rag_metric_label: [],
    llm_metric_label: [],
    rag_metric_value: [],
    rag_metric_stats: [],
    compare_result_data1: [],
    compare_result_data2: [],
    compare_result_data: [],
    llm_compare_result_data1: [],
    llm_compare_result_data2: [],
    llm_compare_result_data: [],
    compare_after_value: [],
    compare_before_value: [],
    compare_field: [],
    llm_compare_after_value: [],
    llm_compare_before_value: [],
    llm_compare_field: [],
    llm_project_id: null,
    llm_metric: null,
    rag_report: null,
    ragprops : {
  checkStrictly: true
},
ragCheckOPtions: [],
  }),
  getters: {},
  actions: {
    handleRagCheckChange(){

       this.project_list_id =  this.ragCheckOPtionsValue[0] ? this.ragCheckOPtionsValue[0] : 8,
       this.requirement_id  =  this.ragCheckOPtionsValue[1] ? this.ragCheckOPtionsValue[1]  : null,
        this.input_version ? this.input_version : null,
       this.suite_id  =  this.ragCheckOPtionsValue[2] ? this.ragCheckOPtionsValue[2] : null
         this.getRagReportStatistics('trendChart11'); // 传递折线图容器ID
        this.getRagReportStats('analysisPie');      // 传递分析饼图容器ID

    },
    handleLlmCheckChange(){


       this.llm_stats_project_list_id =  this.llmCheckOPtionsValue[0] ? this.llmCheckOPtionsValue[0] : 8,
       this.llm_requirement_id  =  this.llmCheckOPtionsValue[1] ? this.llmCheckOPtionsValue[1]  : null,
        this.llm_input_version ? this.llm_input_version : null,
       this.llm_suite_id  =  this.llmCheckOPtionsValue[2] ? this.llmCheckOPtionsValue[2] : null

        this.getllmReportStats('llmAnalysisPie')
        this.getllmReportStatistics('trendChart32')

    },

    handleAnalysisMetricChange() {
      this.getRagReportStats("analysisPie");
    },
    //获取需求
    getRequireList(val) {
      let queryParams = {
        page: 1,
        page_size: 100,
      };
      if (val != null) {
        requireList1(val, queryParams).then((res) => {
          this.require_list = res.resp.map((item) => {
            return {
              value: item.id,
              label: item.name,
            };
          });
        });
      } else {
        ElMessage.error("请选择项目！");
      }
    },
    //获取用例集
    getSuiteInProject() {
      let params = {
        project_id: this.project_list_id? this.project_list_id : 8,
      };
      suiteInProject(params).then((res) => {
        this.suite_list = res.resp.map((item) => {
          return {
            value: item.id,
            label: item.name,
          };
        });
      });

      //   let params = {
      //     project_id:this.project_list_id

      //  }
      //  if(this.project_list_id!=null){

      // }else{
      //     ElMessage.error('请选择项目和需求！')
      //   }
    },

    requestServiceList() {
      //请求项目列表接口
      let params = {
        page: 1,
        page_size: 100,
      };
      serviceList(params).then((res) => {
        this.project_list = res.resp.map((item) => {
          return {
            value: item.id,
            label: item.name,
          };
        });
      });
    },

    // 获取层级结构数据并绑定到ragCheckOPtions
    async getRagHierarchicalStructure() {
      try {
        const res = await getHierarchicalStructure();
        if (res && res.resp) {
          // 将层级结构数据转换为适合树形选择器的格式
          this.ragCheckOPtions = res.resp.map(project => ({
            value: project.id,
            label: project.label,
            children: project.children ? project.children.map(requirement => ({
              value: requirement.id,
              label: requirement.label,
              children: requirement.children ? requirement.children.map(suite => ({
                value: suite.id,
                label: suite.label
              })) : []
            })) : []
          }));
        } else {
          console.warn('获取层级结构数据失败: 响应格式异常');
          ElMessage.warning('获取项目层级数据失败');
        }
      } catch (error) {
        console.error('获取层级结构数据失败:', error);
        ElMessage.error('获取项目层级数据失败，请检查网络连接');
        this.ragCheckOPtions = [];
      }
    },

    async getRagCompareResult(val) {
      let params = {
        suite_id: this.suite_id ? this.suite_id : 23,
        execution_times_after: this.execution_times_after
          ? this.execution_times_after
          : 2,
        execution_times_before: this.execution_times_before
          ? this.execution_times_before
          : 1,
      };
      await ragCompareResult(params).then((res) => {
        if (res.success) {
          this.compare_result_data = res.resp.comparison_results;

          this.compare_after_value = this.compare_result_data.map((item) => {
            this.compare_field.push(item.compare.map((item2) => item2.field));
            return item.compare.map((item2) => item2.after_value);
          });
          this.compare_before_value = this.compare_result_data.map((item) => {
            return item.compare.map((item2) => item2.before_value);
          });

          this.currentDetail = this.compare_result_data.map((item) => ({
            reference: item.reference,
            response: item.response,
            retrieved_contexts: item.retrieved_contexts,
          }));

          // 只有在非列表模式下才生成图表
          if (val !== 'list') {
            this.getfullTrendChart(val);
          }
        } else {
          ElMessage.error(res.msg);
        }
      });
    },
    getfullTrendChart(val) {
      const fullTrendChart = echarts.init(document.getElementById(val));
      fullTrendChart.setOption({
        title: { text: "RAG结果对比" },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "cross",
            label: {
              backgroundColor: "#6a7985",
            },
          },
        },
        legend: {
          data: this.compare_result_data.flatMap((item, index) => [
            `问题${index + 1}本次得分`,
            `问题${index + 1}前一次得分`,
          ]),
        },
        grid: {
          left: "3%",
          right: "4%",
          bottom: "3%",
          containLabel: true,
        },
        xAxis: [
          {
            type: "category",
            boundaryGap: false,
            data: this.compare_field[0],
          },
        ],
        yAxis: [
          {
            type: "value",
          },
        ],
        // 动态生成series（每个对比项生成两条线：before和after）
        series: this.compare_result_data.flatMap((item, index) => [
          {
            name: `问题${index + 1}本次得分`,
            type: "line",
            stack: "对比",
            areaStyle: {},
            emphasis: {
              focus: "series",
            },
            data: this.compare_after_value[index], // 动态获取after_value
            itemStyle: {
              color: ["#468cf2", "#ffb8f5"][index], // 根据index设置不同颜色（可扩展颜色数组）
            },
          },
          {
            name: `问题${index + 1}前一次得分`,
            type: "line",
            stack: "对比",
            areaStyle: {},
            emphasis: {
              focus: "series",
            },
            data: this.compare_before_value[index], // 动态获取before_value
            itemStyle: {
              color: ["#9fd5e5", "#6d5ecf"][index], // 根据index设置不同颜色（可扩展颜色数组）
            },
          },
        ]),
      });
    },

    async getRagReportStats(val) {
      let params = {
        metric: this.selectedAnalysisMetric
          ? this.selectedAnalysisMetric
          : "context_precision",
        project_id: this.project_list_id ? this.project_list_id : 8,
      };


      try {
        const res = await ragReportStats(params);

        if (res && res.success && res.resp && res.resp.stats) {
          this.rag_metric_stats = res.resp.stats;
        } else {
          this.rag_metric_stats = [];
          ElMessage.warning('获取RAG指标统计数据失败');
        }

        this.getanalysisPie(val);
      } catch (error) {
        console.error('RAG指标统计数据请求异常:', error);
        this.rag_metric_stats = [];
        ElMessage.error('获取RAG指标统计数据异常，请检查网络连接');
        this.getanalysisPie(val);
      }
    },
    getanalysisPie(val) {
      let data = [];

      // 检查rag_metric_stats是否存在且为数组
      if (this.rag_metric_stats && Array.isArray(this.rag_metric_stats) && this.rag_metric_stats.length > 0) {
        data = this.rag_metric_stats;
      } else {
        // 如果没有数据，使用默认的空数据结构
        console.warn('RAG指标统计数据为空，使用默认数据');
        data = [
          { value: 0 },
          { value: 0 },
          { value: 0 },
          { value: 0 },
          { value: 0 }
        ];
      }
      // 检查DOM元素是否存在
      const domElement = document.getElementById(val);
      if (!domElement) {
        console.error(`找不到ID为 ${val} 的DOM元素`);
        return;
      }

      const analysisPie = echarts.init(domElement);

      // 确保data数组有足够的元素
      while (data.length < 5) {
        data.push({ value: 0 });
      }

      analysisPie.setOption({
        title: { text: "RAG指标占比", left: "center" },
        tooltip: {
          trigger: "item",
        },
        legend: {
          top: "12%",
          left: "center",
        },
        series: [
          {
            top: "13%",
            // name: 'Access From',
            type: "pie",
            radius: ["40%", "70%"],
            avoidLabelOverlap: false,
            padAngle: 5,
            itemStyle: {
              borderRadius: 10,
            },
            label: {
              show: false,
              position: "center",
            },
            emphasis: {
              label: {
                show: true,
                fontSize: 40,
                fontWeight: "bold",
              },
            },
            labelLine: {
              show: false,
            },
            data: [
              {
                value: (data[0] && data[0].value !== undefined) ? data[0].value : 0,
                name: "小于0.6",
                itemStyle: {
                  color: "#468cf2",
                },
              },
              {
                value: (data[1] && data[1].value !== undefined) ? data[1].value : 0,
                name: "0.6-0.7",
                itemStyle: {
                  color: "#aac4e9",
                },
              },
              {
                value: (data[2] && data[2].value !== undefined) ? data[2].value : 0,
                name: "0.7-0.8",
                itemStyle: {
                  color: "#9fd5e5",
                },
              },
              {
                value: (data[3] && data[3].value !== undefined) ? data[3].value : 0,
                name: "0.8-0.9",
                itemStyle: {
                  color: "#ffb8f5",
                },
              },
              {
                value: (data[4] && data[4].value !== undefined) ? data[4].value : 0,
                name: "大于0.9",
                itemStyle: {
                  color: "#6d5ecf",
                },
              },
            ],
          },
        ],
      });
    },

    async getRagReportStatistics(val) {
      let params = {
        project_id: this.project_list_id ? this.project_list_id : 8,
        requirement_id: this.requirement_id ? this.requirement_id : null,
        version: this.input_version ? this.input_version : null,
        suite_id: this.suite_id ? this.suite_id : null,
      };
      await ragReportStatistics(params).then((res) => {
        if (res.success) {
          const labelMap = {
            requirement_count: "需求个数",
            suite_count: "测试套件个数",
            test_case_count: "测试用例个数",
            context_precision: "上下文查准率",
            context_recall: "上下文召回率",
            context_entity_recall: "上下文实体召回率",
            faithfulness: "忠实度",
            answer_relevancy: "答案相关性",
            // 可根据实际返回的字段扩展其他映射
          };
          this.rag_metric = Object.entries(res.resp.statistics).map(([key, value]) => ({
            label: labelMap[key] || key, // 优先使用中文映射，无匹配时保留原key
            value: value,
          }));
          for (let i = 0; i < this.rag_metric.length; i++) {
            this.rag_metric_label.push(this.rag_metric[i].label);
          }
          this.trendChart(val);
        }
      });
    },

    getRagReport(val) {
      let project_id = this.project_list_id ? this.project_list_id : 8;
      ragReport({ project_id }).then((res) => {
        const { metrics, ...rest } = res[0];
        this.rag_collect = { ...rest, ...metrics };

        // 添加字段到中文的映射表（参考evaluationList.vue中的metric配置）
        const labelMap = {
          requirement_count: "需求个数",
          suite_count: "测试套件个数",
          test_case_count: "测试用例个数",

          context_precision: "上下文查准率",
          context_recall: "上下文召回率",
          context_entity_recall: "上下文实体召回率",
          faithfulness: "忠实度",
          answer_relevancy: "答案相关性",
          // 可根据实际返回的字段扩展其他映射
        };
        // 将 rag_collect 转换为 { label: 中文字段名, value: 字段值 } 的数组，并过滤掉 project_name
        this.rag_collect_data = Object.entries(this.rag_collect)
          .map(([key, value]) => ({
            label: labelMap[key] || key, // 优先使用中文映射，无匹配时保留原key
            value: value,
          }))
          .filter((item) => item.label !== "project_name"); // 过滤条件

        this.getProjectPie(val);
      });
    },
    getProjectPie(val) {
      const projectPie = echarts.init(document.getElementById(val));
      projectPie.setOption({
        title: {
          text: "RAG指标总计",
          left: "center",
          subtext: "0.6以下评分数据占比",
          subtextStyle: {
            color: "#666",
            fontSize: 12
          }
        },
        tooltip: {
          trigger: "item",
        },
        legend: {
          top: "20%",
          left: "center",
        },
        series: [
          {
            name: "Access From",
            type: "pie",
            radius: ["40%", "70%"],
            center: ["50%", "75%"],
            // adjust the start and end angle
            startAngle: 180,
            endAngle: 360,
            data: [
              {
                value: this.rag_collect_data[3].value,
                name: "上下文查准率",
                itemStyle: {
                  color: "#468cf2",
                },
              },
              {
                value: this.rag_collect_data[4].value,
                name: "上下文召回率",
                itemStyle: {
                  color: "#aac4e9",
                },
              },
              {
                value: this.rag_collect_data[5].value,
                name: "上下文实体召回率",
                itemStyle: {
                  color: "#9fd5e5",
                },
              },

              {
                value: this.rag_collect_data[6].value,
                name: "忠实度",
                itemStyle: {
                  color: "#ffb8f5",
                },
              },

              {
                value: this.rag_collect_data[7].value,
                name: "答案相关性",
                itemStyle: {
                  color: "#6d5ecf",
                },
              },
            ],
          },
        ],
      });
    },

    trendChart(val) {
      const trendChart = echarts.init(document.getElementById(val));

      // 检查数据是否存在
      if (!this.rag_metric || this.rag_metric.length === 0) {
        console.warn('RAG指标数据为空，无法渲染图表');
        return;
      }

      // 过滤出RAG评估指标（排除基础统计数据）
      const ragMetrics = this.rag_metric.filter(metric =>
        ['上下文查准率', '上下文召回率', '上下文实体召回率', '忠实度', '答案相关性'].includes(metric.label)
      );

      // 如果没有RAG评估指标数据，显示提示
      if (ragMetrics.length === 0) {
        console.warn('没有找到RAG评估指标数据');
        return;
      }

      // 定义分数区间
      const scoreRanges = ['0.6以下', '0.6-0.7', '0.7-0.8', '0.8-0.9', '0.9以上'];

      // 原有配色方案
      const colors = ['#574ad3', '#887cfc', '#4b96fd', '#2fb4b2', '#6fdfe2'];

      // 准备系列数据
      const series = ragMetrics.map((metric, index) => {
        // 确保 metric.value 是对象且包含分数区间数据
        const values = metric.value && typeof metric.value === 'object'
          ? Object.values(metric.value)
          : [0, 0, 0, 0, 0]; // 默认值

        return {
          name: metric.label,
          type: 'bar',
          data: values,
          itemStyle: {
            color: colors[index % colors.length]
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 8,
              shadowColor: 'rgba(0, 0, 0, 0.3)'
            }
          },
          barMaxWidth: 50
        };
      });

      trendChart.setOption({
        title: {
          text: 'RAG指标分布统计',
          left: 'center',
          top: '3%',
          textStyle: {
            fontSize: 16,
            fontWeight: 'bold',
            color: '#333'
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          backgroundColor: 'rgba(255, 255, 255, 0.95)',
          borderColor: '#ddd',
          borderWidth: 1,
          textStyle: {
            color: '#333'
          },
          formatter: function(params) {
            let result = `<div style="font-weight: bold; margin-bottom: 8px; color: #333;">${params[0].axisValue}</div>`;
            params.forEach(param => {
              result += `
                <div style="display: flex; align-items: center; margin-bottom: 4px;">
                  <span style="display: inline-block; width: 12px; height: 12px; background-color: ${param.color}; border-radius: 2px; margin-right: 8px;"></span>
                  <span style="flex: 1; color: #666;">${param.seriesName}</span>
                  <span style="font-weight: bold; margin-left: 8px; color: #333;">${param.value}</span>
                </div>
              `;
            });
            return result;
          }
        },
        legend: {
          top: '12%',
          left: 'center',
          itemGap: 20,
          textStyle: {
            fontSize: 12,
            color: '#666'
          },
          itemWidth: 14,
          itemHeight: 14
        },
        grid: {
          left: '10%',
          right: '10%',
          bottom: '15%',
          top: '25%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: scoreRanges,
          axisLabel: {
            fontSize: 11,
            color: '#666',
            interval: 0
          },
          axisLine: {
            lineStyle: {
              color: '#e0e0e0'
            }
          },
          axisTick: {
            show: false
          }
        },
        yAxis: {
          type: 'value',
          name: '用例数量',
          nameTextStyle: {
            color: '#666',
            fontSize: 12
          },
          axisLabel: {
            fontSize: 11,
            color: '#666'
          },
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          splitLine: {
            lineStyle: {
              color: '#f5f5f5',
              type: 'solid'
            }
          }
        },
        series: series,
        animationDuration: 800,
        animationEasing: 'cubicOut'
      });

      // 添加响应式处理
      const resizeHandler = () => {
        trendChart.resize();
      };

      // 移除之前的监听器，避免重复绑定
      window.removeEventListener('resize', resizeHandler);
      window.addEventListener('resize', resizeHandler);
    },
    // llmReportStatistics,llmReportStats,llmReport
    async getLlmReport(val) {
      let params = {
        project_id: this.llm_project_list_id?this.llm_project_list_id:8,
      };
      try {
        await llmReport(params).then((res) => {
          if (res && Array.isArray(res) && res.length > 0 && res[0]) {
            const { metrics, ...rest } = res[0];

            this.llm_collect = { ...rest, ...metrics };
            const labelMap = {
              requirement_count: "需求个数",
              suite_count: "测试套件个数",
              test_case_count: "测试用例个数",
              agent_goal_accuracy: "AI回答推断得分",
              precision: "AI回答准确率",
              recall: "AI回答召回率",
              f1: "AI回答F1",

              // 可根据实际返回的字段扩展其他映射
            };
            // 将 llm_collect 转换为 { label: 中文字段名, value: 字段值 } 的数组，并过滤掉 project_name
            this.llm_report_data = Object.entries(this.llm_collect)
              .map(([key, value]) => ({
                label: labelMap[key] || key, // 优先使用中文映射，无匹配时保留原key
                value: value,
              }))
              .filter((item) => item.label !== "project_name"); // 过滤条件
            this.getllmReportPie(val);
          } else {
            console.warn('LLM report API returned invalid data:', res);
            // 设置默认数据
            this.llm_collect = {};
            this.llm_report_data = [];
          }
        });
      } catch (error) {
        console.error('Error fetching LLM report:', error);
        // 设置默认数据
        this.llm_collect = {};
        this.llm_report_data = [];
      }
    },
    llmresetRagQuery(){
      this.llm_project_list_id = null
      this.getLlmReport('llmProjectPie')

    },
    getllmReportPie(val) {
      // 安全检查：确保数据存在且有足够的元素
      if (!this.llm_report_data || !Array.isArray(this.llm_report_data) || this.llm_report_data.length < 7) {
        console.warn('LLM report data is not available or incomplete');
        return;
      }

      const projectPie = echarts.init(document.getElementById(val));
      projectPie.setOption({
        title: { text: "LLM指标统计", left: "center" },
        tooltip: {
          trigger: "item",
        },
        legend: {
          top: "15%",
          left: "center",
        },
        series: [
          {
            name: "Access From",
            type: "pie",
            radius: ["40%", "70%"],
            center: ["50%", "70%"],
            // adjust the start and end angle
            startAngle: 180,
            endAngle: 360,

            data: [
              {
                value: this.llm_report_data[3]?.value || 0,
                name: "AI回答推断得分",
                itemStyle: {
                  color: "#468cf2",
                },
              },
              {
                value: this.llm_report_data[4]?.value || 0,
                name: "AI回答准确率",
                itemStyle: {
                  color: "#9fd5e5",
                },
              },

              {
                value: this.llm_report_data[5]?.value || 0,
                name: "AI回答召回率",
                itemStyle: {
                  color: "#ffb8f5",
                },
              },

              {
                value: this.llm_report_data[6]?.value || 0,
                name: "AI回答F1",
                itemStyle: {
                  color: "#6d5ecf",
                },
              },
            ],
          },
        ],
      });
    },
    async getllmReportStats(val) {

      let params = {
        metric:this.selectedAnalysisMetric? this.selectedAnalysisMetric:"precision",
        suite_id: this.llm_suite_id?this.llm_suite_id:5,
        version: this.llm_input_version ? this.llm_input_version : null,
        requirement_id: this.llm_requirement_id ? this.llm_requirement_id : null
      };
      try {
        await llmReportStats(params).then((res) => {
          if (res && res.resp && res.resp.stats) {
            this.llm_metric_stats = res.resp.stats;
            this.getllmReportStatsPie(val);
          } else {
            console.warn('LLM report stats API returned invalid data:', res);
            // 设置默认数据以避免错误
            this.llm_metric_stats = [
              { value: 0 },
              { value: 0 },
              { value: 0 },
              { value: 0 },
              { value: 0 }
            ];
            this.getllmReportStatsPie(val);
          }
        });
      } catch (error) {
        console.error('Error fetching LLM report stats:', error);
        // 设置默认数据以避免错误
        this.llm_metric_stats = [
          { value: 0 },
          { value: 0 },
          { value: 0 },
          { value: 0 },
          { value: 0 }
        ];
        this.getllmReportStatsPie(val);
      }
    },
    getllmReportStatsPie(val) {
      // 安全检查：确保数据存在且有足够的元素
      if (!this.llm_metric_stats || !Array.isArray(this.llm_metric_stats) || this.llm_metric_stats.length < 5) {
        console.warn('LLM metric stats data is not available or incomplete');
        return;
      }

      const analysisPie = echarts.init(document.getElementById(val));
      analysisPie.setOption({
        title: { text: "LLM指标占比", left: "center" },
        tooltip: {
          trigger: "item",
        },
        legend: {
          top: "12%",
          left: "center",
        },
        series: [
          {
            top: "13%",
            name: "Access From",
            type: "pie",
            radius: ["40%", "70%"],
            avoidLabelOverlap: false,
            padAngle: 5,
            itemStyle: {
              borderRadius: 10,
            },
            label: {
              show: false,
              position: "center",
            },
            emphasis: {
              label: {
                show: true,
                fontSize: 40,
                fontWeight: "bold",
              },
            },
            labelLine: {
              show: false,
            },
            data: [
              {
                value: this.llm_metric_stats[0]?.value || 0,
                name: "小于0.6",
                itemStyle: {
                  color: "#468cf2",
                },
              },
              {
                value: this.llm_metric_stats[1]?.value || 0,
                name: "0.6-0.7",
                itemStyle: {
                  color: "#aac4e9",
                },
              },
              {
                value: this.llm_metric_stats[2]?.value || 0,
                name: "0.7-0.8",
                itemStyle: {
                  color: "#9fd5e5",
                },
              },

              {
                value: this.llm_metric_stats[3]?.value || 0,
                name: "0.8-0.9",
                itemStyle: {
                  color: "#ffb8f5",
                },
              },

              {
                value: this.llm_metric_stats[4]?.value || 0,
                name: "大于0.9",
                itemStyle: {
                  color: "#6d5ecf",
                },
              },
            ],
          },
        ],
      });
    },
    async getllmReportStatistics(val) {
      let params = {
        project_id: this.llm_stats_project_list_id ? this.llm_stats_project_list_id : 8,
        suite_id: this.llm_suite_id ? this.llm_suite_id : 5,
        requirement_id: this.llm_requirement_id
          ? this.llm_requirement_id
          : null,
        version: this.llm_input_version ? this.llm_input_version : null,
      };
      try {
        await llmReportStatistics(params).then((res) => {
          if (res && res.success && res.resp) {
            const labelMap = {
              requirement_count: "需求个数",
              suite_count: "测试套件个数",
              test_case_count: "测试用例个数",
              agent_goal_accuracy: "AI回答推断得分",
              precision: "AI回答准确率",
              recall: "AI回答召回率",
              f1: "AI回答F1",
            };
            this.llm_metric = Object.entries(res.resp).map(([key, value]) => ({
              label: labelMap[key] || key, // 优先使用中文映射，无匹配时保留原key
              value: value,
            }));
            // 清空之前的标签数据
            this.llm_metric_label = [];
            for (let i = 0; i < this.llm_metric.length; i++) {
              this.llm_metric_label.push(this.llm_metric[i].label);
            }
            this.getllmChart(val);
          } else {
            console.warn('LLM report statistics API returned invalid data:', res);
            // 设置默认数据
            this.llm_metric = [];
            this.llm_metric_label = [];
          }
        });
      } catch (error) {
        console.error('Error fetching LLM report statistics:', error);
        // 设置默认数据
        this.llm_metric = [];
        this.llm_metric_label = [];
      }
    },
    getllmChart(val) {
      const trendChart = echarts.init(document.getElementById(val));
         // 检查数据是否存在

    // 检查数据是否存在
      if (!this.llm_metric || this.llm_metric.length === 0) {
        console.warn('RAG指标数据为空，无法渲染图表');
        return;
      }

      // 过滤出RAG评估指标（排除基础统计数据）
      const llmMetrics = this.llm_metric.filter(metric =>
        ['AI回答推断得分', 'AI回答准确率', 'AI回答召回率', 'AI回答F1'].includes(metric.label)
      );

      // 如果没有RAG评估指标数据，显示提示
      if (llmMetrics.length === 0) {
        console.warn('没有找到RAG评估指标数据');
        return;
      }
   // 定义分数区间
      const scoreRanges = ['0.6以下', '0.6-0.7', '0.7-0.8', '0.8-0.9', '0.9以上'];

      // 原有配色方案
      const colors = ['#574ad3', '#887cfc', '#4b96fd', '#2fb4b2', '#6fdfe2'];

      // 准备系列数据
      const series = llmMetrics.map((metric, index) => {
        // 确保 metric.value 是对象且包含分数区间数据
        const values = metric.value && typeof metric.value === 'object'
          ? Object.values(metric.value)
          : [0, 0, 0, 0, 0]; // 默认值

        return {
          name: metric.label,
          type: 'bar',
          data: values,
          itemStyle: {
            color: colors[index % colors.length]
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 8,
              shadowColor: 'rgba(0, 0, 0, 0.3)'
            }
          },
          barMaxWidth: 50
        };
      });
 trendChart.setOption({
        title: {
          text: 'LLM指标分布统计',
          left: 'center',
          top: '3%',
          textStyle: {
            fontSize: 16,
            fontWeight: 'bold',
            color: '#333'
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          backgroundColor: 'rgba(255, 255, 255, 0.95)',
          borderColor: '#ddd',
          borderWidth: 1,
          textStyle: {
            color: '#333'
          },
          formatter: function(params) {
            let result = `<div style="font-weight: bold; margin-bottom: 8px; color: #333;">${params[0].axisValue}</div>`;
            params.forEach(param => {
              result += `
                <div style="display: flex; align-items: center; margin-bottom: 4px;">
                  <span style="display: inline-block; width: 12px; height: 12px; background-color: ${param.color}; border-radius: 2px; margin-right: 8px;"></span>
                  <span style="flex: 1; color: #666;">${param.seriesName}</span>
                  <span style="font-weight: bold; margin-left: 8px; color: #333;">${param.value}</span>
                </div>
              `;
            });
            return result;
          }
        },
        legend: {
          top: '12%',
          left: 'center',
          itemGap: 20,
          textStyle: {
            fontSize: 12,
            color: '#666'
          },
          itemWidth: 14,
          itemHeight: 14
        },
        grid: {
          left: '10%',
          right: '10%',
          bottom: '15%',
          top: '25%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: scoreRanges,
          axisLabel: {
            fontSize: 11,
            color: '#666',
            interval: 0
          },
          axisLine: {
            lineStyle: {
              color: '#e0e0e0'
            }
          },
          axisTick: {
            show: false
          }
        },
        yAxis: {
          type: 'value',
          name: '用例数量',
          nameTextStyle: {
            color: '#666',
            fontSize: 12
          },
          axisLabel: {
            fontSize: 11,
            color: '#666'
          },
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          splitLine: {
            lineStyle: {
              color: '#f5f5f5',
              type: 'solid'
            }
          }
        },
        series: series,
        animationDuration: 800,
        animationEasing: 'cubicOut'
      });



    },
    async getllmCompareResult(val) {
      let params = {
        suite_id: 5,
        test_case_id: 0,
        execution_times_after: 0,
        execution_times_before: 0,
      };
      try {
        await llmCompareResult(params).then((res) => {
          if (res && res.resp && res.resp.comparison_results && Array.isArray(res.resp.comparison_results)) {
            this.llm_compare_result_data1 = res.resp.comparison_results[0] || {};
            this.llm_compare_result_data2 = res.resp.comparison_results[1] || {};
            this.llm_compare_result_data = res.resp.comparison_results;

            this.llm_compare_after_value = this.llm_compare_result_data.map(
              (item) => {
                return item.compare ? item.compare.map((item2) => {
                  return item2.after_value;
                }) : [];
              }
            );

            this.llm_compare_before_value = this.llm_compare_result_data.map(
              (item) => {
                return item.compare ? item.compare.map((item2) => {
                  return item2.before_value;
                }) : [];
              }
            );

            this.llm_compare_field = this.llm_compare_result_data.map((item) => {
              return item.compare ? item.compare.map((item2) => {
                return item2.field;
              }) : [];
            });
            this.getllmComparerChart(val);
          } else {
            console.warn('LLM compare result API returned invalid data:', res);
            // 设置默认数据
            this.llm_compare_result_data1 = {};
            this.llm_compare_result_data2 = {};
            this.llm_compare_result_data = [];
            this.llm_compare_after_value = [];
            this.llm_compare_before_value = [];
            this.llm_compare_field = [];
          }
        });
      } catch (error) {
        console.error('Error fetching LLM compare result:', error);
        // 设置默认数据
        this.llm_compare_result_data1 = {};
        this.llm_compare_result_data2 = {};
        this.llm_compare_result_data = [];
        this.llm_compare_after_value = [];
        this.llm_compare_before_value = [];
        this.llm_compare_field = [];
      }
    },
    getllmComparerChart(val) {
      const fullTrendChart = echarts.init(document.getElementById(val));
      fullTrendChart.setOption({
        title: { text: "月数据总览" },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "cross",
            label: {
              backgroundColor: "#6a7985",
            },
          },
        },
        legend: {
          data: ["Email", "Union Ads", "Video Ads", "Direct", "Search Engine"],
        },
        toolbox: {
          feature: {
            saveAsImage: {},
          },
        },
        grid: {
          left: "3%",
          right: "4%",
          bottom: "3%",
          containLabel: true,
        },
        xAxis: [
          {
            type: "category",
            boundaryGap: false,
            data: ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"],
          },
        ],
        xAxis: [
          {
            type: "category",
            boundaryGap: false,
            data: this.llm_compare_field[0],
          },
        ],
        yAxis: [
          {
            type: "value",
          },
        ],
        series: [
          {
            name: "Email",
            type: "line",
            stack: "Total",
            areaStyle: {},
            emphasis: {
              focus: "series",
            },
            data: this.llm_compare_after_value[0],
            itemStyle: {
              color: "#468cf2",
            },
          },
          {
            name: `${this.aq1}/次`,
            type: "line",
            stack: "Total",
            areaStyle: {},
            emphasis: {
              focus: "series",
            },
            data: this.llm_compare_before_value[0],
            itemStyle: {
              color: "#9fd5e5",
            },
          },
          {
            name: "Email",
            type: "line",
            stack: "Total",
            areaStyle: {},
            emphasis: {
              focus: "series",
            },
            data: this.llm_compare_after_value[1],
            itemStyle: {
              color: "#ffb8f5",
            },
          },
          {
            name: "Email12",
            type: "line",
            stack: "Total",
            areaStyle: {},
            emphasis: {
              focus: "series",
            },
            data: this.llm_compare_before_value[1],
            itemStyle: {
              color: "#6d5ecf",
            },
          },
        ],
      });
    },

    // 触发详情弹窗的方法（例如在表格操作列中调用）
    handleShowDetail() {
      this.detailDialogVisible = true;
    },

    // 关闭弹窗时清空数据
    handleDetailClose() {
      // currentDetail.value = {};
      this.detailDialogVisible = false;
    },

    // async getRagReport() {
    //   try {
    //     const response = await ragReport({ project_id: 8 });
    //     console.log(response.data);
    //   } catch (error) {  // 捕获请求错误
    //     console.error('请求失败:', error);
    //   }
    // }
  },
});
