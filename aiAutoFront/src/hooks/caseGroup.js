import { ref } from "vue";

export default function () {


const dialogImageUrl = ref('')
const disabled = ref(false)
let wo='1213'

const handleRemove = (file) => {
  console.log(file)
}

const handlePictureCardPreview = (file) => {
  dialogImageUrl.value = !file.url
  dialogVisible.value = true
}

const handleDownload = (file) => {
  console.log(file)
}
let uploadVisible=ref(false);
let dialogVisible = ref(false);


let Case = [
  {
    caseName: "case1",
    id: "1",
    module_name: "模块1",
    description: "描述1",
    createUser: "user1",
    source: "系统",
    content: "xxxx",
    type: "a",
    updateTime: "2023-07-01",
  },
  {
    caseName: "case2",
    id: "2",
    module_name: "模块1",
    description: "描述1",
    createUser: "user1",
    source: "导入",
    content: "xxxx",
    type: "a",
    updateTime: "2023-07-01",
  },
  {
    caseName: "case2",
    id: "2",
    module_name: "模块1",
    description: "描述1",
    createUser: "user1",
    source: "系统",
    content: "xxxx",
    type: "a",
    updateTime: "2023-07-01",
  },
  {
    caseName: "case2",
    id: "2",
    module_name: "模块1",
    description: "描述1",
    createUser: "user1",
    source: "数据库",
    content: "xxxx",
    type: "a",
    updateTime: "2023-07-01",
  },
  {
    caseName: "case2",
    id: "2",
    module_name: "模块1",
    description: "描述1",
    createUser: "user1",
    source: "AI生成",
    content: "xxxx",
    type: "a",
    updateTime: "2023-07-01",
  },
];

let treeData = ref([
  {
    label: "默认用例集",
  },
  {
    label: "新建一",
  },
  {
    label: "新建二",
  },
]);

let defaultProps = {
  children: "children",
  label: "label",
};

function testAdd() {
  dialogVisible.value = true;
}
function uploadfile() {
    uploadVisible.value = true;
}
function uploadClose() {
    uploadVisible.value = false;
}
function handleClose() {
  dialogVisible.value = false;
}

// 获取列表
const treeList = ref([]);
const getTreeList = () => {
  http.get("/获取树列表接口").then((res) => {
    treeList.value = res.data.children;
    filterAddParms(treeList.value, "isOper");
  });
};

//点击树节点时触发的方法
const input = ref("");
const parentId = ref("");
const handleNodeClick = (node, data) => {
  parentId.value = node.parentId;
};

// 点击新增一级（新增一级时parentid默认为0）
const newNodeRef = ref(null);
const addNodeTree = ref("");
const inputShow = ref(false);
const addNodeTreeList = () => {
  inputShow.value = true;
  setTimeout(() => {
    newNodeRef.value && newNodeRef.value.focus();
  }, 800);
};

//新增一级出现输入框时通过change事件输入内容请求方法添加节点
const addClassification = (a) => {

    treeData.value.push({label:a})
    inputShow.value = false;

  const data = {
    name: addNodeTree.value,
    parentId: 0,
  };
  if (addNodeTree.value != "") {
    http.post("新增一级保存接口", data).then((res) => {
      inputShow.value = false;
      getTreeList();
      addNodeTree.value = "";
    });
  } else {
    inputShow.value = false;
  }
};
//此方法为用户在输入框不输入任何东西时回车输入框不显示
const handleEnter = () => {
  inputShow.value = false;
};

//节点被关闭时触发的事件，节点关闭时输入框也消失
const nodeCollapse = (data) => {
  // 如果有input框, 删除节点
  if (nodeShow.value) {
    data.children.pop();
    nodeShow.value = false;
  }
};

//点击新增，出现输入框
const nodeShow = ref(false);
const addRef = ref(null);
const newChildNode = ref("");
// 删除节点。添加完节点后删除节点
function removeTreeNode(node, data) {
  const parent = node.parent;
  const children = parent.data.children || parent.data;
  const index = children.findIndex((d) => d.id === data.id);
  children.splice(index, 1);
  treeList.value = [...treeList.value];
  nodeShow.value = false;
}
//输入框输入内容添加数据
const defaultExpandedkeys = ref([]);
const handleAddNode = (node, data) => {
  defaultExpandedkeys.value.push(node.data.parentId);
  const params = {
    parentId: data.parentId,
    name: newChildNode.value,
  };
  http.post("点击新增接口", params).then((res) => {
    nextTick(() => {
      data.isAddNode = false;
      addRef.value && addRef.value.focus();
    });
    getTreeList();
    newChildNode.value = "";
  });
};
//此方法为用户在输入框不输入任何东西时回车输入框不显示
const handleAddEnter = (node, data) => {
  // if(data.isAddNode) return
  nextTick(() => {
    data.isAddNode = false;
    addRef.value && addRef.value.focus();
  });
};

//点击编辑，出现输入框
const editAllNode = (node, data) => {
  nextTick(() => {
    data.isEditNode = true;
  });
};
//编辑完之后请求接口保存编辑完的数据
const handleEditNode = (node, data) => {

  const editParams = {
    name: data.name,
    id: data.id,
    parentId: data.parentId,
  };
  console.log(editParams);
  http.put("点击编辑接口", editParams).then((res) => {
    nextTick(() => {
      data.isEditNode = false;
    });
    getTreeList();
  });
};
//此方法为用户在输入框不输入任何东西时回车输入框不显示
const handleEditEnter = (node, data) => {
  nextTick(() => {
    data.isEditNode = false;
  });
};
return {
    wo,
    handleDownload,
    handleRemove,
    handlePictureCardPreview,
    Case,
  treeData,
  defaultProps,
  testAdd,
  dialogVisible,
  handleClose,
  uploadfile,
  uploadClose,
  handleNodeClick,
  addNodeTree,
  addNodeTreeList,
  addClassification,
  handleEnter,
  nodeShow,
  addRef,
  newChildNode,
  removeTreeNode,
  handleAddNode,
  handleAddEnter,
  editAllNode,
}
}