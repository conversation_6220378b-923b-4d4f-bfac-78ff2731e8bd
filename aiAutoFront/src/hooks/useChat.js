import { ref } from "vue";

export function useChat(modelId) {
  // 确保函数接受 modelId 参数
  const chatMessages = ref([]);
  const inputMessage = ref("");
  const isStreaming = ref(false);
  const assistantMsgIndex = ref(-1);

  const processStreamResponse = async (res) => {
    // 分步验证响应有效性
    if (!res) {
      throw new Error("网络错误: 无响应");
    }

    if (!res.ok) {
      throw new Error(`请求失败，状态码: ${res.status}`);
    }

    if (!res.body) {
      throw new Error("响应体不可用");
    }

    if (typeof res.body.getReader !== "function") {
      throw new Error("响应不支持流式处理");
    }

    isStreaming.value = true;
    const reader = res.body.getReader();
    const decoder = new TextDecoder("utf-8");
    let buffer = "";

    try {
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split(/\r?\n/);
        buffer = lines.pop();

        for (const line of lines) {
          if (!line.startsWith("data:")) continue;

          try {
            const data = JSON.parse(line.substring(5).trim());
            handleStreamEvent(data);
          } catch (e) {
            console.error("Failed to parse stream data:", e);
          }
        }
      }
    } finally {
      isStreaming.value = false;
      reader.releaseLock();
    }
  };

  const handleStreamEvent = (data) => {
    switch (data.type) {
      case "start":
        // 清除之前的助手消息
        chatMessages.value = chatMessages.value.filter(
          (msg) => msg.role !== "assistant" || msg.finished
        );
        chatMessages.value.push({
          role: "assistant",
          content: "",
          finished: false,
        });
        assistantMsgIndex.value = chatMessages.value.length - 1;
        break;

      case "chat_message":
        if (assistantMsgIndex.value >= 0) {
          const msg = chatMessages.value[assistantMsgIndex.value];
          msg.content += decodeURIComponent(data.content);
          msg.finished = data.finished;
        }
        break;
      case "error":
        // 更新当前助手消息的内容，而不是创建新的助手消息
        if (assistantMsgIndex.value >= 0) {
          const msg = chatMessages.value[assistantMsgIndex.value];
          msg.content = decodeURIComponent(data.content);
          msg.finished = true;
        } else {
          chatMessages.value.push({
            role: "assistant",
            content: decodeURIComponent(data.content),
            finished: true,
          });
          assistantMsgIndex.value = -1; // 重置助手消息索引
        }
        break;

      case "end":
        if (assistantMsgIndex.value >= 0) {
          chatMessages.value[assistantMsgIndex.value].finished = true;
          assistantMsgIndex.value = -1;
        }
        break;

      default:
        console.warn("Unknown message type:", data.type);
    }
  };

  const sendMessage = async () => {
    if (!inputMessage.value.trim()) return;

    chatMessages.value.push({
      role: "user",
      content: inputMessage.value,
      finished: true,
    });

    const userMessage = inputMessage.value;
    inputMessage.value = "";

    try {
      const response = await fetch("/api/agent/chat/stream", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `${localStorage.getItem("token")}`.replace(/"/g, ""),
        },
        body: JSON.stringify({
          model_id: modelId.value, // 确保使用 modelId.value 获取实际值
          message: userMessage,
        }),
      });

      if (!response.ok) {
        throw new Error(`API请求失败: ${response.status}`);
      }

      await processStreamResponse(response);
    } catch (error) {
      console.error("Chat error:", error);
      // 更新当前助手消息的内容，而不是创建新的助手消息
      if (assistantMsgIndex.value >= 0) {
        const msg = chatMessages.value[assistantMsgIndex.value];
        msg.content = `抱歉，发生错误: ${error.message}`;
        msg.finished = true;
      } else {
        chatMessages.value.push({
          role: "assistant",
          content: `抱歉，发生错误: ${error.message}`,
          finished: true,
        });
      }
    }
  };

  const clearContent = () => {
    chatMessages.value = [];
    assistantMsgIndex.value = -1;
  };

  const shouldRenderContent = (msg) => {
    // 有内容时始终渲染
    if (msg.content) return true;
    // 用户消息始终渲染
    if (msg.role === "user") return true;
    // 其他情况不渲染
    return false;
  };

  return {
    chatMessages,
    inputMessage,
    isStreaming,
    sendMessage,
    clearContent,
    processStreamResponse,
    shouldRenderContent,
  };
}