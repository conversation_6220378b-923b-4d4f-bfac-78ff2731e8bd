

import { createRouter, createWebHistory, createWebHashHistory } from 'vue-router'


import login from './modules/login';
import home from './modules/home';
import manage from './modules/manage';
import DialogView from '../views/prompt/dialog.vue'
import PromptComparsionView from '../views/template/PromptComparison.vue'


// export const fixedRoutes = [...home];
// 动态菜单
const routes = [
    {
        path: '/PromptComparison',
        name: 'PromptComparison',
        component: PromptComparsionView,
    }, {
        path: '/dialog',
        name: 'Dialog',
        component: DialogView,
    },

    //     {
    //       path: '/login',
    //       name:'login',
    //       component: () => import('@/views/login/index.vue'),

    //   },
    //   {
    //     path: '/ssoLogin',
    //     name:'ssoLogin',
    //     component: () => import('@/views/login/ssoLogin.vue'),

// }
      ...manage,
      ...login,
      ...home

  ];
  const router = createRouter({

    history: createWebHistory(),
    routes,

    scrollBehavior(to, from, savedPosition) {
        if (savedPosition) {
            return savedPosition;
        } else {
            return { left: 0, top: 0 };
        }
    }
}
);

//router.matcher = createRouter().matcher

export function resetRouter() {
    router.replace({ path: '/ssoLogin' });
    location.reload();
}
export default router;
