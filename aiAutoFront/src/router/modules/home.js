import Layout from "@/components/layout/AppLayout.vue";

export default [
  {
    path: "/",
    redirect: "/home",
    component: () => import("@/components/layout/AppLayout.vue"),
    meta: {
      authority: 0,
    },
    name: "",
    projectHidden: true,
    children: [
      {
        path: "/home",
        component: () => import("@/views/project/ProjectList.vue"),
      },
      {
        path: "/requires",
        component: () => import("@/views/requirement/requirementList.vue"),
      },
      {
        path: "/material",
        component: () => import("@/views/data/DataList.vue"),
      },
      {
        path: "/suite",
        component: () => import("@/views/suite/suiteList.vue"),
      },

      {
        path: "/execute",
        component: () => import("@/views/suite/suiteExecuteList.vue"),
      },
      {
        path: "/task",
        component: () => import("@/views/evaluation/evaluateTaskList.vue"),
      },

      {
        path: "/report",
        component: () => import("@/views/evaluation/testEv.vue"),
      },
      {
        path: "/image-to-testcase",
        component: () => import("@/views/imageToTestCase/ImageToTestCase.vue"),
      },
      {
        path: "/model",
        component: () => import("@/views/model/modelList.vue"),
      },
      {
        path: "/prompt",
        component: () => import("@/views/prompt/prompt.vue"),
      },
      {
        path: "/dialog",
        component: () => import("@/views/prompt/dialog.vue"),
      },
      {
        path: "/template",
        component: () => import("@/views/template/TemplateList.vue"),
      }
    ],
  },

  // {

  // path: '/material',

  // name: 'Material',

  // component: () => import('@/views/data/DataList.vue') // 物料管理组件

  // },

  // {

  // path: '/requirement',

  // name: 'Requirement',

  // component: () => import('@/views/requirement/requirementList.vue')

  // },

  // {

  // path: '/suite',

  // name: 'Suite',

  // component: () => import('@/views/suite/suiteList.vue')

  // },

  // {

  // path: '/execute',

  // name: 'Execute',

  // component: () => import('@/views/suite/suiteExecuteList.vue')

  // },

  // {

  // path: '/report',

  // name: 'Report',

  // component: () => import('@/views/evaluation/testEv.vue')

  // }
];
