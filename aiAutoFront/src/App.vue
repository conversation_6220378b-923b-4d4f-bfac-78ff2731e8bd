<template>

	<div id="app">



        <router-view></router-view>

	</div>
</template>

<style lang="scss">
html,
body,
#app {
    width: 100%;
    height: 100%;
    margin: 0;
    padding: 0;
    // 动态设置根元素字体大小，基于屏幕宽度
    font-size: calc(100vw / 1920 * 16); // 假设设计稿宽度为1920px
}

// 自适应输入框内容对齐方式
.el-input-number {
    .el-input {
        input {
            text-align: left !important;
        }
    }
}

// 媒体查询：针对小屏幕优化
@media screen and (max-width: 768px) {
    html {
        font-size: calc(100vw / 768 * 16); // 小屏幕下调整字体大小
    }
}

// 媒体查询：针对超大屏幕优化
@media screen and (min-width: 2560px) {
    html {
        font-size: calc(100vw / 2560 * 16); // 超大屏幕下调整字体大小
    }
}
</style>


