// 该文件中的变量是全局变量，在css文件和vue组件中可以直接使用

$mainColor: #38b2ac; // 网站主题色

// 侧边栏
$menuBg: #1d4044; // 菜单背景颜色
$menuTextColor: #fff; // 菜单文字颜色
$menuActiveTextColor: $mainColor; // 已选中菜单文字颜色
$menuActiveBg: none; // 已选中菜单背景颜色
$menuHover: #234e52; // 鼠标经过菜单时的背景颜色
$subMenuBg: #285e61; // 子菜单背景颜色
$subMenuHover: #319795; // 鼠标经过子菜单时的背景颜色
$collapseMenuActiveBg: #234e52; // 菜单宽度折叠后，已选中菜单的背景颜色
$collapseMenuActiveColor: $menuTextColor; // 菜单宽度折叠后，已选中菜单的文字颜色
$collapseMenuActiveBorderColor: $mainColor; // 菜单宽度折叠后，已选中菜单的边框颜色
$collapseMenuActiveBorderWidth: 2px; // 菜单宽度折叠后，已选中菜单的边框宽度
$arrowColor: #909399; // 展开/收起箭头颜色
$horizontalMenuHeight: 48px; // 菜单栏水平排列时候的高度
$bgLogo: #0097a7; //logo背景图

$bgLogoText: #B2F5EA//logo背景图;
