// import dark theme
// @use "element-plus/theme-chalk/src/dark/css-vars.scss" as *;
// @forward 'element-plus/theme-chalk/src/dark/var.scss' with (
//   $bg-color: (
//     'page': #0a0a0a,
//     'lll': #626aef,
//     'overlay': #1d1e1f,
//   )
// );
// @use "./reset.scss" as *;

// .ellipsis {
//   // color: f00;
//   // background-color: red;
//   // color: #fff;
//   width: 15%;
//   overflow: hidden;
//   text-overflow: ellipsis;
//   white-space: nowrap;
// }
