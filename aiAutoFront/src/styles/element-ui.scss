// cover some element-ui styles

// .el-breadcrumb__inner,
// .el-breadcrumb__inner a {
//   font-weight: 400 !important;
// }

// .el-upload {
//   input[type="file"] {
//     display: none !important;
//   }
// }

// .el-upload__input {
//   display: none;
// }

// .cell {
//   .el-tag {
//     margin-right: 0px;
//   }
// }

// .small-padding {
//   .cell {
//     padding-left: 5px;
//     padding-right: 5px;
//   }
// }

// .fixed-width {
//   .el-button--mini {
//     padding: 7px 10px;
//     width: 60px;
//   }
// }

// .status-col {
//   .cell {
//     padding: 0 10px;
//     text-align: center;

//     .el-tag {
//       margin-right: 0px;
//     }
//   }
// }

// // to fixed https://github.com/ElemeFE/element/issues/2461
// // .el-dialog {
// //   transform: none;
// //   left: 0;
// //   position: relative;
// //   margin: 0 auto;
// // }

// // refine element ui upload
// .upload-container {
//   .el-upload {
//     width: 100%;

//     .el-upload-dragger {
//       width: 100%;
//       height: 200px;
//     }
//   }
// }

// // dropdown
// .el-dropdown-menu {
//   a {
//     display: block;
//   }
// }

// // fix date-picker ui bug in filter-item
// .el-range-editor.el-input__inner {
//   display: inline-flex !important;
// }

// // to fix el-date-picker css style
// .el-range-separator {
//   box-sizing: content-box;
// }

// .el-menu--collapse>div>.el-submenu>.el-submenu__title .el-submenu__icon-arrow {
//   display: none;
// }

// .el-dropdown .el-dropdown-link {
//   color: var(--el-color-primary) !important;
// }

// /* 设置 notification、message 层级在 loading 之上 */
// .el-message,
// .el-notification {
//   z-index: 2058 !important;
// }

// /* el-alert */
// .el-alert {
//   border: 1px solid;
// }

// /* 当前页面最大化 css */
// .main-maximize {

//   .aside-split,
//   .el-aside,
//   .el-header,
//   .el-footer,
//   .tabs-box {
//     display: none !important;
//   }
// }

// /* custom card */
// .card {
//   box-sizing: border-box;
//   padding: 20px;
//   overflow-x: hidden;
//   background-color: var(--el-bg-color);
//   border: 1px solid var(--el-border-color-light);
//   border-radius: 6px;
//   box-shadow: 0 0 12px rgb(0 0 0 / 5%);
// }

// /* el-table 组件大小 */
// .el-table--small {
//   .el-table__header th {
//     height: 40px !important;
//     font-size: 14px !important;
//   }

//   .el-table__row {
//     height: 40px !important;
//     font-size: 13px !important;
//   }
// }

// .el-table--large {
//   .el-table__header th {
//     height: 50px !important;
//     font-size: 16px !important;
//   }

//   .el-table__row {
//     height: 50px !important;
//     font-size: 15px !important;
//   }
// }

// /* el-drawer */
// .el-drawer {
//   .el-drawer__header {
//     padding: 16px 20px;
//     margin-bottom: 0;
//     border-bottom: 1px solid var(--el-border-color-lighter);

//     span {
//       font-size: 17px;
//       line-height: 17px;
//       color: var(--el-text-color-primary) !important;
//     }
//   }

//   .el-drawer__footer {
//     border-top: 1px solid var(--el-border-color-lighter);
//   }

//   // select 样式
//   .el-select {
//     width: 100%;
//   }

//   // drawer-form 中存在两列 form-item 样式
//   .drawer-multiColumn-form {
//     display: flex;
//     flex-wrap: wrap;

//     .el-form-item {
//       width: 47%;

//       &:nth-child(2n-1) {
//         margin-right: 5%;
//       }
//     }
//   }
// }

// /* el-dialog */
// .el-dialog {
//   .el-dialog__header {
//     padding: 15px 20px;
//     margin: 0;
//     border-bottom: 1px solid var(--el-border-color-lighter);
//     .el-dialog__title {
//       font-size: 17px;
//     }
//   }
// }

// .el-tooltip__popper,
// .el-popper.is-dark {
//   line-height: 14px;
//   max-width: 25rem;
//   color: var(--el-bg-color);
//   background: var(--el-text-color-primary);
//   border: 1px solid var(--el-text-color-primary);
// }

// .el-table [class*=el-table__row--level] .el-table__expand-icon {
//   line-height: 32px;
//   height: 32px;
// }
