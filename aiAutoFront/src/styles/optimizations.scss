// CSS 优化和性能提升
// 基于最佳实践的样式优化

// ===== 性能优化 =====

// 硬件加速
.gpu-accelerated {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

// 平滑滚动
.smooth-scroll {
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
}

// 优化字体渲染
.optimized-text {
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-feature-settings: "liga" 1, "kern" 1;
}

// 图片优化
.optimized-image {
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
  
  &--smooth {
    image-rendering: auto;
  }
}

// ===== 动画优化 =====

// 高性能动画
.performant-animation {
  will-change: transform, opacity;
  
  &:hover {
    will-change: auto;
  }
}

// 减少重绘的动画
.efficient-hover {
  transition: transform 0.2s ease, opacity 0.2s ease;
  
  &:hover {
    transform: translateY(-2px);
  }
}

// 优化的淡入动画
@keyframes optimized-fade-in {
  from {
    opacity: 0;
    transform: translate3d(0, 10px, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

.fade-in-optimized {
  animation: optimized-fade-in 0.3s ease-out;
}

// ===== 布局优化 =====

// 避免布局抖动
.layout-stable {
  contain: layout style paint;
}

// 优化的 Flexbox
.flex-optimized {
  display: flex;
  flex-direction: column;
  
  @supports (display: grid) {
    display: grid;
    grid-template-rows: auto 1fr auto;
  }
}

// 优化的网格布局
.grid-optimized {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  
  // 回退方案
  @supports not (display: grid) {
    display: flex;
    flex-wrap: wrap;
    margin: -0.5rem;
    
    > * {
      flex: 1 1 250px;
      margin: 0.5rem;
    }
  }
}

// ===== 滚动优化 =====

// 优化滚动性能
.scroll-optimized {
  overflow-y: auto;
  overscroll-behavior: contain;
  scroll-snap-type: y mandatory;
  
  // 隐藏滚动条但保持功能
  &.hide-scrollbar {
    scrollbar-width: none;
    -ms-overflow-style: none;
    
    &::-webkit-scrollbar {
      display: none;
    }
  }
}

// 滚动捕捉
.scroll-snap-item {
  scroll-snap-align: start;
  scroll-snap-stop: always;
}

// ===== 加载优化 =====

// 骨架屏
.skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

// 懒加载图片
.lazy-image {
  opacity: 0;
  transition: opacity 0.3s;
  
  &.loaded {
    opacity: 1;
  }
  
  &.loading {
    background: #f0f0f0;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24'%3E%3Cpath fill='%23ccc' d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: center;
  }
}

// ===== 内存优化 =====

// 限制重绘区域
.paint-containment {
  contain: paint;
}

// 限制样式重计算
.style-containment {
  contain: style;
}

// 完全隔离
.full-containment {
  contain: strict;
}

// ===== 响应式图片优化 =====

// 响应式图片容器
.responsive-image-container {
  position: relative;
  overflow: hidden;
  
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
  }
  
  // 悬浮效果
  &:hover img {
    transform: scale(1.05);
  }
}

// 图片占位符
.image-placeholder {
  background: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6c757d;
  font-size: 0.875rem;
  
  &::before {
    content: "📷";
    font-size: 2rem;
    margin-bottom: 0.5rem;
  }
}

// ===== 表格优化 =====

// 大表格优化
.large-table {
  table-layout: fixed;
  border-collapse: separate;
  border-spacing: 0;
  
  th, td {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  
  // 虚拟滚动支持
  &.virtual-scroll {
    tbody {
      display: block;
      height: 400px;
      overflow-y: auto;
    }
    
    thead, tbody tr {
      display: table;
      width: 100%;
      table-layout: fixed;
    }
  }
}

// ===== 表单优化 =====

// 优化的表单布局
.optimized-form {
  .form-group {
    margin-bottom: 1.5rem;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
  
  .form-control {
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    
    &:focus {
      outline: 0;
      border-color: #80bdff;
      box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }
  }
}

// ===== 打印优化 =====

@media print {
  // 隐藏不必要的元素
  .no-print,
  .sidebar,
  .header-actions,
  .pagination {
    display: none !important;
  }
  
  // 优化打印布局
  .print-optimized {
    width: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
    box-shadow: none !important;
    border: none !important;
  }
  
  // 确保文字清晰
  * {
    color: #000 !important;
    background: transparent !important;
  }
  
  // 分页控制
  .page-break-before {
    page-break-before: always;
  }
  
  .page-break-after {
    page-break-after: always;
  }
  
  .page-break-inside-avoid {
    page-break-inside: avoid;
  }
}

// ===== 可访问性优化 =====

// 焦点可见性
.focus-visible {
  &:focus-visible {
    outline: 2px solid #005fcc;
    outline-offset: 2px;
  }
}

// 高对比度支持
@media (prefers-contrast: high) {
  .high-contrast {
    border: 2px solid;
    background: white;
    color: black;
  }
}

// 减少动画
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

// ===== 暗色主题优化 =====

@media (prefers-color-scheme: dark) {
  .auto-dark {
    background-color: #1a1a1a;
    color: #ffffff;
  }
  
  .auto-dark-card {
    background-color: #2d2d2d;
    border-color: #404040;
  }
}

// ===== 工具类 =====

// 性能监控
.perf-monitor {
  &::before {
    content: attr(data-perf);
    position: fixed;
    top: 10px;
    right: 10px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 5px 10px;
    border-radius: 3px;
    font-size: 12px;
    z-index: 9999;
    pointer-events: none;
  }
}

// 调试边框
.debug-borders * {
  outline: 1px solid red;
}

// 内容可见性优化
.content-visibility-auto {
  content-visibility: auto;
  contain-intrinsic-size: 0 500px;
}
