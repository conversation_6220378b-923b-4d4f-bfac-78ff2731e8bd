// 响应式设计样式工具类
// 基于 Tailwind CSS 的响应式设计系统

@use 'sass:map';

// ===== 断点变量 =====
$breakpoints: (
  'xs': 480px,
  'sm': 640px,
  'md': 768px,
  'lg': 1024px,
  'xl': 1280px,
  '2xl': 1536px
) !default;

// ===== 媒体查询混入 =====
@mixin respond-to($breakpoint) {
  @if map.has-key($breakpoints, $breakpoint) {
    @media (min-width: map.get($breakpoints, $breakpoint)) {
      @content;
    }
  } @else {
    @warn "Unknown breakpoint: #{$breakpoint}";
  }
}

@mixin respond-below($breakpoint) {
  @if map.has-key($breakpoints, $breakpoint) {
    @media (max-width: map.get($breakpoints, $breakpoint) - 1px) {
      @content;
    }
  } @else {
    @warn "Unknown breakpoint: #{$breakpoint}";
  }
}

@mixin respond-between($min, $max) {
  @if map.has-key($breakpoints, $min) and map.has-key($breakpoints, $max) {
    @media (min-width: map.get($breakpoints, $min)) and (max-width: map.get($breakpoints, $max) - 1px) {
      @content;
    }
  } @else {
    @warn "Unknown breakpoints: #{$min} or #{$max}";
  }
}

// ===== 响应式容器 =====
.responsive-container {
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem;

  @include respond-to('sm') {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
    max-width: 42rem;
  }

  @include respond-to('md') {
    padding-left: 2rem;
    padding-right: 2rem;
    max-width: 56rem;
  }

  @include respond-to('lg') {
    padding-left: 2rem;
    padding-right: 2rem;
    max-width: 72rem;
  }

  @include respond-to('xl') {
    padding-left: 2rem;
    padding-right: 2rem;
    max-width: 80rem;
  }
}

// ===== 响应式网格 =====
.responsive-grid {
  @apply grid gap-4;

  &--1-2-3 {
    @apply grid-cols-1;

    @include respond-to('sm') {
      @apply grid-cols-2;
    }

    @include respond-to('lg') {
      @apply grid-cols-3;
    }
  }

  &--1-2-4 {
    @apply grid-cols-1;

    @include respond-to('sm') {
      @apply grid-cols-2;
    }

    @include respond-to('lg') {
      @apply grid-cols-4;
    }
  }

  &--2-3-4 {
    @apply grid-cols-2;

    @include respond-to('md') {
      @apply grid-cols-3;
    }

    @include respond-to('lg') {
      @apply grid-cols-4;
    }
  }

  &--auto-fit {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }

  &--auto-fill {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  }
}

// ===== 响应式弹性布局 =====
.responsive-flex {
  @apply flex flex-col gap-4;

  @include respond-to('md') {
    @apply flex-row;
  }

  &--reverse {
    @apply flex-col-reverse;

    @include respond-to('md') {
      @apply flex-row;
    }
  }

  &--center {
    @apply items-center justify-center;
  }

  &--between {
    @apply justify-between;
  }

  &--around {
    @apply justify-around;
  }
}

// ===== 响应式文字 =====
.responsive-text {
  &--heading {
    @apply text-2xl font-bold;

    @include respond-to('sm') {
      @apply text-3xl;
    }

    @include respond-to('md') {
      @apply text-4xl;
    }

    @include respond-to('lg') {
      @apply text-5xl;
    }
  }

  &--subheading {
    @apply text-lg font-semibold;

    @include respond-to('sm') {
      @apply text-xl;
    }

    @include respond-to('md') {
      @apply text-2xl;
    }
  }

  &--body {
    @apply text-sm;

    @include respond-to('sm') {
      @apply text-base;
    }
  }

  &--center-mobile {
    @apply text-center;

    @include respond-to('md') {
      @apply text-left;
    }
  }
}

// ===== 响应式间距 =====
.responsive-spacing {
  &--padding {
    @apply p-4;

    @include respond-to('sm') {
      @apply p-6;
    }

    @include respond-to('md') {
      @apply p-8;
    }

    @include respond-to('lg') {
      @apply p-12;
    }
  }

  &--margin {
    @apply m-4;

    @include respond-to('sm') {
      @apply m-6;
    }

    @include respond-to('md') {
      @apply m-8;
    }

    @include respond-to('lg') {
      @apply m-12;
    }
  }

  &--gap {
    @apply gap-2;

    @include respond-to('sm') {
      @apply gap-4;
    }

    @include respond-to('md') {
      @apply gap-6;
    }

    @include respond-to('lg') {
      @apply gap-8;
    }
  }
}

// ===== 响应式显示/隐藏 =====
.hide-mobile {
  @apply hidden;

  @include respond-to('md') {
    @apply block;
  }
}

.hide-desktop {
  @apply block;

  @include respond-to('md') {
    @apply hidden;
  }
}

.show-mobile-only {
  @apply block;

  @include respond-to('sm') {
    @apply hidden;
  }
}

.show-tablet-only {
  @apply hidden;

  @include respond-to('sm') {
    @apply block;
  }

  @include respond-to('lg') {
    @apply hidden;
  }
}

.show-desktop-only {
  @apply hidden;

  @include respond-to('lg') {
    @apply block;
  }
}

// ===== 响应式侧边栏 =====
.responsive-sidebar {
  @apply fixed left-0 top-0 h-full w-64 bg-white shadow-lg transform -translate-x-full transition-transform duration-300 z-50;

  &.open {
    @apply translate-x-0;
  }

  @include respond-to('lg') {
    @apply relative translate-x-0 shadow-none;
  }

  &--collapsed {
    @include respond-to('lg') {
      @apply w-16;
    }
  }
}

.responsive-sidebar-overlay {
  @apply fixed inset-0 bg-black bg-opacity-50 z-40;

  @include respond-to('lg') {
    @apply hidden;
  }
}

// ===== 响应式导航 =====
.responsive-nav {
  @apply flex flex-col;

  @include respond-to('md') {
    @apply flex-row;
  }

  &__item {
    @apply block py-2 px-4 text-center;

    @include respond-to('md') {
      @apply inline-block text-left;
    }
  }

  &__toggle {
    @apply block;

    @include respond-to('md') {
      @apply hidden;
    }
  }

  &__menu {
    @apply hidden;

    &.open {
      @apply block;
    }

    @include respond-to('md') {
      @apply block;
    }
  }
}

// ===== 响应式表格 =====
.responsive-table {
  @apply w-full overflow-x-auto;

  table {
    @apply min-w-full;
  }

  &--stack {
    @include respond-below('md') {
      table, thead, tbody, th, td, tr {
        @apply block;
      }

      thead tr {
        @apply absolute -top-full -left-full;
      }

      tr {
        @apply border border-gray-200 mb-4 p-4 rounded-lg;
      }

      td {
        @apply border-none relative pl-12 py-2;

        &:before {
          content: attr(data-label) ": ";
          @apply absolute left-0 top-2 font-semibold text-gray-600;
        }
      }
    }
  }
}

// ===== 响应式卡片 =====
.responsive-card {
  @apply p-4 rounded-lg shadow-md;

  @include respond-to('sm') {
    @apply p-6;
  }

  @include respond-to('md') {
    @apply p-8;
  }

  &--horizontal {
    @apply flex flex-col;

    @include respond-to('md') {
      @apply flex-row;
    }
  }

  &__image {
    @apply w-full h-48 object-cover rounded-lg mb-4;

    @include respond-to('md') {
      @apply w-1/3 h-auto mb-0 mr-6;
    }
  }

  &__content {
    @apply flex-1;
  }
}

// ===== 响应式按钮 =====
.responsive-button {
  @apply w-full py-3 px-4 text-sm;

  @include respond-to('sm') {
    @apply w-auto py-2 px-6;
  }

  &--stack {
    @apply block mb-2;

    @include respond-to('sm') {
      @apply inline-block mb-0 mr-2;
    }
  }
}

// ===== 响应式表单 =====
.responsive-form {
  @apply space-y-4;

  &__group {
    @apply flex flex-col;

    @include respond-to('md') {
      @apply flex-row gap-4;
    }
  }

  &__field {
    @apply flex-1;
  }

  &__actions {
    @apply flex flex-col gap-2;

    @include respond-to('sm') {
      @apply flex-row justify-end;
    }
  }
}

// ===== 响应式图片 =====
.responsive-image {
  @apply w-full h-auto;

  &--cover {
    @apply object-cover;
  }

  &--contain {
    @apply object-contain;
  }

  &--hero {
    @apply h-64;

    @include respond-to('sm') {
      @apply h-80;
    }

    @include respond-to('md') {
      @apply h-96;
    }

    @include respond-to('lg') {
      @apply h-screen;
    }
  }
}

// ===== 响应式视频 =====
.responsive-video {
  @apply relative w-full h-0;
  padding-bottom: 56.25%; // 16:9 aspect ratio

  iframe, video {
    @apply absolute top-0 left-0 w-full h-full;
  }
}

// ===== 打印样式 =====
@media print {
  .no-print {
    @apply hidden;
  }

  .print-only {
    @apply block;
  }

  .responsive-container {
    @apply max-w-none px-0;
  }

  .responsive-sidebar {
    @apply hidden;
  }
}
