@tailwind base;
@tailwind components;
@tailwind utilities;

/* 自定义基础样式 */
@layer base {
  html {
    @apply scroll-smooth;
  }

  body {
    @apply font-sans antialiased;
  }

  /* 确保与Element Plus兼容的基础样式 */
  * {
    @apply border-gray-200;
  }
}

/* 自定义组件样式 */
@layer components {
  /* 科技感卡片 */
  .tech-card {
    @apply bg-white dark:bg-gray-800 rounded-xl shadow-md hover:shadow-lg transition-all duration-300 border border-gray-100 dark:border-gray-700;
  }

  .tech-card-glow {
    @apply bg-white dark:bg-gray-800 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100 dark:border-gray-700;
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  }

  .tech-card-glow:hover {
    box-shadow: 0 0 40px rgba(59, 130, 246, 0.4);
  }

  /* 渐变背景 */
  .gradient-bg {
    @apply bg-gradient-to-br from-primary-500 via-secondary-500 to-accent-500;
  }

  .gradient-text {
    @apply bg-gradient-to-r from-primary-600 to-secondary-600 bg-clip-text text-transparent;
  }

  /* 按钮样式 */
  .btn-primary {
    @apply bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 shadow-sm hover:shadow-md;
  }

  .btn-secondary {
    @apply bg-secondary-600 hover:bg-secondary-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 shadow-sm hover:shadow-md;
  }

  .btn-outline {
    @apply border-2 border-primary-600 text-primary-600 hover:bg-primary-600 hover:text-white font-medium py-2 px-4 rounded-lg transition-all duration-200;
  }

  /* 导航样式 */
  .nav-item {
    @apply flex items-center px-4 py-3 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors duration-200;
  }

  .nav-item-active {
    @apply nav-item bg-primary-50 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300 border-r-2 border-primary-600;
  }

  /* 表格样式 */
  .table-header {
    @apply bg-gray-50 dark:bg-gray-800 text-gray-900 dark:text-gray-100 font-semibold;
  }

  .table-row {
    @apply hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors duration-150;
  }

  /* 输入框样式 */
  .input-field {
    @apply w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all duration-200;
  }

  /* 侧边栏样式 */
  .sidebar {
    @apply bg-white dark:bg-gray-900 border-r border-gray-200 dark:border-gray-700 shadow-sm;
  }

  .sidebar-collapsed {
    @apply w-16;
  }

  .sidebar-expanded {
    @apply w-64;
  }

  /* 头部样式 */
  .header {
    @apply bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 shadow-sm;
  }

  /* 内容区域 */
  .main-content {
    @apply bg-gray-50 dark:bg-gray-950 min-h-screen;
  }

  /* 加载动画 */
  .loading-spinner {
    @apply animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600;
  }

  /* 状态指示器 */
  .status-success {
    @apply bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium;
  }

  .status-warning {
    @apply bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-xs font-medium;
  }

  .status-error {
    @apply bg-red-100 text-red-800 px-2 py-1 rounded-full text-xs font-medium;
  }

  .status-info {
    @apply bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-medium;
  }
}

/* 自定义工具类 */
@layer utilities {
  .text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .text-shadow-lg {
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  }

  .backdrop-blur-xs {
    backdrop-filter: blur(2px);
  }

  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* 响应式工具类 */
  .container-responsive {
    @apply container mx-auto px-4 sm:px-6 lg:px-8;
  }

  /* 科技感效果 */
  .tech-glow {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  }

  .tech-border {
    @apply border border-primary-200 dark:border-primary-800;
  }

  .tech-hover {
    @apply hover:scale-105 transition-all duration-300;
  }

  .tech-hover:hover {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  }

  /* 滚动条隐藏 */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
}
