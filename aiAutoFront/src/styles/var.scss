/* global css variable */
$primary-color: var(--el-color-primary);
// $mainColor: #38B2AC;
// 该文件中的变量是全局变量，在css文件和vue组件中可以直接使用
:root {
    --mainColor: #72c7c3;
    --menuBg: #1d4044; // 菜单背景颜色
    --menuTextColor: #fff; // 菜单文字颜色
    --menuActiveTextColor: var(--mainColor); // 已选中菜单文字颜色
    --menuActiveBg: none; // 已选中菜单背景颜色
    --menuHover: #234e52; // 鼠标经过菜单时的背景颜色
    --subMenuBg: #285e61; // 子菜单背景颜色
    --subMenuHover: #319795; // 鼠标经过子菜单时的背景颜色
    --collapseMenuActiveBg: #234e52; // 菜单宽度折叠后，已选中菜单的背景颜色
    --collapseMenuActiveColor: #fff; // 菜单宽度折叠后，已选中菜单的文字颜色
    --collapseMenuActiveBorderColor: var(--mainColor); // 菜单宽度折叠后，已选中菜单的边框颜色
    --collapseMenuActiveBorderWidth: 2px; // 菜单宽度折叠后，已选中菜单的边框宽度
    --arrowColor: #909399; // 展开/收起箭头颜色
    --horizontalMenuHeight: 48px; // 菜单栏水平排列时候的高度
    --bgLogo: #0097a7; //logo背景图
    --bgLogoText: #b2f5ea; //logo背景图
}

$mainColor: var(--mainColor); // 网站主题色
// 侧边栏
$menuBg: var(--menuBg); // 菜单背景颜色
$menuTextColor: var(--menuTextColor); // 菜单文字颜色
$menuActiveTextColor: var(--menuActiveTextColor); // 已选中菜单文字颜色
$menuActiveBg: var(--menuActiveBg); // 已选中菜单背景颜色
$menuHover: var(--menuHover); // 鼠标经过菜单时的背景颜色
$subMenuBg: var(--subMenuBg); // 子菜单背景颜色
$subMenuHover: var(--subMenuHover); // 鼠标经过子菜单时的背景颜色
$collapseMenuActiveBg: var(--collapseMenuActiveBg); // 菜单宽度折叠后，已选中菜单的背景颜色
$collapseMenuActiveColor: var(--collapseMenuActiveColor); // 菜单宽度折叠后，已选中菜单的文字颜色
$collapseMenuActiveBorderColor: var(--collapseMenuActiveBorderColor); // 菜单宽度折叠后，已选中菜单的边框颜色
$collapseMenuActiveBorderWidth: var(--collapseMenuActiveBorderWidth); // 菜单宽度折叠后，已选中菜单的边框宽度
$arrowColor: #909399; // 展开/收起箭头颜色
$horizontalMenuHeight: var(--horizontalMenuHeight); // 菜单栏水平排列时候的高度

$bgLogo: var(--bgLogo); //logo背景图
$bgLogoText: var(--bgLogoText); //logo背景图
