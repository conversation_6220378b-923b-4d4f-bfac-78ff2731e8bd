@use './element-variables.scss';
@use './element-ui.scss';
@use './design-system.scss';
@use './components.scss';
@use './responsive.scss';
@use './optimizations.scss';

html,
body,
#app {
    width: 100%;
    height: 100%;
    padding: 0;
    margin: 0;
}

// body {
//   filter: hue-rotate(45deg);
// }

@font-face {
    font-family: 'JetBrains Mono';
    src: url('./fonts/JetBrainsMono-Light.woff2') format('woff2');
    font-weight: normal;
    font-style: normal;
}

/* 解决 h1 标签在 webkit 内核浏览器中文字大小失效问题 */
:-webkit-any(article, aside, nav, section) h1 {
    font-size: 2em;
}

body {
    height: 100%;
    -moz-osx-font-smoothing: grayscale;
    -webkit-font-smoothing: antialiased;
    text-rendering: optimizeLegibility;
    // font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif;
    font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
}

// label {
//   font-weight: 700;
// }

html {
    height: 100%;
    box-sizing: border-box;
}

#app {
    height: 100%;
}

*,
*:before,
*:after {
    box-sizing: inherit;
}

.no-padding {
    padding: 0px !important;
}

.padding-content {
    padding: 4px 0;
}

a:focus,
a:active {
    outline: none;
}

a,
a:focus,
a:hover {
    cursor: pointer;
    color: inherit;
    text-decoration: none;
}

div:focus {
    outline: none;
}

.fr {
    float: right;
}

.fl {
    float: left;
}

.pr-5 {
    padding-right: 5px;
}

.pl-5 {
    padding-left: 5px;
}

.block {
    display: block;
}

.pointer {
    cursor: pointer;
}

.inlineBlock {
    display: block;
}

.clearfix {
    &:after {
        visibility: hidden;
        display: block;
        font-size: 0;
        content: ' ';
        clear: both;
        height: 0;
    }
}

// 菜单栏
aside {
    // background: #f00;
    background-color: var(--el-menu-bg-color);
    padding: 0 2px;
    // margin-bottom: 10px;
    // border-radius: 2px;
    display: block;
    line-height: 32px;
    font-size: 16px;
    // font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Fira Sans", "Droid Sans",
    //   "Helvetica Neue", sans-serif;
    color: #2c3e50;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;

    a {
        color: #337ab7;
        cursor: pointer;

        &:hover {
            color: rgb(32, 160, 255);
        }
    }
}

// 全局样式
.text-center {
    text-align: center;
}

.link-type,
.link-type:focus {
    color: #337ab7;
    cursor: pointer;

    &:hover {
        color: rgb(32, 160, 255);
    }
}

//refine vue-multiselect plugin
.multiselect {
    line-height: 16px;
}

.multiselect--active {
    z-index: 1000 !important;
}

// 主题颜色
.router-link-active .is-active span {
    color: #fff;
}

.tags-view-container .tags-view-wrapper .tags-view-item.active {
    background-color: #355db4 !important;
    border-color: #355db4 !important;
}

/*******************flex样式****************************/
.flex-box {
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
}

/*************垂直方向左对齐********/
.flex-ver-left {
    -webkit-align-items: flex-start;
    align-items: flex-start;
}

/*************水平方向左对齐********/
.flex-ver-h-left {
    justify-content: flex-start;
}

/*************水平方向右对齐********/
.flex-ver-h-right {
    justify-content: flex-end;
}

/*************水平垂直居中********/
.flex-ver {
    align-items: center;
    justify-content: center;
}

/*************垂直居中********/
.flex-ver-v {
    align-items: center;
}

/*************水平居中********/
.flex-ver-h {
    justify-content: center;
}

/*************换行********/
.flex-wrap {
    flex-wrap: wrap;
}

/*************两端对齐********/
.flex-between {
    justify-content: space-between;
}

/*******************flex样式结束****************************/
.dl-horizontal {
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;

    dt {
        min-width: 60px;
        text-align: right;
        font-weight: bold;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    dd {
        margin-inline-start: 20px;
    }
}

.sidebar-logo-container {
    cursor: pointer;
}

/* flex */
.flx-center {
    display: flex;
    align-items: center;
    justify-content: center;
}

.flx-justify-between {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.flx-align-center {
    display: flex;
    align-items: center;
}

/* clearfix */
.clearfix::after {
    display: block;
    height: 0;
    overflow: hidden;
    clear: both;
    content: '';
}

/* 文字单行省略号 */
.sle {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* 文字多行省略号 */
.mle {
    display: -webkit-box;
    overflow: hidden;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
}

/* 文字多了自动換行 */
.break-word {
    word-break: break-all;
    word-wrap: break-word;
}

/* fade-transform */
.fade-transform-leave-active,
.fade-transform-enter-active {
    transition: all 0.2s;
}

.fade-transform-enter-from {
    opacity: 0;
    transition: all 0.2s;
    transform: translateX(-30px);
}

.fade-transform-leave-to {
    opacity: 0;
    transition: all 0.2s;
    transform: translateX(30px);
}

/* breadcrumb-transform */
.breadcrumb-enter-active {
    transition: all 0.2s;
}

.breadcrumb-enter-from,
.breadcrumb-leave-active {
    opacity: 0;
    transform: translateX(10px);
}

/*
  进入和离开动画可以使用不同
  持续时间和速度曲线。
*/
.c-enter-active {
    transition: all 0.3s ease-out;
}

.slide-fade-leave-active {
    transition: all 0.6s cubic-bezier(1, 0.5, 0.8, 1);
}

.slide-fade-enter-from,
.slide-fade-leave-to {
    transform: translateX(20px);
    opacity: 0;
}

/* scroll bar */
::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

::-webkit-scrollbar-thumb {
    background-color: var(--el-border-color-darker);
    border-radius: 20px;
}

/* nprogress */
#nprogress .bar {
    background: var(--el-color-primary) !important;
}

#nprogress .spinner-icon {
    border-top-color: var(--el-color-primary) !important;
    border-left-color: var(--el-color-primary) !important;
}

#nprogress .peg {
    box-shadow: 0 0 10px var(--el-color-primary), 0 0 5px var(--el-color-primary) !important;
}

/* 外边距、内边距全局样式 */
@for $i from 0 through 40 {
    .mt#{$i} {
        margin-top: #{$i}px !important;
    }

    .mr#{$i} {
        margin-right: #{$i}px !important;
    }

    .mb#{$i} {
        margin-bottom: #{$i}px !important;
    }

    .ml#{$i} {
        margin-left: #{$i}px !important;
    }

    .pt#{$i} {
        padding-top: #{$i}px !important;
    }

    .pr#{$i} {
        padding-right: #{$i}px !important;
    }

    .pb#{$i} {
        padding-bottom: #{$i}px !important;
    }

    .pl#{$i} {
        padding-left: #{$i}px !important;
    }
}

.text-red {
    color: red;
}

.ops-card {
    background: linear-gradient(225deg, transparent 18px, white 0);
    position: relative;
    border-radius: 0.5em;
    border-top-left-radius: 0;
    box-sizing: border-box;
    padding: 15px 15px;
    // margin-bottom: 10px;

    .form-content .form-delete {
        button:first-child {
            display: none;
        }
    }

    & > div:last-child {
        .form-content .form-delete {
            button:first-child {
                display: block;
            }
        }
    }

    &::after {
        content: '';
        position: absolute;
        right: 0px;
        top: 0px;
        background: linear-gradient(-135deg, transparent 50%, white 0);
        width: 25px;
        height: 25px;
        border-bottom-left-radius: 4px;
        box-shadow: -0.2em 0.2em 0.2em #dcdfe6;
    }

    .el-form-item {
        .el-form-item__content {
            display: flex;
            flex: 1;
            flex-wrap: nowrap;
        }
    }
}

// .ops-collapse {
//   width: 100%;
//   height: 40px;
//   display: flex;
//   justify-content: space-between;
//   // background: rgb(114, 199, 195, 1);
//   background-color: #fff;
//   line-height: 30px;

//   border-top-right-radius: 10px;
//   border-left: 6px solid #2086c1;
//   border-bottom: 6px solid transparent;
//   border-top: 6px solid transparent;
//   box-shadow: 2px 3px 5px 0px rgb(100 166 204 / 30%);

//   &__title {
//     font-size: 16px;
//     font-weight: 700;
//     text-indent: 5px;
//   }

//   &__subtitle {
//     padding-right: 10px;
//     cursor: pointer;
//   }
// }

.ops-commonDetail {
    .el-collapse-item {
        margin-bottom: 25px;
    }

    .el-collapse-item__header {
        font-weight: 600;
        letter-spacing: normal;
        font-size: 16px;
        padding-left: 10px;
    }
}

.breadcrumb-boder {
    margin-bottom: 10px;
    padding: 10px;
    background-color: #fff;
    border: 1px solid var(--el-color-primary);
    margin-bottom: 10px;
}

.text-color {
    color: #409eff;
    cursor: pointer;
    font-size: 12px;
}

.waves-ripple {
    position: absolute;
    border-radius: 100%;
    background-image: radial-gradient(circle, rgba(255, 255, 255, 0.35) 100%, rgba(0, 0, 0, 0.15) 100%);
    background-clip: padding-box;
    pointer-events: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-transform: scale(0);
    -ms-transform: scale(0);
    transform: scale(0);
    opacity: 1;
}

.waves-ripple.z-active {
    opacity: 0;
    -webkit-transform: scale(2);
    -ms-transform: scale(2);
    transform: scale(2);
    -webkit-transition: opacity 1.2s ease-out, -webkit-transform 0.6s ease-out;
    transition: opacity 1.2s ease-out, -webkit-transform 0.6s ease-out;
    transition: opacity 1.2s ease-out, transform 0.6s ease-out;
    transition: opacity 1.2s ease-out, transform 0.6s ease-out, -webkit-transform 0.6s ease-out;
}

.loader {
    color: #ffffff;
    font-size: 10px;
    margin: 100px auto;
    width: 1em;
    height: 1em;
    border-radius: 50%;
    position: relative;
    text-indent: -9999em;
    -webkit-animation: load4 1.3s infinite linear;
    animation: load4 1.3s infinite linear;
    -webkit-transform: translateZ(0);
    -ms-transform: translateZ(0);
    transform: translateZ(0);
}
@-webkit-keyframes load4 {
    0%,
    100% {
        box-shadow: 0 -3em 0 0.2em, 2em -2em 0 0em, 3em 0 0 -1em, 2em 2em 0 -1em, 0 3em 0 -1em, -2em 2em 0 -1em, -3em 0 0 -1em, -2em -2em 0 0;
    }
    12.5% {
        box-shadow: 0 -3em 0 0, 2em -2em 0 0.2em, 3em 0 0 0, 2em 2em 0 -1em, 0 3em 0 -1em, -2em 2em 0 -1em, -3em 0 0 -1em, -2em -2em 0 -1em;
    }
    25% {
        box-shadow: 0 -3em 0 -0.5em, 2em -2em 0 0, 3em 0 0 0.2em, 2em 2em 0 0, 0 3em 0 -1em, -2em 2em 0 -1em, -3em 0 0 -1em, -2em -2em 0 -1em;
    }
    37.5% {
        box-shadow: 0 -3em 0 -1em, 2em -2em 0 -1em, 3em 0em 0 0, 2em 2em 0 0.2em, 0 3em 0 0em, -2em 2em 0 -1em, -3em 0em 0 -1em, -2em -2em 0 -1em;
    }
    50% {
        box-shadow: 0 -3em 0 -1em, 2em -2em 0 -1em, 3em 0 0 -1em, 2em 2em 0 0em, 0 3em 0 0.2em, -2em 2em 0 0, -3em 0em 0 -1em, -2em -2em 0 -1em;
    }
    62.5% {
        box-shadow: 0 -3em 0 -1em, 2em -2em 0 -1em, 3em 0 0 -1em, 2em 2em 0 -1em, 0 3em 0 0, -2em 2em 0 0.2em, -3em 0 0 0, -2em -2em 0 -1em;
    }
    75% {
        box-shadow: 0em -3em 0 -1em, 2em -2em 0 -1em, 3em 0em 0 -1em, 2em 2em 0 -1em, 0 3em 0 -1em, -2em 2em 0 0, -3em 0em 0 0.2em, -2em -2em 0 0;
    }
    87.5% {
        box-shadow: 0em -3em 0 0, 2em -2em 0 -1em, 3em 0 0 -1em, 2em 2em 0 -1em, 0 3em 0 -1em, -2em 2em 0 0, -3em 0em 0 0, -2em -2em 0 0.2em;
    }
}
@keyframes load4 {
    0%,
    100% {
        box-shadow: 0 -3em 0 0.2em, 2em -2em 0 0em, 3em 0 0 -1em, 2em 2em 0 -1em, 0 3em 0 -1em, -2em 2em 0 -1em, -3em 0 0 -1em, -2em -2em 0 0;
    }
    12.5% {
        box-shadow: 0 -3em 0 0, 2em -2em 0 0.2em, 3em 0 0 0, 2em 2em 0 -1em, 0 3em 0 -1em, -2em 2em 0 -1em, -3em 0 0 -1em, -2em -2em 0 -1em;
    }
    25% {
        box-shadow: 0 -3em 0 -0.5em, 2em -2em 0 0, 3em 0 0 0.2em, 2em 2em 0 0, 0 3em 0 -1em, -2em 2em 0 -1em, -3em 0 0 -1em, -2em -2em 0 -1em;
    }
    37.5% {
        box-shadow: 0 -3em 0 -1em, 2em -2em 0 -1em, 3em 0em 0 0, 2em 2em 0 0.2em, 0 3em 0 0em, -2em 2em 0 -1em, -3em 0em 0 -1em, -2em -2em 0 -1em;
    }
    50% {
        box-shadow: 0 -3em 0 -1em, 2em -2em 0 -1em, 3em 0 0 -1em, 2em 2em 0 0em, 0 3em 0 0.2em, -2em 2em 0 0, -3em 0em 0 -1em, -2em -2em 0 -1em;
    }
    62.5% {
        box-shadow: 0 -3em 0 -1em, 2em -2em 0 -1em, 3em 0 0 -1em, 2em 2em 0 -1em, 0 3em 0 0, -2em 2em 0 0.2em, -3em 0 0 0, -2em -2em 0 -1em;
    }
    75% {
        box-shadow: 0em -3em 0 -1em, 2em -2em 0 -1em, 3em 0em 0 -1em, 2em 2em 0 -1em, 0 3em 0 -1em, -2em 2em 0 0, -3em 0em 0 0.2em, -2em -2em 0 0;
    }
    87.5% {
        box-shadow: 0em -3em 0 0, 2em -2em 0 -1em, 3em 0 0 -1em, 2em 2em 0 -1em, 0 3em 0 -1em, -2em 2em 0 0, -3em 0em 0 0, -2em -2em 0 0.2em;
    }
}
