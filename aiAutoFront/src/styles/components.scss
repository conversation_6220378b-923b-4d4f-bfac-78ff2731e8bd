// AutoMatrix 组件样式库
// 基于设计系统的统一组件样式

// ===== 卡片组件 =====
.am-card {
  background: var(--bg-primary);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-base);
  border: 1px solid var(--border-light);
  transition: all var(--duration-normal) var(--ease-out);
  
  &:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
  }
  
  &--glow {
    box-shadow: var(--shadow-glow);
    
    &:hover {
      box-shadow: var(--shadow-glow-lg);
    }
  }
  
  &--bordered {
    border: 2px solid var(--color-primary-200);
  }
  
  &__header {
    padding: var(--card-padding-md);
    border-bottom: 1px solid var(--border-light);
    font-weight: var(--font-semibold);
    color: var(--text-primary);
  }
  
  &__body {
    padding: var(--card-padding-md);
  }
  
  &__footer {
    padding: var(--card-padding-md);
    border-top: 1px solid var(--border-light);
    background: var(--bg-secondary);
    border-radius: 0 0 var(--radius-xl) var(--radius-xl);
  }
}

// ===== 按钮组件 =====
.am-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0 var(--space-4);
  height: var(--btn-height-md);
  border-radius: var(--radius-lg);
  font-family: var(--font-sans);
  font-weight: var(--font-medium);
  font-size: var(--text-sm);
  line-height: 1;
  cursor: pointer;
  transition: all var(--duration-fast) var(--ease-out);
  border: none;
  text-decoration: none;
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
  
  // 尺寸变体
  &--sm {
    height: var(--btn-height-sm);
    padding: 0 var(--space-3);
    font-size: var(--text-xs);
  }
  
  &--lg {
    height: var(--btn-height-lg);
    padding: 0 var(--space-6);
    font-size: var(--text-base);
  }
  
  // 颜色变体
  &--primary {
    background: var(--color-primary-600);
    color: var(--text-inverse);
    
    &:hover:not(:disabled) {
      background: var(--color-primary-700);
      box-shadow: var(--shadow-md);
    }
    
    &:active {
      background: var(--color-primary-800);
    }
  }
  
  &--secondary {
    background: var(--color-secondary-600);
    color: var(--text-inverse);
    
    &:hover:not(:disabled) {
      background: var(--color-secondary-700);
      box-shadow: var(--shadow-md);
    }
  }
  
  &--outline {
    background: transparent;
    border: 2px solid var(--color-primary-600);
    color: var(--color-primary-600);
    
    &:hover:not(:disabled) {
      background: var(--color-primary-600);
      color: var(--text-inverse);
    }
  }
  
  &--ghost {
    background: transparent;
    color: var(--color-primary-600);
    
    &:hover:not(:disabled) {
      background: var(--color-primary-50);
    }
  }
  
  &--danger {
    background: var(--color-error);
    color: var(--text-inverse);
    
    &:hover:not(:disabled) {
      background: #dc2626;
    }
  }
}

// ===== 输入框组件 =====
.am-input {
  width: 100%;
  height: var(--input-height-md);
  padding: 0 var(--space-3);
  border: 1px solid var(--border-medium);
  border-radius: var(--radius-lg);
  font-family: var(--font-sans);
  font-size: var(--text-sm);
  color: var(--text-primary);
  background: var(--bg-primary);
  transition: all var(--duration-fast) var(--ease-out);
  
  &:focus {
    outline: none;
    border-color: var(--color-primary-500);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }
  
  &:disabled {
    background: var(--bg-tertiary);
    color: var(--text-tertiary);
    cursor: not-allowed;
  }
  
  &--error {
    border-color: var(--color-error);
    
    &:focus {
      box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
    }
  }
  
  &--sm {
    height: var(--input-height-sm);
    font-size: var(--text-xs);
  }
  
  &--lg {
    height: var(--input-height-lg);
    font-size: var(--text-base);
  }
}

// ===== 表格组件 =====
.am-table {
  width: 100%;
  border-collapse: collapse;
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-base);
  
  &__header {
    background: var(--bg-secondary);
    
    th {
      padding: var(--space-4);
      text-align: left;
      font-weight: var(--font-semibold);
      color: var(--text-primary);
      border-bottom: 1px solid var(--border-light);
    }
  }
  
  &__body {
    tr {
      transition: background-color var(--duration-fast) var(--ease-out);
      
      &:hover {
        background: var(--bg-secondary);
      }
      
      &:not(:last-child) {
        border-bottom: 1px solid var(--border-light);
      }
    }
    
    td {
      padding: var(--space-4);
      color: var(--text-secondary);
    }
  }
  
  &--striped {
    .am-table__body tr:nth-child(even) {
      background: var(--bg-secondary);
    }
  }
}

// ===== 导航组件 =====
.am-nav {
  &__item {
    display: flex;
    align-items: center;
    padding: var(--space-3) var(--space-4);
    color: var(--text-secondary);
    text-decoration: none;
    border-radius: var(--radius-lg);
    transition: all var(--duration-fast) var(--ease-out);
    
    &:hover {
      background: var(--color-primary-50);
      color: var(--color-primary-700);
    }
    
    &--active {
      background: var(--color-primary-100);
      color: var(--color-primary-700);
      font-weight: var(--font-medium);
      border-left: 3px solid var(--color-primary-600);
    }
    
    &__icon {
      margin-right: var(--space-3);
      width: 1.25rem;
      height: 1.25rem;
    }
  }
}

// ===== 状态指示器 =====
.am-badge {
  display: inline-flex;
  align-items: center;
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-full);
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
  
  &--success {
    background: #dcfce7;
    color: #166534;
  }
  
  &--warning {
    background: #fef3c7;
    color: #92400e;
  }
  
  &--error {
    background: #fee2e2;
    color: #991b1b;
  }
  
  &--info {
    background: var(--color-primary-100);
    color: var(--color-primary-800);
  }
}

// ===== 加载动画 =====
.am-loading {
  &__spinner {
    width: 2rem;
    height: 2rem;
    border: 2px solid var(--border-light);
    border-top: 2px solid var(--color-primary-600);
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
  
  &__dots {
    display: flex;
    gap: var(--space-1);
    
    &__dot {
      width: 0.5rem;
      height: 0.5rem;
      background: var(--color-primary-600);
      border-radius: 50%;
      animation: pulse 1.5s ease-in-out infinite;
      
      &:nth-child(2) {
        animation-delay: 0.2s;
      }
      
      &:nth-child(3) {
        animation-delay: 0.4s;
      }
    }
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0%, 80%, 100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}
