import { defineStore } from 'pinia'
import { ref, reactive } from 'vue'
import axios from 'axios'

// 创建axios实例
const api = axios.create({
  baseURL: import.meta.env.VITE_BASE_API+'api', // 通过代理转发到后端
  timeout: 300000, // 5分钟超时，因为AI生成可能需要较长时间
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 添加认证token
    // const token = localStorage.getItem('token')
     const token = JSON.parse(localStorage.getItem('token'))
    if (token) {
      config.headers.Authorization = token
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    return response
  },
  (error) => {
    console.error('API请求错误:', error)

    // 处理常见错误
    if (error.response) {
      const { status, data } = error.response

      switch (status) {
        case 401:
          // 未授权，可能需要重新登录
          localStorage.removeItem('token')
          window.location.href = '/ssoLogin'
          break
        case 403:
          throw new Error('没有权限访问该资源')
        case 404:
          throw new Error('请求的资源不存在')
        case 500:
          throw new Error('服务器内部错误')
        default:
          throw new Error(data?.detail || data?.message || '请求失败')
      }
    } else if (error.request) {
      throw new Error('网络连接失败，请检查网络设置')
    } else {
      throw new Error('请求配置错误')
    }

    return Promise.reject(error)
  }
)

export const useImageToTestCaseStore = defineStore('imageToTestCase', () => {
  // 状态
  const isGenerating = ref(false)
  const generationRecords = ref([])
  const currentRecord = ref(null)
  const uploadProgress = ref(0)
  const generationProgress = ref(0)

  // 生成测试用例
  const generateTestCases = async (formData) => {
    try {
      isGenerating.value = true
      uploadProgress.value = 0
      generationProgress.value = 0

      // 使用fetch进行流式请求，因为axios不太适合处理流式响应
      const response = await fetch(import.meta.env.VITE_BASE_API+'api/test-cases/generate', {
        method: 'POST',
        body: formData,
       headers: {
          'Authorization':  JSON.parse(localStorage.getItem('token'))
        }
      })



      if (!response.ok) {
        const errorText = await response.text()
        throw new Error(`HTTP ${response.status}: ${errorText}`)
      }

      // 从响应头获取记录ID（如果后端提供的话）
      const recordId = response.headers.get('X-Record-ID')

      return {
        response,
        recordId
      }
    } catch (error) {
      isGenerating.value = false
      throw error
    }
  }
  // 获取生成记录列表
  const getGenerationRecords = async (page = 1, pageSize = 20) => {
    try {
      const response = await api.get('/test-cases/records', {
        params: { page, page_size: pageSize }
      })

      const records = response.data.records || response.data || []
      generationRecords.value = records
      return records
    } catch (error) {
      console.warn('获取生成记录失败，可能是后端服务未启动:', error.message)
      // 返回空数组而不是抛出错误
      generationRecords.value = []
      return []
    }
  }

  // 获取单个记录详情
  const getGenerationRecord = async (recordId) => {
    try {
      const response = await api.get(`/test-cases/record/${recordId}`)
      const record = response.data
      currentRecord.value = record
      return record
    } catch (error) {
      console.error('获取记录详情失败:', error)
      throw error
    }
  }

  // 下载Excel文件
  const downloadExcel = async (recordId) => {
    try {
      const response = await api.get(`/test-cases/download/excel/${recordId}`, {
        responseType: 'blob'
      })

      // 如果返回的是重定向URL（302），则直接打开链接
      if (response.request.responseURL && response.request.responseURL !== response.config.url) {
        window.open(response.request.responseURL, '_blank')
        return
      }

      // 如果返回的是文件内容，则创建下载链接
      const blob = new Blob([response.data], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      })
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `testcases_${recordId}.xlsx`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
    } catch (error) {
      console.error('下载Excel文件失败:', error)
      throw error
    }
  }

  // 下载XMind文件
  const downloadXMind = async (recordId) => {
    try {
      const response = await api.get(`/test-cases/download/xmind/${recordId}`, {
        responseType: 'blob'
      })

      // 如果返回的是重定向URL（302），则直接打开链接
      if (response.request.responseURL && response.request.responseURL !== response.config.url) {
        window.open(response.request.responseURL, '_blank')
        return
      }

      // 如果返回的是文件内容，则创建下载链接
      const blob = new Blob([response.data], { type: 'application/octet-stream' })
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `testcases_${recordId}.xmind`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
    } catch (error) {
      console.error('下载XMind文件失败:', error)
      throw error
    }
  }

  // 轮询记录状态（用于检查文件生成进度）
  const pollRecordStatus = async (recordId, maxAttempts = 30, interval = 2000) => {
    let attempts = 0

    const poll = async () => {
      try {
        const record = await getGenerationRecord(recordId)

        if (record.status === 'completed' || record.status === 'failed') {
          return record
        }

        if (attempts < maxAttempts) {
          attempts++
          setTimeout(poll, interval)
        } else {
          throw new Error('轮询超时')
        }
      } catch (error) {
        console.error('轮询记录状态失败:', error)
        throw error
      }
    }

    return poll()
  }

  // 重置状态
  const resetState = () => {
    isGenerating.value = false
    currentRecord.value = null
    uploadProgress.value = 0
    generationProgress.value = 0
  }

  // 设置当前记录
  const setCurrentRecord = (record) => {
    currentRecord.value = record
  }

  return {
    // 状态
    isGenerating,
    generationRecords,
    currentRecord,
    uploadProgress,
    generationProgress,

    // 方法
    generateTestCases,
    getGenerationRecords,
    getGenerationRecord,
    downloadExcel,
    downloadXMind,
    pollRecordStatus,
    resetState,
    setCurrentRecord
  }
})
