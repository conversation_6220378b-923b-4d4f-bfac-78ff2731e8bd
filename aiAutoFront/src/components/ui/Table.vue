<template>
  <div class="am-table-container">
    <!-- 表格工具栏 -->
    <div v-if="$slots.toolbar" class="am-table__toolbar">
      <slot name="toolbar"></slot>
    </div>

    <!-- 表格主体 -->
    <div class="am-table__wrapper" :class="{ 'am-table__wrapper--loading': loading }">
      <!-- 加载状态 -->
      <div v-if="loading" class="am-table__loading">
        <div class="loading-spinner"></div>
        <span class="loading-text">{{ loadingText }}</span>
      </div>

      <!-- 空状态 -->
      <div v-else-if="!data.length" class="am-table__empty">
        <slot name="empty">
          <div class="empty-content">
            <div class="empty-icon">
              <el-icon><DocumentRemove /></el-icon>
            </div>
            <p class="empty-text">{{ emptyText }}</p>
          </div>
        </slot>
      </div>

      <!-- 表格内容 -->
      <div v-else class="am-table__content">
        <table class="am-table" :class="tableClasses">
          <!-- 表头 -->
          <thead class="am-table__header">
            <tr>
              <th
                v-for="column in columns"
                :key="column.key"
                :class="[
                  'am-table__header-cell',
                  {
                    'am-table__header-cell--sortable': column.sortable,
                    'am-table__header-cell--sorted': sortKey === column.key
                  }
                ]"
                :style="{ width: column.width, minWidth: column.minWidth }"
                @click="handleSort(column)"
              >
                <div class="header-cell-content">
                  <span class="header-text">{{ column.title }}</span>
                  <div v-if="column.sortable" class="sort-icons">
                    <el-icon 
                      class="sort-icon sort-icon--asc"
                      :class="{ 'sort-icon--active': sortKey === column.key && sortOrder === 'asc' }"
                    >
                      <CaretTop />
                    </el-icon>
                    <el-icon 
                      class="sort-icon sort-icon--desc"
                      :class="{ 'sort-icon--active': sortKey === column.key && sortOrder === 'desc' }"
                    >
                      <CaretBottom />
                    </el-icon>
                  </div>
                </div>
              </th>
            </tr>
          </thead>

          <!-- 表体 -->
          <tbody class="am-table__body">
            <tr
              v-for="(row, index) in data"
              :key="getRowKey(row, index)"
              class="am-table__row"
              :class="{ 'am-table__row--selected': selectedRows.includes(getRowKey(row, index)) }"
              @click="handleRowClick(row, index)"
            >
              <td
                v-for="column in columns"
                :key="column.key"
                class="am-table__cell"
                :class="column.cellClass"
              >
                <div class="cell-content">
                  <!-- 自定义渲染 -->
                  <slot
                    v-if="column.slot"
                    :name="column.slot"
                    :row="row"
                    :column="column"
                    :index="index"
                    :value="getColumnValue(row, column.key)"
                  ></slot>
                  
                  <!-- 默认渲染 -->
                  <span v-else>{{ getColumnValue(row, column.key) }}</span>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- 分页 -->
    <div v-if="pagination && data.length" class="am-table__pagination">
      <slot name="pagination">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="pageSizes"
          :total="total"
          :layout="paginationLayout"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </slot>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { DocumentRemove, CaretTop, CaretBottom } from '@element-plus/icons-vue'

const props = defineProps({
  data: {
    type: Array,
    default: () => []
  },
  columns: {
    type: Array,
    required: true
  },
  loading: {
    type: Boolean,
    default: false
  },
  loadingText: {
    type: String,
    default: '加载中...'
  },
  emptyText: {
    type: String,
    default: '暂无数据'
  },
  striped: {
    type: Boolean,
    default: false
  },
  bordered: {
    type: Boolean,
    default: false
  },
  hoverable: {
    type: Boolean,
    default: true
  },
  size: {
    type: String,
    default: 'medium',
    validator: (value) => ['small', 'medium', 'large'].includes(value)
  },
  rowKey: {
    type: [String, Function],
    default: 'id'
  },
  selectedRows: {
    type: Array,
    default: () => []
  },
  pagination: {
    type: Boolean,
    default: false
  },
  total: {
    type: Number,
    default: 0
  },
  currentPage: {
    type: Number,
    default: 1
  },
  pageSize: {
    type: Number,
    default: 20
  },
  pageSizes: {
    type: Array,
    default: () => [10, 20, 50, 100]
  },
  paginationLayout: {
    type: String,
    default: 'total, sizes, prev, pager, next, jumper'
  }
})

const emit = defineEmits([
  'row-click', 'sort-change', 'selection-change',
  'size-change', 'current-change'
])

// 响应式数据
const sortKey = ref('')
const sortOrder = ref('') // 'asc' | 'desc'

// 计算属性
const tableClasses = computed(() => ({
  'am-table--striped': props.striped,
  'am-table--bordered': props.bordered,
  'am-table--hoverable': props.hoverable,
  [`am-table--${props.size}`]: true
}))

// 方法
const getRowKey = (row, index) => {
  if (typeof props.rowKey === 'function') {
    return props.rowKey(row, index)
  }
  return row[props.rowKey] || index
}

const getColumnValue = (row, key) => {
  return key.split('.').reduce((obj, k) => obj?.[k], row)
}

const handleRowClick = (row, index) => {
  emit('row-click', row, index)
}

const handleSort = (column) => {
  if (!column.sortable) return
  
  if (sortKey.value === column.key) {
    // 切换排序方向
    if (sortOrder.value === 'asc') {
      sortOrder.value = 'desc'
    } else if (sortOrder.value === 'desc') {
      sortKey.value = ''
      sortOrder.value = ''
    } else {
      sortOrder.value = 'asc'
    }
  } else {
    sortKey.value = column.key
    sortOrder.value = 'asc'
  }
  
  emit('sort-change', {
    key: sortKey.value,
    order: sortOrder.value,
    column
  })
}

const handleSizeChange = (size) => {
  emit('size-change', size)
}

const handleCurrentChange = (current) => {
  emit('current-change', current)
}
</script>

<style lang="scss" scoped>
.am-table-container {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden;
}

.am-table__toolbar {
  @apply p-4 border-b border-gray-200 bg-gray-50;
}

.am-table__wrapper {
  @apply relative overflow-x-auto;
  
  &--loading {
    @apply pointer-events-none;
  }
}

.am-table__loading {
  @apply absolute inset-0 bg-white bg-opacity-90 flex flex-col items-center justify-center z-10;
}

.loading-spinner {
  @apply w-8 h-8 border-2 border-primary-200 border-t-primary-600 rounded-full animate-spin mb-2;
}

.loading-text {
  @apply text-sm text-gray-600;
}

.am-table__empty {
  @apply p-12 text-center;
}

.empty-content {
  @apply flex flex-col items-center;
}

.empty-icon {
  @apply w-16 h-16 text-gray-300 mb-4;
}

.empty-text {
  @apply text-gray-500;
}

.am-table {
  @apply w-full border-collapse;
  
  &--small {
    .am-table__header-cell,
    .am-table__cell {
      @apply px-3 py-2 text-sm;
    }
  }
  
  &--medium {
    .am-table__header-cell,
    .am-table__cell {
      @apply px-4 py-3 text-sm;
    }
  }
  
  &--large {
    .am-table__header-cell,
    .am-table__cell {
      @apply px-6 py-4 text-base;
    }
  }
  
  &--striped {
    .am-table__row:nth-child(even) {
      @apply bg-gray-50;
    }
  }
  
  &--bordered {
    @apply border border-gray-200;
    
    .am-table__header-cell,
    .am-table__cell {
      @apply border-r border-gray-200;
      
      &:last-child {
        @apply border-r-0;
      }
    }
  }
  
  &--hoverable {
    .am-table__row:hover {
      @apply bg-gray-50;
    }
  }
}

.am-table__header {
  @apply bg-gray-50;
}

.am-table__header-cell {
  @apply text-left font-semibold text-gray-900 border-b border-gray-200;
  
  &--sortable {
    @apply cursor-pointer select-none hover:bg-gray-100;
  }
  
  &--sorted {
    @apply bg-primary-50 text-primary-700;
  }
}

.header-cell-content {
  @apply flex items-center justify-between;
}

.header-text {
  @apply flex-1;
}

.sort-icons {
  @apply flex flex-col ml-2;
}

.sort-icon {
  @apply w-3 h-3 text-gray-400 transition-colors duration-200;
  
  &--active {
    @apply text-primary-600;
  }
  
  &--asc {
    @apply -mb-1;
  }
}

.am-table__body {
  @apply bg-white;
}

.am-table__row {
  @apply transition-colors duration-150 cursor-pointer;
  
  &--selected {
    @apply bg-primary-50;
  }
}

.am-table__cell {
  @apply text-gray-700 border-b border-gray-200;
  
  &:last-child {
    .cell-content {
      @apply justify-end;
    }
  }
}

.cell-content {
  @apply flex items-center;
}

.am-table__pagination {
  @apply p-4 border-t border-gray-200 flex justify-center bg-gray-50;
}

// 响应式设计
@media (max-width: 768px) {
  .am-table__wrapper {
    @apply overflow-x-scroll;
  }
  
  .am-table {
    @apply min-w-full;
  }
  
  .am-table__pagination {
    @apply p-2;
    
    :deep(.el-pagination) {
      @apply text-sm;
    }
  }
}

// 暗色主题
.dark {
  .am-table-container {
    @apply bg-gray-800 border-gray-700;
  }
  
  .am-table__toolbar {
    @apply bg-gray-900 border-gray-700;
  }
  
  .am-table__header {
    @apply bg-gray-900;
  }
  
  .am-table__header-cell {
    @apply text-gray-200 border-gray-700;
    
    &--sorted {
      @apply bg-primary-900 text-primary-300;
    }
  }
  
  .am-table__body {
    @apply bg-gray-800;
  }
  
  .am-table__row {
    &:hover {
      @apply bg-gray-700;
    }
    
    &--selected {
      @apply bg-primary-900;
    }
  }
  
  .am-table__cell {
    @apply text-gray-300 border-gray-700;
  }
  
  .am-table--striped {
    .am-table__row:nth-child(even) {
      @apply bg-gray-900;
    }
  }
  
  .am-table__pagination {
    @apply bg-gray-900 border-gray-700;
  }
  
  .am-table__loading {
    @apply bg-gray-800 bg-opacity-90;
  }
  
  .loading-text {
    @apply text-gray-300;
  }
  
  .empty-text {
    @apply text-gray-400;
  }
}
</style>
