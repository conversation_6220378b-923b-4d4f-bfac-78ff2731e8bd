<template>
  <div class="am-input-wrapper" :class="wrapperClasses">
    <!-- 标签 -->
    <label v-if="label" class="am-input__label" :for="inputId">
      {{ label }}
      <span v-if="required" class="am-input__required">*</span>
    </label>

    <!-- 输入框容器 -->
    <div class="am-input__container" :class="containerClasses">
      <!-- 前置图标 -->
      <div v-if="$slots.prefix || prefixIcon" class="am-input__prefix">
        <slot name="prefix">
          <el-icon v-if="prefixIcon">
            <component :is="prefixIcon" />
          </el-icon>
        </slot>
      </div>

      <!-- 输入框 -->
      <component
        :is="inputComponent"
        :id="inputId"
        ref="inputRef"
        v-model="inputValue"
        :type="type"
        :placeholder="placeholder"
        :disabled="disabled"
        :readonly="readonly"
        :maxlength="maxlength"
        :minlength="minlength"
        :rows="rows"
        :cols="cols"
        :resize="resize"
        :autocomplete="autocomplete"
        class="am-input__inner"
        :class="inputClasses"
        @input="handleInput"
        @change="handleChange"
        @focus="handleFocus"
        @blur="handleBlur"
        @keydown="handleKeydown"
        @keyup="handleKeyup"
        @keypress="handleKeypress"
      />

      <!-- 后置图标 -->
      <div v-if="$slots.suffix || suffixIcon || clearable || showPassword" class="am-input__suffix">
        <!-- 清除按钮 -->
        <button
          v-if="clearable && inputValue && !disabled && !readonly"
          type="button"
          class="am-input__clear"
          @click="handleClear"
        >
          <el-icon><CircleClose /></el-icon>
        </button>

        <!-- 密码显示切换 -->
        <button
          v-if="showPassword && type === 'password'"
          type="button"
          class="am-input__password-toggle"
          @click="togglePasswordVisibility"
        >
          <el-icon>
            <component :is="passwordVisible ? 'Hide' : 'View'" />
          </el-icon>
        </button>

        <!-- 自定义后置内容 -->
        <slot name="suffix">
          <el-icon v-if="suffixIcon">
            <component :is="suffixIcon" />
          </el-icon>
        </slot>
      </div>

      <!-- 字数统计 -->
      <div v-if="showWordLimit && maxlength" class="am-input__count">
        {{ inputValue.length }}/{{ maxlength }}
      </div>
    </div>

    <!-- 帮助文本 -->
    <div v-if="helpText || errorMessage" class="am-input__help">
      <span v-if="errorMessage" class="am-input__error">{{ errorMessage }}</span>
      <span v-else-if="helpText" class="am-input__help-text">{{ helpText }}</span>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, nextTick } from 'vue'
import { CircleClose, Hide, View } from '@element-plus/icons-vue'

const props = defineProps({
  modelValue: {
    type: [String, Number],
    default: ''
  },
  type: {
    type: String,
    default: 'text'
  },
  label: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: ''
  },
  size: {
    type: String,
    default: 'medium',
    validator: (value) => ['small', 'medium', 'large'].includes(value)
  },
  disabled: {
    type: Boolean,
    default: false
  },
  readonly: {
    type: Boolean,
    default: false
  },
  required: {
    type: Boolean,
    default: false
  },
  clearable: {
    type: Boolean,
    default: false
  },
  showPassword: {
    type: Boolean,
    default: false
  },
  showWordLimit: {
    type: Boolean,
    default: false
  },
  maxlength: {
    type: Number,
    default: null
  },
  minlength: {
    type: Number,
    default: null
  },
  rows: {
    type: Number,
    default: 3
  },
  cols: {
    type: Number,
    default: null
  },
  resize: {
    type: String,
    default: 'vertical',
    validator: (value) => ['none', 'both', 'horizontal', 'vertical'].includes(value)
  },
  autocomplete: {
    type: String,
    default: 'off'
  },
  prefixIcon: {
    type: String,
    default: ''
  },
  suffixIcon: {
    type: String,
    default: ''
  },
  errorMessage: {
    type: String,
    default: ''
  },
  helpText: {
    type: String,
    default: ''
  },
  variant: {
    type: String,
    default: 'default',
    validator: (value) => ['default', 'filled', 'outlined'].includes(value)
  }
})

const emit = defineEmits([
  'update:modelValue', 'input', 'change', 'focus', 'blur',
  'keydown', 'keyup', 'keypress', 'clear'
])

// 响应式数据
const inputRef = ref(null)
const focused = ref(false)
const passwordVisible = ref(false)
const inputId = `am-input-${Math.random().toString(36).substr(2, 9)}`

// 计算属性
const inputValue = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const inputComponent = computed(() => {
  return props.type === 'textarea' ? 'textarea' : 'input'
})

const actualType = computed(() => {
  if (props.type === 'password' && passwordVisible.value) {
    return 'text'
  }
  return props.type
})

const wrapperClasses = computed(() => ({
  [`am-input-wrapper--${props.size}`]: true,
  'am-input-wrapper--disabled': props.disabled,
  'am-input-wrapper--readonly': props.readonly,
  'am-input-wrapper--error': props.errorMessage,
  'am-input-wrapper--focused': focused.value
}))

const containerClasses = computed(() => ({
  [`am-input__container--${props.variant}`]: true,
  [`am-input__container--${props.size}`]: true,
  'am-input__container--disabled': props.disabled,
  'am-input__container--readonly': props.readonly,
  'am-input__container--error': props.errorMessage,
  'am-input__container--focused': focused.value,
  'am-input__container--textarea': props.type === 'textarea'
}))

const inputClasses = computed(() => ({
  'am-input__inner--textarea': props.type === 'textarea'
}))

// 方法
const handleInput = (event) => {
  const value = event.target.value
  emit('input', value, event)
  emit('update:modelValue', value)
}

const handleChange = (event) => {
  emit('change', event.target.value, event)
}

const handleFocus = (event) => {
  focused.value = true
  emit('focus', event)
}

const handleBlur = (event) => {
  focused.value = false
  emit('blur', event)
}

const handleKeydown = (event) => {
  emit('keydown', event)
}

const handleKeyup = (event) => {
  emit('keyup', event)
}

const handleKeypress = (event) => {
  emit('keypress', event)
}

const handleClear = () => {
  emit('update:modelValue', '')
  emit('clear')
  nextTick(() => {
    inputRef.value?.focus()
  })
}

const togglePasswordVisibility = () => {
  passwordVisible.value = !passwordVisible.value
}

const focus = () => {
  inputRef.value?.focus()
}

const blur = () => {
  inputRef.value?.blur()
}

const select = () => {
  inputRef.value?.select()
}

// 暴露方法
defineExpose({
  focus,
  blur,
  select,
  inputRef
})
</script>

<style lang="scss" scoped>
.am-input-wrapper {
  @apply w-full;
  
  &--small {
    .am-input__label {
      @apply text-sm mb-1;
    }
  }
  
  &--medium {
    .am-input__label {
      @apply text-sm mb-2;
    }
  }
  
  &--large {
    .am-input__label {
      @apply text-base mb-2;
    }
  }
}

.am-input__label {
  @apply block font-medium text-gray-700 mb-2;
}

.am-input__required {
  @apply text-red-500 ml-1;
}

.am-input__container {
  @apply relative flex items-center border rounded-lg transition-all duration-200;
  
  &--default {
    @apply border-gray-300 bg-white;
    
    &:hover:not(.am-input__container--disabled) {
      @apply border-gray-400;
    }
    
    &.am-input__container--focused {
      @apply border-primary-500 ring-2 ring-primary-100;
    }
  }
  
  &--filled {
    @apply border-transparent bg-gray-100;
    
    &:hover:not(.am-input__container--disabled) {
      @apply bg-gray-200;
    }
    
    &.am-input__container--focused {
      @apply bg-white border-primary-500 ring-2 ring-primary-100;
    }
  }
  
  &--outlined {
    @apply border-2 border-gray-300 bg-transparent;
    
    &:hover:not(.am-input__container--disabled) {
      @apply border-gray-400;
    }
    
    &.am-input__container--focused {
      @apply border-primary-500;
    }
  }
  
  &--small {
    @apply h-8 text-sm;
  }
  
  &--medium {
    @apply h-10 text-sm;
  }
  
  &--large {
    @apply h-12 text-base;
  }
  
  &--disabled {
    @apply bg-gray-100 border-gray-200 cursor-not-allowed;
  }
  
  &--readonly {
    @apply bg-gray-50 border-gray-200;
  }
  
  &--error {
    @apply border-red-500;
    
    &.am-input__container--focused {
      @apply ring-2 ring-red-100;
    }
  }
  
  &--textarea {
    @apply h-auto items-start;
  }
}

.am-input__prefix,
.am-input__suffix {
  @apply flex items-center px-3 text-gray-400;
}

.am-input__inner {
  @apply flex-1 px-3 py-2 bg-transparent border-none outline-none text-gray-900 placeholder-gray-400;
  
  &:disabled {
    @apply cursor-not-allowed text-gray-500;
  }
  
  &--textarea {
    @apply resize-none py-2;
    resize: var(--resize, vertical);
  }
}

.am-input__clear,
.am-input__password-toggle {
  @apply p-1 text-gray-400 hover:text-gray-600 transition-colors duration-200 cursor-pointer;
}

.am-input__count {
  @apply absolute bottom-2 right-2 text-xs text-gray-400 bg-white px-1;
}

.am-input__help {
  @apply mt-1 text-sm;
}

.am-input__error {
  @apply text-red-600;
}

.am-input__help-text {
  @apply text-gray-500;
}

// 响应式设计
@media (max-width: 768px) {
  .am-input__container {
    &--large {
      @apply h-10 text-sm;
    }
  }
}

// 暗色主题
.dark {
  .am-input__label {
    @apply text-gray-300;
  }
  
  .am-input__container {
    &--default {
      @apply border-gray-600 bg-gray-800;
      
      &:hover:not(.am-input__container--disabled) {
        @apply border-gray-500;
      }
      
      &.am-input__container--focused {
        @apply border-primary-400 ring-primary-900;
      }
    }
    
    &--filled {
      @apply bg-gray-700;
      
      &:hover:not(.am-input__container--disabled) {
        @apply bg-gray-600;
      }
      
      &.am-input__container--focused {
        @apply bg-gray-800 border-primary-400 ring-primary-900;
      }
    }
    
    &--outlined {
      @apply border-gray-600;
      
      &:hover:not(.am-input__container--disabled) {
        @apply border-gray-500;
      }
      
      &.am-input__container--focused {
        @apply border-primary-400;
      }
    }
    
    &--disabled {
      @apply bg-gray-900 border-gray-700;
    }
    
    &--readonly {
      @apply bg-gray-900 border-gray-700;
    }
    
    &--error {
      @apply border-red-400;
      
      &.am-input__container--focused {
        @apply ring-red-900;
      }
    }
  }
  
  .am-input__inner {
    @apply text-gray-100 placeholder-gray-500;
    
    &:disabled {
      @apply text-gray-600;
    }
  }
  
  .am-input__prefix,
  .am-input__suffix {
    @apply text-gray-500;
  }
  
  .am-input__clear,
  .am-input__password-toggle {
    @apply text-gray-500 hover:text-gray-300;
  }
  
  .am-input__count {
    @apply text-gray-500 bg-gray-800;
  }
  
  .am-input__help-text {
    @apply text-gray-400;
  }
  
  .am-input__error {
    @apply text-red-400;
  }
}
</style>
