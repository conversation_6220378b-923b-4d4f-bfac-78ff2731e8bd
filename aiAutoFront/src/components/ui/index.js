// AutoMatrix UI 组件库
// 基于 Tailwind CSS 的现代化组件库

import Card from './Card.vue'
import Button from './Button.vue'
import Table from './Table.vue'
import Input from './Input.vue'

// 组件列表
const components = [
  Card,
  Button,
  Table,
  Input
]

// 安装函数
const install = (app) => {
  components.forEach(component => {
    app.component(component.name || component.__name, component)
  })
}

// 导出组件
export {
  Card,
  Button,
  Table,
  Input,
  install
}

// 默认导出
export default {
  install,
  Card,
  Button,
  Table,
  Input
}

// 组件库信息
export const version = '1.0.0'
export const name = 'AutoMatrix UI'
