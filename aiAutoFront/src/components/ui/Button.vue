<template>
  <component
    :is="tag"
    :type="nativeType"
    :disabled="disabled || loading"
    :to="to"
    :href="href"
    :target="target"
    class="am-button"
    :class="[
      `am-button--${type}`,
      `am-button--${size}`,
      {
        'am-button--loading': loading,
        'am-button--disabled': disabled,
        'am-button--block': block,
        'am-button--round': round,
        'am-button--circle': circle,
        'am-button--icon-only': iconOnly
      }
    ]"
    @click="handleClick"
  >
    <!-- 加载图标 -->
    <span v-if="loading" class="am-button__loading">
      <div class="loading-spinner"></div>
    </span>

    <!-- 前置图标 -->
    <span v-if="$slots.icon && !loading" class="am-button__icon">
      <slot name="icon"></slot>
    </span>

    <!-- 按钮文字 -->
    <span v-if="!iconOnly" class="am-button__text">
      <slot></slot>
    </span>

    <!-- 后置图标 -->
    <span v-if="$slots.suffix" class="am-button__suffix">
      <slot name="suffix"></slot>
    </span>
  </component>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  type: {
    type: String,
    default: 'default',
    validator: (value) => [
      'default', 'primary', 'success', 'warning', 'danger', 
      'info', 'text', 'link'
    ].includes(value)
  },
  size: {
    type: String,
    default: 'medium',
    validator: (value) => ['mini', 'small', 'medium', 'large'].includes(value)
  },
  nativeType: {
    type: String,
    default: 'button',
    validator: (value) => ['button', 'submit', 'reset'].includes(value)
  },
  disabled: {
    type: Boolean,
    default: false
  },
  loading: {
    type: Boolean,
    default: false
  },
  block: {
    type: Boolean,
    default: false
  },
  round: {
    type: Boolean,
    default: false
  },
  circle: {
    type: Boolean,
    default: false
  },
  iconOnly: {
    type: Boolean,
    default: false
  },
  to: {
    type: [String, Object],
    default: null
  },
  href: {
    type: String,
    default: null
  },
  target: {
    type: String,
    default: null
  }
})

const emit = defineEmits(['click'])

// 计算组件标签
const tag = computed(() => {
  if (props.to) return 'router-link'
  if (props.href) return 'a'
  return 'button'
})

const handleClick = (event) => {
  if (props.disabled || props.loading) {
    event.preventDefault()
    return
  }
  emit('click', event)
}
</script>

<style lang="scss" scoped>
.am-button {
  @apply inline-flex items-center justify-center gap-2 font-medium rounded-lg border transition-all duration-200 cursor-pointer select-none;
  @apply focus:outline-none focus:ring-2 focus:ring-offset-2;
  
  // 默认样式
  &--default {
    @apply bg-white border-gray-300 text-gray-700 hover:bg-gray-50 hover:border-gray-400;
    @apply focus:ring-gray-500;
  }
  
  &--primary {
    @apply bg-primary-600 border-primary-600 text-white hover:bg-primary-700 hover:border-primary-700;
    @apply focus:ring-primary-500;
  }
  
  &--success {
    @apply bg-green-600 border-green-600 text-white hover:bg-green-700 hover:border-green-700;
    @apply focus:ring-green-500;
  }
  
  &--warning {
    @apply bg-yellow-600 border-yellow-600 text-white hover:bg-yellow-700 hover:border-yellow-700;
    @apply focus:ring-yellow-500;
  }
  
  &--danger {
    @apply bg-red-600 border-red-600 text-white hover:bg-red-700 hover:border-red-700;
    @apply focus:ring-red-500;
  }
  
  &--info {
    @apply bg-blue-600 border-blue-600 text-white hover:bg-blue-700 hover:border-blue-700;
    @apply focus:ring-blue-500;
  }
  
  &--text {
    @apply bg-transparent border-transparent text-gray-700 hover:bg-gray-100;
    @apply focus:ring-gray-500;
  }
  
  &--link {
    @apply bg-transparent border-transparent text-primary-600 hover:text-primary-700 hover:underline;
    @apply focus:ring-primary-500;
  }
  
  // 尺寸样式
  &--mini {
    @apply px-2 py-1 text-xs h-6;
  }
  
  &--small {
    @apply px-3 py-1.5 text-sm h-8;
  }
  
  &--medium {
    @apply px-4 py-2 text-sm h-10;
  }
  
  &--large {
    @apply px-6 py-3 text-base h-12;
  }
  
  // 状态样式
  &--loading {
    @apply pointer-events-none;
  }
  
  &--disabled {
    @apply opacity-50 cursor-not-allowed pointer-events-none;
  }
  
  &--block {
    @apply w-full;
  }
  
  &--round {
    @apply rounded-full;
  }
  
  &--circle {
    @apply rounded-full;
    
    &.am-button--mini {
      @apply w-6 h-6 p-0;
    }
    
    &.am-button--small {
      @apply w-8 h-8 p-0;
    }
    
    &.am-button--medium {
      @apply w-10 h-10 p-0;
    }
    
    &.am-button--large {
      @apply w-12 h-12 p-0;
    }
  }
  
  &--icon-only {
    @apply gap-0;
    
    &.am-button--mini {
      @apply w-6 px-0;
    }
    
    &.am-button--small {
      @apply w-8 px-0;
    }
    
    &.am-button--medium {
      @apply w-10 px-0;
    }
    
    &.am-button--large {
      @apply w-12 px-0;
    }
  }
}

.am-button__loading {
  @apply flex items-center justify-center;
}

.loading-spinner {
  @apply w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin;
}

.am-button__icon {
  @apply flex items-center justify-center;
  
  :deep(.el-icon) {
    @apply w-4 h-4;
  }
}

.am-button__text {
  @apply truncate;
}

.am-button__suffix {
  @apply flex items-center justify-center;
  
  :deep(.el-icon) {
    @apply w-4 h-4;
  }
}

// 按钮组合
.am-button + .am-button {
  @apply ml-2;
}

// 悬浮效果
.am-button:not(.am-button--disabled):not(.am-button--loading) {
  &:hover {
    @apply transform -translate-y-0.5 shadow-md;
  }
  
  &:active {
    @apply transform translate-y-0;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .am-button {
    &--large {
      @apply px-4 py-2 text-sm h-10;
    }
    
    &--medium {
      @apply px-3 py-1.5 text-sm h-8;
    }
  }
}

// 暗色主题
.dark {
  .am-button {
    &--default {
      @apply bg-gray-700 border-gray-600 text-gray-200 hover:bg-gray-600 hover:border-gray-500;
    }
    
    &--text {
      @apply text-gray-300 hover:bg-gray-700;
    }
    
    &--link {
      @apply text-primary-400 hover:text-primary-300;
    }
  }
}

// 特殊效果
.am-button--primary {
  background: linear-gradient(135deg, var(--color-primary-600), var(--color-primary-700));
  
  &:hover {
    background: linear-gradient(135deg, var(--color-primary-700), var(--color-primary-800));
  }
}

.am-button--success {
  background: linear-gradient(135deg, #10b981, #059669);
  
  &:hover {
    background: linear-gradient(135deg, #059669, #047857);
  }
}

.am-button--warning {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  
  &:hover {
    background: linear-gradient(135deg, #d97706, #b45309);
  }
}

.am-button--danger {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  
  &:hover {
    background: linear-gradient(135deg, #dc2626, #b91c1c);
  }
}
</style>
