<template>
  <div 
    class="am-card"
    :class="[
      `am-card--${variant}`,
      `am-card--${size}`,
      {
        'am-card--hoverable': hoverable,
        'am-card--bordered': bordered,
        'am-card--shadow': shadow,
        'am-card--glow': glow,
        'am-card--loading': loading
      }
    ]"
  >
    <!-- 加载状态 -->
    <div v-if="loading" class="am-card__loading">
      <div class="loading-spinner"></div>
      <span class="loading-text">{{ loadingText }}</span>
    </div>

    <!-- 卡片头部 -->
    <header v-if="$slots.header || title" class="am-card__header">
      <slot name="header">
        <div class="am-card__title">
          <h3 class="title-text">{{ title }}</h3>
          <p v-if="subtitle" class="title-subtitle">{{ subtitle }}</p>
        </div>
      </slot>
      <div v-if="$slots.extra" class="am-card__extra">
        <slot name="extra"></slot>
      </div>
    </header>

    <!-- 卡片内容 -->
    <main class="am-card__body">
      <slot></slot>
    </main>

    <!-- 卡片底部 -->
    <footer v-if="$slots.footer" class="am-card__footer">
      <slot name="footer"></slot>
    </footer>
  </div>
</template>

<script setup>
defineProps({
  title: {
    type: String,
    default: ''
  },
  subtitle: {
    type: String,
    default: ''
  },
  variant: {
    type: String,
    default: 'default',
    validator: (value) => ['default', 'primary', 'success', 'warning', 'danger'].includes(value)
  },
  size: {
    type: String,
    default: 'medium',
    validator: (value) => ['small', 'medium', 'large'].includes(value)
  },
  hoverable: {
    type: Boolean,
    default: false
  },
  bordered: {
    type: Boolean,
    default: false
  },
  shadow: {
    type: Boolean,
    default: true
  },
  glow: {
    type: Boolean,
    default: false
  },
  loading: {
    type: Boolean,
    default: false
  },
  loadingText: {
    type: String,
    default: '加载中...'
  }
})
</script>

<style lang="scss" scoped>
.am-card {
  @apply relative bg-white rounded-xl border border-gray-200 transition-all duration-300 overflow-hidden;
  
  // 变体样式
  &--default {
    @apply bg-white border-gray-200;
  }
  
  &--primary {
    @apply bg-primary-50 border-primary-200;
  }
  
  &--success {
    @apply bg-green-50 border-green-200;
  }
  
  &--warning {
    @apply bg-yellow-50 border-yellow-200;
  }
  
  &--danger {
    @apply bg-red-50 border-red-200;
  }
  
  // 尺寸样式
  &--small {
    .am-card__header {
      @apply p-4;
    }
    
    .am-card__body {
      @apply p-4;
    }
    
    .am-card__footer {
      @apply p-4;
    }
  }
  
  &--medium {
    .am-card__header {
      @apply p-6;
    }
    
    .am-card__body {
      @apply p-6;
    }
    
    .am-card__footer {
      @apply p-6;
    }
  }
  
  &--large {
    .am-card__header {
      @apply p-8;
    }
    
    .am-card__body {
      @apply p-8;
    }
    
    .am-card__footer {
      @apply p-8;
    }
  }
  
  // 状态样式
  &--hoverable {
    @apply cursor-pointer hover:shadow-lg hover:-translate-y-1;
  }
  
  &--bordered {
    @apply border-2;
  }
  
  &--shadow {
    @apply shadow-md;
  }
  
  &--glow {
    @apply shadow-glow hover:shadow-glow-lg;
  }
  
  &--loading {
    @apply pointer-events-none;
  }
}

.am-card__loading {
  @apply absolute inset-0 bg-white bg-opacity-90 flex flex-col items-center justify-center z-10;
}

.loading-spinner {
  @apply w-8 h-8 border-2 border-primary-200 border-t-primary-600 rounded-full animate-spin mb-2;
}

.loading-text {
  @apply text-sm text-gray-600;
}

.am-card__header {
  @apply border-b border-gray-200 flex items-start justify-between;
}

.am-card__title {
  @apply flex-1;
}

.title-text {
  @apply text-lg font-semibold text-gray-900 mb-1;
}

.title-subtitle {
  @apply text-sm text-gray-600;
}

.am-card__extra {
  @apply ml-4 flex-shrink-0;
}

.am-card__body {
  @apply text-gray-700;
}

.am-card__footer {
  @apply border-t border-gray-200 bg-gray-50;
}

// 响应式设计
@media (max-width: 768px) {
  .am-card {
    &--small,
    &--medium,
    &--large {
      .am-card__header,
      .am-card__body,
      .am-card__footer {
        @apply p-4;
      }
    }
  }
  
  .am-card__header {
    @apply flex-col items-start gap-3;
  }
  
  .am-card__extra {
    @apply ml-0 w-full;
  }
}

// 暗色主题
.dark {
  .am-card {
    @apply bg-gray-800 border-gray-700;
    
    &--default {
      @apply bg-gray-800 border-gray-700;
    }
    
    &--primary {
      @apply bg-primary-900 border-primary-700;
    }
    
    &--success {
      @apply bg-green-900 border-green-700;
    }
    
    &--warning {
      @apply bg-yellow-900 border-yellow-700;
    }
    
    &--danger {
      @apply bg-red-900 border-red-700;
    }
  }
  
  .am-card__header {
    @apply border-gray-700;
  }
  
  .title-text {
    @apply text-white;
  }
  
  .title-subtitle {
    @apply text-gray-300;
  }
  
  .am-card__body {
    @apply text-gray-300;
  }
  
  .am-card__footer {
    @apply border-gray-700 bg-gray-900;
  }
  
  .am-card__loading {
    @apply bg-gray-800 bg-opacity-90;
  }
  
  .loading-text {
    @apply text-gray-300;
  }
}
</style>
