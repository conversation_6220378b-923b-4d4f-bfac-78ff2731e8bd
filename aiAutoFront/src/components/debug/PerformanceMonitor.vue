<template>
  <div v-if="showMonitor" class="performance-monitor">
    <div class="monitor-header">
      <h3 class="monitor-title">性能监控</h3>
      <button @click="toggleMonitor" class="monitor-toggle">
        {{ isExpanded ? '收起' : '展开' }}
      </button>
    </div>
    
    <div v-show="isExpanded" class="monitor-content">
      <!-- 基本性能指标 -->
      <div class="metric-group">
        <h4 class="group-title">基本指标</h4>
        <div class="metrics">
          <div class="metric">
            <span class="metric-label">FPS:</span>
            <span class="metric-value" :class="getFPSClass(fps)">{{ fps }}</span>
          </div>
          <div class="metric">
            <span class="metric-label">内存:</span>
            <span class="metric-value">{{ memoryUsage }}</span>
          </div>
          <div class="metric">
            <span class="metric-label">加载时间:</span>
            <span class="metric-value">{{ loadTime }}ms</span>
          </div>
        </div>
      </div>

      <!-- 网络状态 -->
      <div class="metric-group">
        <h4 class="group-title">网络状态</h4>
        <div class="metrics">
          <div class="metric">
            <span class="metric-label">连接类型:</span>
            <span class="metric-value">{{ networkInfo.effectiveType }}</span>
          </div>
          <div class="metric">
            <span class="metric-label">下行速度:</span>
            <span class="metric-value">{{ networkInfo.downlink }} Mbps</span>
          </div>
          <div class="metric">
            <span class="metric-label">RTT:</span>
            <span class="metric-value">{{ networkInfo.rtt }}ms</span>
          </div>
        </div>
      </div>

      <!-- 资源统计 -->
      <div class="metric-group">
        <h4 class="group-title">资源统计</h4>
        <div class="metrics">
          <div class="metric">
            <span class="metric-label">DOM 节点:</span>
            <span class="metric-value">{{ domNodes }}</span>
          </div>
          <div class="metric">
            <span class="metric-label">事件监听器:</span>
            <span class="metric-value">{{ eventListeners }}</span>
          </div>
          <div class="metric">
            <span class="metric-label">活跃定时器:</span>
            <span class="metric-value">{{ activeTimers }}</span>
          </div>
        </div>
      </div>

      <!-- 性能建议 -->
      <div class="metric-group" v-if="suggestions.length > 0">
        <h4 class="group-title">性能建议</h4>
        <ul class="suggestions">
          <li v-for="suggestion in suggestions" :key="suggestion" class="suggestion">
            {{ suggestion }}
          </li>
        </ul>
      </div>

      <!-- 操作按钮 -->
      <div class="monitor-actions">
        <button @click="clearMetrics" class="action-btn">清除数据</button>
        <button @click="exportMetrics" class="action-btn">导出数据</button>
        <button @click="runPerformanceTest" class="action-btn">性能测试</button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed } from 'vue'
import { performanceMonitor } from '@/utils/performance'

const props = defineProps({
  enabled: {
    type: Boolean,
    default: process.env.NODE_ENV === 'development'
  }
})

// 响应式数据
const showMonitor = ref(props.enabled)
const isExpanded = ref(false)
const fps = ref(0)
const memoryUsage = ref('0 MB')
const loadTime = ref(0)
const domNodes = ref(0)
const eventListeners = ref(0)
const activeTimers = ref(0)

const networkInfo = ref({
  effectiveType: 'unknown',
  downlink: 0,
  rtt: 0
})

const suggestions = ref([])

// 性能监控实例
const monitor = performanceMonitor()

// 计算属性
const getFPSClass = (fps) => {
  if (fps >= 55) return 'metric-value--good'
  if (fps >= 30) return 'metric-value--warning'
  return 'metric-value--poor'
}

// FPS 监控
let fpsCounter = 0
let lastTime = performance.now()

const updateFPS = () => {
  const now = performance.now()
  const delta = now - lastTime
  
  if (delta >= 1000) {
    fps.value = Math.round((fpsCounter * 1000) / delta)
    fpsCounter = 0
    lastTime = now
  }
  
  fpsCounter++
  
  if (showMonitor.value) {
    requestAnimationFrame(updateFPS)
  }
}

// 内存监控
const updateMemoryUsage = () => {
  if (performance.memory) {
    const used = performance.memory.usedJSHeapSize
    const total = performance.memory.totalJSHeapSize
    const usedMB = Math.round(used / 1024 / 1024)
    const totalMB = Math.round(total / 1024 / 1024)
    memoryUsage.value = `${usedMB}/${totalMB} MB`
    
    // 内存使用率过高时添加建议
    if (used / total > 0.8) {
      addSuggestion('内存使用率过高，考虑优化内存使用')
    }
  }
}

// 网络信息监控
const updateNetworkInfo = () => {
  if (navigator.connection) {
    const conn = navigator.connection
    networkInfo.value = {
      effectiveType: conn.effectiveType || 'unknown',
      downlink: conn.downlink || 0,
      rtt: conn.rtt || 0
    }
    
    // 网络状况差时添加建议
    if (conn.effectiveType === 'slow-2g' || conn.effectiveType === '2g') {
      addSuggestion('网络连接较慢，建议优化资源加载')
    }
  }
}

// DOM 节点统计
const updateDOMStats = () => {
  domNodes.value = document.querySelectorAll('*').length
  
  // DOM 节点过多时添加建议
  if (domNodes.value > 1500) {
    addSuggestion('DOM 节点过多，考虑使用虚拟滚动或分页')
  }
}

// 事件监听器统计（简化版本）
const updateEventListeners = () => {
  // 这是一个简化的实现，实际项目中需要更复杂的统计
  eventListeners.value = window.getEventListeners ? 
    Object.keys(window.getEventListeners(document)).length : 0
}

// 定时器统计（简化版本）
const updateActiveTimers = () => {
  // 这是一个简化的实现
  activeTimers.value = 0 // 实际需要跟踪所有活跃的定时器
}

// 添加建议
const addSuggestion = (suggestion) => {
  if (!suggestions.value.includes(suggestion)) {
    suggestions.value.push(suggestion)
  }
}

// 方法
const toggleMonitor = () => {
  isExpanded.value = !isExpanded.value
}

const clearMetrics = () => {
  suggestions.value = []
  monitor.metrics.errors = []
}

const exportMetrics = () => {
  const data = {
    timestamp: new Date().toISOString(),
    fps: fps.value,
    memory: memoryUsage.value,
    loadTime: loadTime.value,
    domNodes: domNodes.value,
    networkInfo: networkInfo.value,
    suggestions: suggestions.value,
    ...monitor.generateReport()
  }
  
  const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `performance-report-${Date.now()}.json`
  a.click()
  URL.revokeObjectURL(url)
}

const runPerformanceTest = async () => {
  console.log('🚀 开始性能测试...')
  
  // 测试渲染性能
  await monitor.measureRenderTime(async () => {
    // 模拟一些DOM操作
    const testDiv = document.createElement('div')
    testDiv.innerHTML = '<div>'.repeat(100) + '</div>'.repeat(100)
    document.body.appendChild(testDiv)
    await new Promise(resolve => setTimeout(resolve, 100))
    document.body.removeChild(testDiv)
  })
  
  console.log('✅ 性能测试完成')
  console.log('📊 渲染时间:', monitor.metrics.renderTime, 'ms')
}

// 定期更新指标
let updateInterval

const startMonitoring = () => {
  if (!showMonitor.value) return
  
  updateFPS()
  
  updateInterval = setInterval(() => {
    updateMemoryUsage()
    updateNetworkInfo()
    updateDOMStats()
    updateEventListeners()
    updateActiveTimers()
  }, 2000)
}

const stopMonitoring = () => {
  if (updateInterval) {
    clearInterval(updateInterval)
    updateInterval = null
  }
}

// 生命周期
onMounted(() => {
  if (props.enabled) {
    // 获取页面加载时间
    monitor.measureLoadTime()
    loadTime.value = monitor.metrics.loadTime
    
    startMonitoring()
    
    // 监听错误
    window.addEventListener('error', (event) => {
      monitor.collectError(event.error)
      addSuggestion('检测到JavaScript错误，请查看控制台')
    })
    
    // 监听未处理的Promise拒绝
    window.addEventListener('unhandledrejection', (event) => {
      monitor.collectError(new Error(event.reason))
      addSuggestion('检测到未处理的Promise拒绝')
    })
  }
})

onUnmounted(() => {
  stopMonitoring()
})

// 暴露方法给父组件
defineExpose({
  show: () => { showMonitor.value = true },
  hide: () => { showMonitor.value = false },
  toggle: () => { showMonitor.value = !showMonitor.value },
  exportMetrics,
  runPerformanceTest
})
</script>

<style lang="scss" scoped>
.performance-monitor {
  @apply fixed bottom-4 right-4 bg-white border border-gray-300 rounded-lg shadow-lg z-50 max-w-sm;
  min-width: 300px;
}

.monitor-header {
  @apply flex items-center justify-between p-3 border-b border-gray-200 bg-gray-50 rounded-t-lg;
}

.monitor-title {
  @apply text-sm font-semibold text-gray-800 m-0;
}

.monitor-toggle {
  @apply text-xs px-2 py-1 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors;
}

.monitor-content {
  @apply p-3 max-h-96 overflow-y-auto;
}

.metric-group {
  @apply mb-4;
  
  &:last-child {
    @apply mb-0;
  }
}

.group-title {
  @apply text-xs font-semibold text-gray-600 mb-2 uppercase tracking-wide;
}

.metrics {
  @apply space-y-1;
}

.metric {
  @apply flex justify-between text-xs;
}

.metric-label {
  @apply text-gray-600;
}

.metric-value {
  @apply font-mono text-gray-900;
  
  &--good {
    @apply text-green-600;
  }
  
  &--warning {
    @apply text-yellow-600;
  }
  
  &--poor {
    @apply text-red-600;
  }
}

.suggestions {
  @apply list-none p-0 m-0 space-y-1;
}

.suggestion {
  @apply text-xs text-orange-600 bg-orange-50 p-2 rounded;
}

.monitor-actions {
  @apply flex gap-2 mt-3 pt-3 border-t border-gray-200;
}

.action-btn {
  @apply flex-1 text-xs px-2 py-1 bg-gray-100 text-gray-700 rounded hover:bg-gray-200 transition-colors;
}

// 暗色主题
.dark {
  .performance-monitor {
    @apply bg-gray-800 border-gray-600;
  }
  
  .monitor-header {
    @apply bg-gray-700 border-gray-600;
  }
  
  .monitor-title {
    @apply text-gray-200;
  }
  
  .group-title {
    @apply text-gray-400;
  }
  
  .metric-label {
    @apply text-gray-400;
  }
  
  .metric-value {
    @apply text-gray-200;
  }
  
  .suggestion {
    @apply text-orange-300 bg-orange-900;
  }
  
  .action-btn {
    @apply bg-gray-700 text-gray-300 hover:bg-gray-600;
  }
}
</style>
