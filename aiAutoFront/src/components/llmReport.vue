<template>
  <!-- LLM查询控制区域 -->
  <el-row class="section" :gutter="20">
    <el-col :span="24">
      <el-select filterable v-model="store.llm_project_list_id" placeholder="请选择项目" style="width: 10%;" >
        <el-option v-for="item in store.project_list" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <el-button type="primary" @click="store.getLlmReport('llmProjectPie')" style="margin-left: 1%;">llm查询</el-button>
      <el-button plain @click="store.llmresetRagQuery">重置</el-button>

    </el-col>
  </el-row>

  <!-- 第二部分：项目卡片 + 饼图 -->
  <el-row class="section" :gutter="20">
    <el-col :span="16">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>{{ llmCollect.project_name }}</span>
          </div>
        </template>
        <!-- 第一行4个卡片 -->
        <el-row :gutter="16" style="margin-bottom: 16px;">
          <el-col :span="6" v-for="i in props.llmReportData" :key="'top'+i">
            <el-card class="small-card" shadow="never">
              <div class="card-text">{{i.label}}</div>
              <div class="card-number">{{i.value}}</div>
            </el-card>
          </el-col>
        </el-row>
      </el-card>
    </el-col>

    <!-- 右侧饼图 -->
    <el-col :span="8">
      <div id="llmProjectPie" class="pie-container" ></div>
    </el-col>
  </el-row>
    <el-card class="comparison-query-card" shadow="hover" style="width: 93.5%;margin-top: 1%;">
        <template #header>
          <div class="card-header">
            <h3 class="section-title">RAG指标查询</h3>
          </div>
        </template>

        <!-- 查询控制区域 -->
        <el-row :gutter="20" class="comparison-query-row">
          <!-- 执行次数对比 -->
          <el-col :span="8">
            <div class="query-group">
              <label class="query-group-label">用例集</label>
               <el-cascader
          v-model="store.llmCheckOPtionsValue"
          :options="store.ragCheckOPtions"
          :props="store.ragprops"

          clearable
          placeholder="请选择项目/需求/用例集"
          style="width: 100%; max-width: 350px;"
          size="default"
        />

            </div>
          </el-col>

          <!-- 测试套件选择 -->
          <el-col :span="8">
            <div class="query-group">
              <label class="query-group-label">版本号</label>
             <el-input
          v-model="store.llm_input_version"
          placeholder="请输入版本号"
          size="default"
          clearable
          style="width: 50%;"
        />
            </div>
          </el-col>

          <!-- 操作按钮 -->
          <el-col :span="8">
            <div class="query-group">
              <label class="query-group-label">操作</label>
              <div class="action-buttons">
                <el-button
                  type="primary"
                  @click="store.handleLlmCheckChange"
                  size="default"
                  style="margin-right: 12px;"
                  :icon="Search"
                >
                  查询
                </el-button>

              </div>
            </div>
          </el-col>
        </el-row>
      </el-card>

  <!-- 第三部分：折线图 + 饼图 -->
  <el-row class="section" :gutter="20">
    <!-- 左侧折线图 -->
    <el-col :span="16">
      <div id="trendChart32" class="chart-container" style="height: 400px;"></div>
    </el-col>

    <!-- 右侧饼图 -->
    <el-col :span="8">
      <el-select
        v-model="store.selectedAnalysisMetric"
        placeholder="选择分析指标"
        style="width: 80%; margin-bottom: 10px;"
      >
        <el-option
          v-for="metric in store.analysisMetrics"
          :key="metric.value"
          :label="metric.label"
          :value="metric.value"
        ></el-option>
      </el-select>
      <div id="llmAnalysisPie" class="pie-container"></div>
    </el-col>
  </el-row>

  <!-- 第四部分：LLM对比结果列表 -->
  <el-row class="section" :gutter="20">
    <el-col :span="24">
      <el-card class="comparison-query-card" shadow="hover" style="width: 93.5%;">
        <template #header>
          <div class="card-header">
            <h3 class="section-title">LLM对比结果分析</h3>
            <el-tag type="info" effect="light">执行结果对比查询</el-tag>
          </div>
        </template>

        <!-- 查询控制区域 -->
        <el-row :gutter="20" class="comparison-query-row">
          <!-- 执行次数对比 -->
          <el-col :span="8">
            <div class="query-group">
              <label class="query-group-label">执行次数对比</label>
              <div class="execution-times-controls">
                <span class="input-label">前</span>
                <el-input
                  v-model="store.execution_times_before"
                  placeholder="次数"
                  size="default"
                  style="width: 90px;"
                  type="number"
                  :min="1"
                />
                <span class="separator">对比</span>
                <span class="input-label">后</span>
                <el-input
                  v-model="store.execution_times_after"
                  placeholder="次数"
                  size="default"
                  style="width: 90px;"
                  type="number"
                  :min="1"
                />
              </div>
            </div>
          </el-col>

          <!-- 测试套件选择 -->
          <el-col :span="8">
            <div class="query-group">
              <label class="query-group-label">测试套件</label>
              <el-select
                v-model="store.suiteValue"
                @focus="store.getSuiteInProject()"
                placeholder="请选择测试套件"
                size="default"
                style="width: 50%;"
                clearable
              >
                <el-option
                  v-for="item in store.suite_list"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </div>
          </el-col>

          <!-- 操作按钮 -->
          <el-col :span="8">
            <div class="query-group">
              <label class="query-group-label">操作</label>
              <div class="action-buttons">
                <el-button
                  type="primary"
                  @click="store.getllmCompareResult('list')"
                  size="default"
                  style="margin-right: 12px;"
                  :icon="Search"
                >
                  查询
                </el-button>
              </div>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- LLM对比结果列表 -->
      <div v-if="store.llm_compare_result_data && store.llm_compare_result_data.length > 0" class="comparison-list">
        <el-card v-for="(item, index) in store.llm_compare_result_data" :key="index" class="comparison-item mb-4">
          <template #header>
            <div class="card-header">
              <el-tag type="info" size="small">Suite ID: {{ store.llm_suite_id || 23 }}</el-tag>
            </div>
          </template>

          <!-- 用户输入和响应 -->
          <el-row :gutter="20" class="mb-3">
            <el-col :span="12">
              <div class="info-section">
                <h5 class="section-title">用户输入</h5>
                <p class="content-text">{{ item.user_input }}</p>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-section">
                <h5 class="section-title">参考答案</h5>
                <p class="content-text">{{ item.reference }}</p>
              </div>
            </el-col>
          </el-row>

          <!-- 检索上下文 -->
          <el-row class="mb-3">
            <el-col :span="24">
              <div class="info-section">
                <h5 class="section-title">检索上下文</h5>
                <p class="content-text">{{ item.retrieved_contexts }}</p>
              </div>
            </el-col>
          </el-row>

          <!-- 模型响应 -->
          <el-row class="mb-3">
            <el-col :span="24">
              <div class="info-section">
                <h5 class="section-title">模型响应</h5>
                <div class="response-content">
                  <el-scrollbar height="200px">
                    <p class="content-text">{{ item.response }}</p>
                  </el-scrollbar>
                </div>
              </div>
            </el-col>
          </el-row>

          <!-- 评估指标对比 -->
          <el-row>
            <el-col :span="24">
              <div class="metrics-section">
                <h5 class="section-title">评估指标对比</h5>
                <el-table :data="item.compare" border size="small" class="metrics-table" style="width: 100%;">
                  <el-table-column prop="field" label="指标名称" min-width="180">
                    <template #default="{ row }">
                      <span class="metric-name">{{ formatMetricName(row.field) }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="before_value" label="执行前" min-width="140" align="center">
                    <template #default="{ row }">
                      <el-tag :type="getValueTagType(row.before_value)" size="small">
                        {{ formatValue(row.before_value) }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="after_value" label="执行后" min-width="140" align="center">
                    <template #default="{ row }">
                      <el-tag :type="getValueTagType(row.after_value)" size="small">
                        {{ formatValue(row.after_value) }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="is_different" label="变化状态" min-width="140" align="center">
                    <template #default="{ row }">
                      <el-tag :type="row.is_different ? 'warning' : 'success'" size="small">
                        {{ row.is_different ? '有变化' : '无变化' }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column label="变化趋势" min-width="160" align="center">
                    <template #default="{ row }">
                      <span class="trend-indicator">
                        <el-icon v-if="getTrend(row) === 'up'" color="#67C23A" size="16">
                          <ArrowUp />
                        </el-icon>
                        <el-icon v-else-if="getTrend(row) === 'down'" color="#F56C6C" size="16">
                          <ArrowDown />
                        </el-icon>
                        <el-icon v-else color="#909399" size="16">
                          <Minus />
                        </el-icon>
                        {{ getTrendText(row) }}
                      </span>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </el-col>
          </el-row>
        </el-card>
      </div>

      <!-- 空状态 -->
      <el-empty v-else description="暂无对比数据，请点击查询按钮获取数据" />
    </el-col>
  </el-row>
</template>

<script setup>
import { ref, onMounted, defineProps } from 'vue'
import * as echarts from 'echarts'
import { reportStore } from '@/pinia/reportModule'
import { Search, ArrowUp, ArrowDown, Minus } from '@element-plus/icons-vue'

const props = defineProps({
  llmReportData: {
    type: Array,
    required: true
  },
  llmCollect: {
    type: Object,
    required: true
  }
})

const store = reportStore()

// 格式化指标名称
const formatMetricName = (field) => {
  const metricNames = {
    'context_precision': '上下文精确度',
    'context_recall': '上下文召回率',
    'faithfulness': '忠实度',
    'answer_relevancy': '答案相关性',
    'context_entity_recall': '上下文实体召回率'
  }
  return metricNames[field] || field
}

// 格式化数值
const formatValue = (value) => {
  if (typeof value === 'number') {
    return value.toFixed(3)
  }
  return value || 'N/A'
}

// 获取数值标签类型
const getValueTagType = (value) => {
  if (typeof value === 'number') {
    if (value >= 0.8) return 'success'
    if (value >= 0.6) return 'warning'
    return 'danger'
  }
  return 'info'
}

// 获取趋势
const getTrend = (row) => {
  const before = parseFloat(row.before_value)
  const after = parseFloat(row.after_value)

  if (isNaN(before) || isNaN(after)) return 'none'

  if (after > before) return 'up'
  if (after < before) return 'down'
  return 'none'
}

// 获取趋势文本
const getTrendText = (row) => {
  const trend = getTrend(row)
  const before = parseFloat(row.before_value)
  const after = parseFloat(row.after_value)

  if (trend === 'none' || isNaN(before) || isNaN(after)) return '无变化'

  const diff = Math.abs(after - before)
  const percentage = ((diff / before) * 100).toFixed(1)

  if (trend === 'up') return `提升 ${percentage}%`
  if (trend === 'down') return `下降 ${percentage}%`
  return '无变化'
}

onMounted(() => {
  store.getLlmReport('llmProjectPie')
  store.getllmReportStats('llmAnalysisPie')
  store.getllmReportStatistics('trendChart32')
})
</script>

<style scoped>
/* 通用样式 */
.section {
  margin-bottom: 20px;
}




/* 小卡片样式 */
.small-card {
 height: 140px;  /* 小卡片高度 */
  padding: 16px;

  text-align: center;
  border: none;
}


.small-card .card-text {
  color: #606266;
  font-size: 14px;
  margin-bottom: 12px;
}

.small-card .card-number {
  color: #3f8bfa;
  font-size: 24px;
  font-weight: 600;
}

/* 对比结果查询卡片样式 */
.comparison-query-card {
  margin-bottom: 28px;
  border-radius: 12px;
  border: 1px solid #e4e7ed;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  background: linear-gradient(135deg, #f8faff 0%, #ffffff 100%);
}

.comparison-query-card:hover {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

.comparison-query-card .card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 0;
  padding: 16px 20px;
  border-bottom: 1px solid #f0f2f5;
  background-color: rgba(64, 158, 255, 0.05);
}

.comparison-query-card .section-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
  position: relative;
  padding-left: 12px;
}

.comparison-query-card .section-title:before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 18px;
  background-color: #409eff;
  border-radius: 2px;
}
.pie-container {

  height: 399px;
  width: 80%;  /* 改为100%填满所在列 */
  border: 2px solid #ebedf0;
  border-radius: 4px;
  padding: 16px;
}

.comparison-query-row {
  align-items: flex-end;
  padding: 20px 0;
  min-height: 100px;
}

.query-group {
  display: flex;
  flex-direction: column;
  gap: 10px;
  height: 100%;
  justify-content: flex-end;
}

.query-group-label {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 6px;
  line-height: 1.4;
  position: relative;
  padding-left: 8px;
}

.query-group-label:before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 14px;
  background-color: #409eff;
  border-radius: 1.5px;
}

.execution-times-controls {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
  padding: 12px;

}

.input-label {
  font-size: 13px;
  color: #666;
  font-weight: 500;
  min-width: 24px;
  text-align: center;
}

.separator {
  font-size: 14px;
  color: #409eff;
  font-weight: 600;
  padding: 0 8px;
  background-color: rgba(64, 158, 255, 0.1);
  border-radius: 4px;
  line-height: 1.6;
}

.action-buttons {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
  justify-content: flex-start;
}

/* 对比结果列表样式 */
.comparison-list {
  margin-top: 20px;
}

.comparison-item {
  margin-bottom: 20px;
  border-radius: 12px;
  border: 1px solid #e4e7ed;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}

.comparison-item:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

.comparison-item .card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 20px;
  border-bottom: 1px solid #f0f2f5;
  background-color: rgba(64, 158, 255, 0.03);
}

.info-section {
  padding: 16px;
  background-color: #fafbfc;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.info-section .section-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin: 0 0 8px 0;
  position: relative;
  padding-left: 8px;
}

.info-section .section-title:before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 12px;
  background-color: #409eff;
  border-radius: 1.5px;
}

.content-text {
  font-size: 13px;
  color: #666;
  line-height: 1.6;
  margin: 0;
  word-break: break-word;
}

.response-content {
  background-color: #f8f9fa;
  border-radius: 6px;
  padding: 12px;
  border: 1px solid #e9ecef;
}

/* 评估指标表格样式 */
.metrics-section {
  padding: 16px;
  background-color: #fafbfc;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.metrics-section .section-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin: 0 0 12px 0;
  position: relative;
  padding-left: 8px;
}

.metrics-section .section-title:before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 12px;
  background-color: #409eff;
  border-radius: 1.5px;
}

.metrics-table {
  border-radius: 8px;
  overflow: hidden;
}

.metric-name {
  font-weight: 500;
  color: #333;
}

.trend-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  font-weight: 500;
}

/* 通用样式 */
.section {
  margin-bottom: 20px;
}

.mb-3 {
  margin-bottom: 16px;
}

.mb-4 {
  margin-bottom: 20px;
}

.ml-2 {
  margin-left: 8px;
}

/* 图表容器样式 */
.chart-container {
  height: 300px;
  width: 100%;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  background-color: #fff;
}
</style>
