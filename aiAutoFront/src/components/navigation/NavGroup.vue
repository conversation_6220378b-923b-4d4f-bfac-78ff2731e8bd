<template>
  <div class="nav-group">
    <!-- 组标题 -->
    <div
      class="nav-group__header"
      @click="toggleExpanded"
      :class="{
        'nav-group__header--expanded': expanded,
        'nav-group__header--collapsed': collapsed
      }"
    >
      <div class="nav-group__icon" v-if="icon">
        <el-icon>
          <component :is="icon" />
        </el-icon>
      </div>

      <span class="nav-group__title" v-show="!collapsed">{{ title }}</span>

      <div
        class="nav-group__arrow"
        v-show="!collapsed"
        :class="{ 'nav-group__arrow--expanded': expanded }"
      >
        <el-icon><ArrowRight /></el-icon>
      </div>

      <!-- 折叠状态下的提示 -->
      <el-tooltip
        v-if="collapsed"
        :content="title"
        placement="right"
        :show-after="500"
      >
        <div class="nav-group__tooltip-target"></div>
      </el-tooltip>
    </div>

    <!-- 子项列表 -->
    <transition
      name="nav-group-items"
      @enter="onEnter"
      @leave="onLeave"
    >
      <div
        class="nav-group__items"
        v-show="expanded && !collapsed"
        ref="itemsRef"
      >
        <div class="nav-group__items-inner">
          <slot />
        </div>
      </div>
    </transition>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue'
import { ArrowRight } from '@element-plus/icons-vue'

const props = defineProps({
  title: {
    type: String,
    required: true
  },
  icon: {
    type: String,
    default: null
  },
  collapsed: {
    type: Boolean,
    default: false
  },
  defaultExpanded: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['toggle'])

const expanded = ref(props.defaultExpanded)
const itemsRef = ref(null)

const toggleExpanded = () => {
  if (!props.collapsed) {
    expanded.value = !expanded.value
    emit('toggle', expanded.value)
  }
}

// 当侧边栏折叠时，重置展开状态
watch(() => props.collapsed, (newCollapsed) => {
  if (newCollapsed) {
    expanded.value = false
  } else {
    expanded.value = props.defaultExpanded
  }
})

// 动画钩子
const onEnter = (el) => {
  el.style.height = '0'
  el.offsetHeight // 触发重排
  el.style.height = el.scrollHeight + 'px'
}

const onLeave = (el) => {
  el.style.height = el.scrollHeight + 'px'
  el.offsetHeight // 触发重排
  el.style.height = '0'
}
</script>

<style lang="scss" scoped>
.nav-group {
  @apply mb-2;
}

.nav-group__header {
  @apply flex items-center gap-3 px-3 py-2.5 rounded-lg text-gray-700 hover:bg-gray-50 cursor-pointer transition-all duration-200;

  &:hover {
    transform: translateX(2px);
  }

  &--collapsed {
    @apply justify-center px-2;
  }

  &--expanded {
    .nav-group__arrow {
      @apply transform rotate-90;
    }
  }
}

.nav-group__icon {
  @apply w-5 h-5 text-gray-500 flex-shrink-0 transition-colors duration-200;

  .nav-group__header--collapsed & {
    @apply w-6 h-6;
  }
}

.nav-group__title {
  @apply flex-1 text-sm font-medium;
}

.nav-group__arrow {
  @apply w-4 h-4 text-gray-400 transition-transform duration-200;
}

.nav-group__tooltip-target {
  @apply absolute inset-0;
}

.nav-group__items {
  @apply overflow-hidden transition-all duration-300 ease-in-out;
}

.nav-group__items-inner {
  @apply ml-3 border-l border-gray-200 pl-3 py-1;
}

// 动画类
.nav-group-items-enter-active,
.nav-group-items-leave-active {
  transition: height 0.3s ease-in-out;
}

.nav-group-items-enter-from,
.nav-group-items-leave-to {
  height: 0;
}

// 悬浮效果
.nav-group__header {
  &::before {
    content: '';
    @apply absolute inset-0 rounded-lg bg-gradient-to-r from-primary-500 to-secondary-500 opacity-0 transition-opacity duration-300;
    z-index: -1;
  }

  &:hover::before {
    @apply opacity-5;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .nav-group__header {
    @apply py-3;
  }

  .nav-group__items-inner {
    @apply ml-2 pl-2;
  }
}

// 暗色主题
.dark {
  .nav-group__header {
    @apply text-gray-300 hover:bg-gray-700 hover:text-white;
  }

  .nav-group__items-inner {
    @apply border-gray-600;
  }
}
</style>
