<template>
  <div class="nav-section" :class="{ 'nav-section--collapsed': collapsed }">
    <!-- 分组标题 -->
    <div class="nav-section__title" v-show="!collapsed && title">
      {{ title }}
    </div>
    
    <!-- 分组内容 -->
    <div class="nav-section__content">
      <slot />
    </div>
    
    <!-- 分隔线 -->
    <div class="nav-section__divider" v-if="showDivider && !collapsed"></div>
  </div>
</template>

<script setup>
defineProps({
  title: {
    type: String,
    default: ''
  },
  collapsed: {
    type: Boolean,
    default: false
  },
  showDivider: {
    type: Boolean,
    default: false
  }
})
</script>

<style lang="scss" scoped>
.nav-section {
  @apply mb-6;
  
  &:last-child {
    @apply mb-0;
  }
  
  &--collapsed {
    @apply mb-4;
  }
}

.nav-section__title {
  @apply text-xs font-semibold text-gray-400 uppercase tracking-wider mb-3 px-3;
  animation: fadeInUp 0.3s ease-out;
}

.nav-section__content {
  @apply space-y-1;
}

.nav-section__divider {
  @apply mt-4 mx-3 border-t border-gray-200;
}

// 动画
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 暗色主题
.dark {
  .nav-section__title {
    @apply text-gray-500;
  }
  
  .nav-section__divider {
    @apply border-gray-600;
  }
}
</style>
