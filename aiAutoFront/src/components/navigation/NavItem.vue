<template>
  <component
    :is="isExternal ? 'a' : 'router-link'"
    :to="isExternal ? undefined : to"
    :href="isExternal ? to : undefined"
    :target="isExternal ? '_blank' : undefined"
    class="nav-item"
    :class="{
      'nav-item--active': isActive,
      'nav-item--collapsed': collapsed,
      'nav-item--sub': isSubItem
    }"
    @click="handleClick"
  >
    <!-- 图标 -->
    <div class="nav-item__icon" v-if="icon">
      <el-icon>
        <component :is="icon" />
      </el-icon>
    </div>

    <!-- 文字内容 -->
    <div class="nav-item__content" v-show="!collapsed">
      <span class="nav-item__text">{{ title }}</span>

      <!-- 徽章 -->
      <span
        v-if="badge"
        class="nav-item__badge"
        :class="{
          'nav-item__badge--new': badgeType === 'new',
          'nav-item__badge--count': badgeType === 'count'
        }"
      >
        {{ badge }}
      </span>
    </div>

    <!-- 展开箭头 -->
    <div
      class="nav-item__arrow"
      v-if="hasChildren && !collapsed"
      :class="{ 'nav-item__arrow--expanded': expanded }"
    >
      <el-icon><ArrowRight /></el-icon>
    </div>

    <!-- 活动指示器 -->
    <div class="nav-item__indicator" v-if="isActive"></div>

    <!-- Tooltip for collapsed state -->
    <el-tooltip
      v-if="collapsed && title"
      :content="title"
      placement="right"
      :show-after="500"
    >
      <div class="nav-item__tooltip-target"></div>
    </el-tooltip>
  </component>
</template>

<script setup>
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import { ArrowRight } from '@element-plus/icons-vue'

const props = defineProps({
  to: {
    type: String,
    required: true
  },
  title: {
    type: String,
    required: true
  },
  icon: {
    type: String,
    default: null
  },
  badge: {
    type: [String, Number],
    default: null
  },
  badgeType: {
    type: String,
    default: 'count', // 'count', 'new'
    validator: (value) => ['count', 'new'].includes(value)
  },
  collapsed: {
    type: Boolean,
    default: false
  },
  isSubItem: {
    type: Boolean,
    default: false
  },
  hasChildren: {
    type: Boolean,
    default: false
  },
  expanded: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['click'])

const route = useRoute()

// 判断是否为外部链接
const isExternal = computed(() => {
  return /^https?:\/\//.test(props.to)
})

// 判断是否为当前活动路由
const isActive = computed(() => {
  if (isExternal.value) return false
  return route.path === props.to || route.path.startsWith(props.to + '/')
})

const handleClick = (event) => {
  emit('click', event)
}
</script>

<style lang="scss" scoped>
.nav-item {
  @apply relative flex items-center gap-3 px-3 py-2.5 rounded-lg text-gray-700 hover:bg-gray-50 hover:text-gray-900 transition-all duration-200 mb-1 no-underline;

  &:hover {
    transform: translateX(2px);
  }

  &--active {
    @apply bg-primary-50 text-primary-700 font-medium;

    .nav-item__icon {
      @apply text-primary-600;
    }

    .nav-item__indicator {
      @apply opacity-100;
    }
  }

  &--collapsed {
    @apply justify-center px-2;

    .nav-item__icon {
      @apply w-8 h-8 flex items-center justify-center;
    }
  }

  &--sub {
    @apply ml-6 text-sm py-2;

    &::before {
      content: '';
      @apply absolute left-0 top-1/2 w-2 h-px bg-gray-300 transform -translate-y-1/2;
    }
  }
}

.nav-item__icon {
  @apply w-5 h-5 text-gray-500 flex-shrink-0 transition-colors duration-200;

  .nav-item--collapsed & {
    @apply w-6 h-6;
  }
}

.nav-item__content {
  @apply flex-1 flex items-center justify-between;
}

.nav-item__text {
  @apply text-sm font-medium;
}

.nav-item__badge {
  @apply px-2 py-0.5 text-xs font-medium rounded-full transition-all duration-200;

  &--count {
    @apply bg-gray-100 text-gray-600;

    .nav-item--active & {
      @apply bg-primary-100 text-primary-700;
    }
  }

  &--new {
    @apply bg-green-100 text-green-700 animate-pulse;
  }
}

.nav-item__arrow {
  @apply w-4 h-4 text-gray-400 transition-transform duration-200;

  &--expanded {
    @apply transform rotate-90;
  }
}

.nav-item__indicator {
  @apply absolute left-0 top-1/2 w-1 h-6 bg-primary-600 rounded-r-full transform -translate-y-1/2 opacity-0 transition-opacity duration-200;
}

.nav-item__tooltip-target {
  @apply absolute inset-0;
}

// 悬浮动画效果
.nav-item {
  &::before {
    content: '';
    @apply absolute inset-0 rounded-lg bg-gradient-to-r from-primary-500 to-secondary-500 opacity-0 transition-opacity duration-300;
    z-index: -1;
  }

  &:hover::before {
    @apply opacity-5;
  }

  &--active::before {
    @apply opacity-10;
  }
}

// 加载动画
.nav-item__icon {
  .loading & {
    @apply animate-pulse;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .nav-item {
    @apply py-3;

    &--sub {
      @apply ml-4;
    }
  }
}

// 暗色主题
.dark {
  .nav-item {
    @apply text-gray-300 hover:bg-gray-700 hover:text-white;

    &--active {
      @apply bg-primary-900/30 text-primary-300;

      .nav-item__icon {
        @apply text-primary-400;
      }
    }
  }

  .nav-item__badge {
    &--count {
      @apply bg-gray-700 text-gray-300;

      .nav-item--active & {
        @apply bg-primary-800 text-primary-200;
      }
    }

    &--new {
      @apply bg-green-800 text-green-200;
    }
  }
}
</style>
