<template>
  <div class="content-renderer" :class="{ 'chat-message': isChatMessage }">
    <!-- 渲染HTML内容 -->
    <div v-if="isHtml" v-html="processedContent"></div>

    <!-- 渲染Markdown内容 -->
    <div v-else-if="isMarkdown">
      <div v-for="(block, index) in parsedMarkdown" :key="index">
        <!-- 标题 -->
        <h3 v-if="block.type === 'heading'">{{ block.text }}</h3>

        <!-- 段落 -->
        <p
          v-else-if="block.type === 'paragraph'"
          v-html="processInline(block.text)"
        ></p>

        <!-- 列表 -->
        <ul v-if="block.type === 'unordered-list'" class="md-list">
          <li
            v-for="(item, idx) in block.items"
            :key="idx"
            v-html="processInline(item)"
          ></li>
        </ul>
        <ol v-else-if="block.type === 'ordered-list'" class="md-list">
          <li
            v-for="(item, idx) in block.items"
            :key="idx"
            v-html="processInline(item)"
          ></li>
        </ol>

        <!-- 引用 -->
        <blockquote
          v-else-if="block.type === 'quote'"
          v-html="processInline(block.text)"
        ></blockquote>

        <!-- 代码块 -->
        <pre v-else-if="block.type === 'code'" class="code-block">
          <code v-html="highlightCode(block.code, block.lang)"></code>
        </pre>
      </div>
    </div>

    <!-- 渲染纯文本 -->
    <div v-else class="plain-text">{{ content }}</div>
  </div>
</template>

<script setup>
import { computed } from "vue";
import DOMPurify from "dompurify";
// import hljs from "highlight.js";
// import "highlight.js/styles/github.css";

const props = defineProps({
  content: {
    type: String,
    required: true,
  },
  type: {
    type: String,
    default: "auto", // 'html', 'markdown', 'text', 'auto'
  },
  isChatMessage: {
    type: Boolean,
    default: false,
  },
});

// 自动检测内容类型
const contentType = computed(() => {
  if (props.type !== "auto") return props.type;

  const content = props.content.trim();
  if (/<[a-z][\s\S]*>/i.test(content)) return "html";
  if (/^#{1,6}\s|\*\*|__|```|\[.*\]\(.*\)/.test(content)) return "markdown";
  return "text";
});

const isHtml = computed(() => contentType.value === "html");
const isMarkdown = computed(() => contentType.value === "markdown");

// 安全处理HTML内容
const processedContent = computed(() => {
  return DOMPurify.sanitize(props.content);
});

// 解析Markdown内容
const parsedMarkdown = computed(() => {
  return parseMarkdownLike(props.content);
});

// // 高亮代码块
// function highlightCode(code, lang) {
//   if (lang && hljs.getLanguage(lang)) {
//     return hljs.highlight(code, { language: lang }).value;
//   }
//   return escapeHtml(code);
// }

function escapeHtml(str) {
  return str
    .replace(/&/g, "&amp;")
    .replace(/</g, "&lt;")
    .replace(/>/g, "&gt;")
    .replace(/"/g, "&quot;")
    .replace(/'/g, "&#039;");
}

function processInline(text) {
  // 粗体
  text = text.replace(/\*\*(.+?)\*\*/g, "<strong>$1</strong>");
  // 斜体
  text = text.replace(/_(.+?)_/g, "<em>$1</em>");
  text = text.replace(/\*(.+?)\*/g, "<em>$1</em>");
  // 链接
  text = text.replace(
    /\[(.*?)\]\((.*?)\)/g,
    '<a href="$2" target="_blank">$1</a>'
  );
  // 内联代码
  text = text.replace(/`(.*?)`/g, '<code class="inline-code">$1</code>');

  return text;
}

function parseMarkdownLike(text) {
  const lines = text.split("\n");
  const result = [];
  let currentList = null;
  let currentQuote = null;
  let codeBlock = null;

  // 添加段落合并逻辑
  let currentParagraph = null;

  for (let line of lines) {
    // 去除前后空格
    line = line.trim();

    // 代码块开始
    const codeMatch = line.match(/^```(\w*)$/);
    if (codeMatch) {
      if (codeBlock) {
        // 结束当前代码块
        result.push(codeBlock);
        codeBlock = null;
      } else {
        codeBlock = { type: "code", lang: codeMatch[1] || "", code: "" };
      }
      continue;
    }

    if (codeBlock) {
      codeBlock.code += line + "\n";
      continue;
    }

    // 标题
    if (/^### /.test(line)) {
      result.push({ type: "heading", text: line.replace(/^###\s*/, "") });
      continue;
    }

    // 无序列表
    if (/^\s*-\s/.test(line)) {
      if (!currentList) {
        currentList = { type: "unordered-list", items: [] };
      }
      currentList.items.push(line.replace(/^\s*-\s*/, ""));
      continue;
    }

    // 有序列表
    if (/^\d+\.\s/.test(line)) {
      if (!currentList) {
        currentList = { type: "ordered-list", items: [] };
      }
      currentList.items.push(line.replace(/^\d+\.\s*/, ""));
      continue;
    }

    // 引用
    if (/^>\s/.test(line)) {
      if (!currentQuote) {
        currentQuote = { type: "quote", text: "" };
      }
      currentQuote.text += line.replace(/^>\s*/, "") + "\n";
      continue;
    }

    // 空行
    if (line.trim() === "") {
      if (currentList) {
        result.push(currentList);
        currentList = null;
      }
      if (currentQuote) {
        result.push(currentQuote);
        currentQuote = null;
      }
      continue;
    }

    // 普通段落合并
    if (line && !/^#{1,6}\s|\*\*|__|```|\[.*\]\(.*\)|> |- |\d+\./.test(line)) {
      if (!currentParagraph) {
        currentParagraph = { type: "paragraph", text: line };
      } else {
        currentParagraph.text += " " + line;
      }
      continue;
    }

    // 遇到新元素时提交当前段落
    if (currentParagraph) {
      result.push(currentParagraph);
      currentParagraph = null;
    }

    if (currentList) result.push(currentList);
    if (currentQuote) result.push(currentQuote);

    result.push({ type: "paragraph", text: line });
  }

  // 提交最后一个段落
  if (currentParagraph) {
    result.push(currentParagraph);
  }

  if (currentList) result.push(currentList);
  if (currentQuote) result.push(currentQuote);

  return result;
}
</script>

<style scoped>
.content-renderer {
  font-size: 14px;
  line-height: 1.6;
  color: #333;
  word-break: break-word;
  white-space: pre-wrap;
}

.content-renderer.chat-message {
  background-color: #f8f9fa;
  padding: 12px 16px;
  border-radius: 8px;
  margin: 8px 0;
}

/* 标题样式 */
.content-renderer h3 {
  font-size: 1.2em;
  font-weight: bold;
  color: #2c3e50;
  margin: 16px 0 8px;
  padding-bottom: 4px;
  border-bottom: 1px solid #eee;
}

/* 段落样式 */
.content-renderer p {
  margin: 8px 0;
}

/* 链接样式 */
.content-renderer a {
  color: #0366d6;
  text-decoration: none;
  border-bottom: 1px solid rgba(3, 102, 214, 0.2);
}

.content-renderer a:hover {
  color: #0366d6;
  border-bottom-color: #0366d6;
}

/* 代码块样式 */
.content-renderer .code-block {
  background-color: #f6f8fa;
  border-radius: 6px;
  padding: 16px;
  overflow-x: auto;
  margin: 12px 0;
  font-family: "SFMono-Regular", Consolas, "Liberation Mono", Menlo, monospace;
  font-size: 13px;
}

/* 列表样式 */
.content-renderer .md-list {
  padding-left: 24px;
  margin: 12px 0;
}

.content-renderer .md-list li {
  margin: 6px 0;
  position: relative;
}

/* 引用样式 */
.content-renderer blockquote {
  border-left: 4px solid #dfe2e5;
  color: #6a737d;
  padding: 0 16px;
  margin: 16px 0;
  background-color: #f6f8fa;
  border-radius: 0 6px 6px 0;
}

/* 纯文本样式 */
.content-renderer .plain-text {
  white-space: pre-wrap;
}
</style>
