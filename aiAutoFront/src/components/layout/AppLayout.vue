<template>
  <div class="app-layout">
    <!-- 顶部导航栏 -->
    <header class="app-header">
      <div class="header-content">
        <!-- Logo 区域 -->
        <div class="logo-section">
          <button
            @click="toggleSidebar"
            class="sidebar-toggle"
            :class="{ 'active': !sidebarCollapsed }"
          >
            <el-icon><Menu /></el-icon>
          </button>
          <div class="logo">
            <img src="@/assets/svg/ai_icon1.svg" alt="AutoMatrix" class="logo-icon" />
            <span class="logo-text">AutoMatrix</span>
          </div>
        </div>

        <!-- 右侧操作区 -->
        <div class="actions-section">
          <!-- 用户菜单 -->
          <el-dropdown trigger="click" placement="bottom-end">
            <div class="user-profile">
              <div class="user-avatar">
                <span>{{ userInitials }}</span>
              </div>
              <span class="user-name">{{ loginlabel }}</span>
              <el-icon class="dropdown-arrow"><ArrowDown /></el-icon>
            </div>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item>
                  <el-icon><User /></el-icon>
                  个人资料
                </el-dropdown-item>
                <el-dropdown-item>
                  <el-icon><Setting /></el-icon>
                  设置
                </el-dropdown-item>
                <el-dropdown-item divided @click="logout">
                  <el-icon><SwitchButton /></el-icon>
                  退出登录
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
    </header>

    <!-- 主体内容区 -->
    <div class="app-body">
      <!-- 侧边栏 -->
      <aside
        class="app-sidebar"
        :class="{
          'collapsed': sidebarCollapsed,
          'expanded': !sidebarCollapsed
        }"
      >
        <nav class="sidebar-nav">
          <!-- 核心功能 -->
          <NavSection title="核心功能" :collapsed="sidebarCollapsed">
            <NavItem
              v-for="item in mainNavItems"
              :key="item.path"
              :to="item.path"
              :title="item.title"
              :icon="item.icon"
              :badge="item.badge"
              :collapsed="sidebarCollapsed"
            />
          </NavSection>

          <!-- 任务管理 -->
          <NavSection title="任务管理" :collapsed="sidebarCollapsed">
            <NavGroup
              title="任务列表"
              icon="List"
              :collapsed="sidebarCollapsed"
              :default-expanded="taskGroupExpanded"
              @toggle="toggleTaskGroup"
            >
              <NavItem
                v-for="item in taskNavItems"
                :key="item.path"
                :to="item.path"
                :title="item.title"
                :collapsed="sidebarCollapsed"
                :is-sub-item="true"
              />
            </NavGroup>
          </NavSection>

          <!-- 智能工具 -->
          <NavSection title="智能工具" :collapsed="sidebarCollapsed">
            <NavItem
              v-for="item in toolNavItems"
              :key="item.path"
              :to="item.path"
              :title="item.title"
              :icon="item.icon"
              :badge="item.isNew ? 'NEW' : null"
              :badge-type="item.isNew ? 'new' : 'count'"
              :collapsed="sidebarCollapsed"
            />
          </NavSection>

          <!-- 管理员功能 -->
          <NavSection
            v-if="isAdmin"
            title="系统管理"
            :collapsed="sidebarCollapsed"
            :show-divider="true"
          >
            <NavItem
              to="#"
              title="后台管理"
              icon="Avatar"
              :collapsed="sidebarCollapsed"
              @click="openManagement"
            />
          </NavSection>
        </nav>
      </aside>

      <!-- 主内容区 -->
      <main class="app-main">
        <div class="main-content">
          <router-view />
        </div>
      </main>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import {
  Menu, User, Setting, SwitchButton,
  ArrowDown, ArrowRight, HomeFilled, Connection, MessageBox,
  Grid, List, PieChart, Tools, Avatar, Camera
} from '@element-plus/icons-vue'
import NavItem from '@/components/navigation/NavItem.vue'
import NavGroup from '@/components/navigation/NavGroup.vue'
import NavSection from '@/components/navigation/NavSection.vue'
import { useResponsiveSidebar, useBreakpoint } from '@/utils/responsive'

const router = useRouter()

// 响应式数据
const taskGroupExpanded = ref(true)

// 使用响应式侧边栏
const { isCollapsed: sidebarCollapsed, toggleSidebar, isMobile } = useResponsiveSidebar()
const { currentBreakpoint } = useBreakpoint()

// 用户信息
const loginlabel = localStorage.getItem('loginlabel') || '用户'
const isAdmin = localStorage.getItem('isAdmin') === '1'

// 计算用户名首字母
const userInitials = computed(() => {
  return loginlabel.split('').slice(0, 2).join('').toUpperCase()
})

// 导航菜单数据
const mainNavItems = [
  { path: '/home', title: '项目管理', icon: 'HomeFilled' },
  { path: '/requires', title: '需求管理', icon: 'Connection' },
  { path: '/material', title: '物料管理', icon: 'MessageBox' },
  { path: '/suite', title: '用例集', icon: 'Grid' },
  { path: '/report', title: '数据总览', icon: 'PieChart' }
]

const taskNavItems = [
  { path: '/execute', title: '执行列表' },
  { path: '/task', title: '评估任务' }
]

const toolNavItems = [
  { path: '/image-to-testcase', title: '图生用例', icon: 'Camera', isNew: true }
]

// 方法
// toggleSidebar 方法已由 useResponsiveSidebar 提供

const toggleTaskGroup = () => {
  if (!sidebarCollapsed.value) {
    taskGroupExpanded.value = !taskGroupExpanded.value
  }
}

const logout = () => {
  localStorage.removeItem('token')
  localStorage.removeItem('loginlabel')
  localStorage.removeItem('isAdmin')
  router.push('/ssoLogin')
}

const openManagement = () => {
  window.open(router.resolve({ name: 'manage' }).href, '_blank')
}

// 生命周期
onMounted(() => {
  // 检查本地存储的侧边栏状态
  const savedCollapsed = localStorage.getItem('sidebarCollapsed')
  if (savedCollapsed !== null) {
    sidebarCollapsed.value = JSON.parse(savedCollapsed)
  }
})

// 监听侧边栏状态变化，保存到本地存储
watch(sidebarCollapsed, (newValue) => {
  localStorage.setItem('sidebarCollapsed', JSON.stringify(newValue))
})
</script>

<style lang="scss" scoped>
.app-layout {
  @apply h-screen flex flex-col bg-gray-50;
}

// ===== 顶部导航栏 =====
.app-header {
  @apply bg-white border-b border-gray-200 shadow-sm z-50;
  height: 64px;
}

.header-content {
  @apply h-full px-6 flex items-center justify-between;
}

.logo-section {
  @apply flex items-center gap-4;
}

.sidebar-toggle {
  @apply w-10 h-10 rounded-lg border border-gray-200 bg-white hover:bg-gray-50 flex items-center justify-center transition-all duration-200;

  &.active {
    @apply bg-primary-50 border-primary-200 text-primary-600;
  }
}

.logo {
  @apply flex items-center gap-3;
}

.logo-icon {
  @apply w-8 h-8;
}

.logo-text {
  @apply text-xl font-bold;
  background: linear-gradient(135deg, var(--color-primary-600), var(--color-secondary-600));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}



// ===== 右侧操作区 =====
.actions-section {
  @apply flex items-center gap-2;
}



.user-profile {
  @apply flex items-center gap-3 px-3 py-2 rounded-lg hover:bg-gray-50 cursor-pointer transition-all duration-200;
}

.user-avatar {
  @apply w-8 h-8 rounded-full bg-primary-600 text-white flex items-center justify-center text-sm font-medium;
}

.user-name {
  @apply text-sm font-medium text-gray-700;
}

.dropdown-arrow {
  @apply w-4 h-4 text-gray-400;
}

// ===== 主体区域 =====
.app-body {
  @apply flex-1 flex overflow-hidden;
}

// ===== 侧边栏 =====
.app-sidebar {
  @apply bg-white border-r border-gray-200 transition-all duration-300 ease-in-out;

  &.collapsed {
    width: 64px;
  }

  &.expanded {
    width: 256px;
  }
}

.sidebar-nav {
  @apply h-full overflow-y-auto scrollbar-hide p-4;
}

.nav-section {
  @apply mb-6;

  &:last-child {
    @apply mb-0;
  }
}

.nav-section-title {
  @apply text-xs font-semibold text-gray-400 uppercase tracking-wider mb-3 px-3;
}

.nav-item {
  @apply flex items-center gap-3 px-3 py-2.5 rounded-lg text-gray-700 hover:bg-gray-50 hover:text-gray-900 transition-all duration-200 mb-1 no-underline;

  &.active {
    @apply bg-primary-50 text-primary-700 font-medium;

    .nav-icon {
      @apply text-primary-600;
    }
  }

  &--sub {
    @apply ml-6 text-sm;
  }
}

.nav-icon {
  @apply w-5 h-5 text-gray-500 flex-shrink-0;
}

.nav-text {
  @apply flex-1 text-sm;
}

.nav-badge {
  @apply px-2 py-0.5 text-xs font-medium bg-gray-100 text-gray-600 rounded-full;

  &--new {
    @apply bg-green-100 text-green-700;
  }
}

// ===== 导航组 =====
.nav-group-header {
  @apply flex items-center gap-3 px-3 py-2.5 rounded-lg text-gray-700 hover:bg-gray-50 cursor-pointer transition-all duration-200 mb-1;

  &.expanded {
    .expand-icon {
      @apply transform rotate-90;
    }
  }
}

.expand-icon {
  @apply w-4 h-4 text-gray-400 transition-transform duration-200 ml-auto;
}

.nav-group-items {
  @apply ml-3 border-l border-gray-200 pl-3;
}

// ===== 主内容区 =====
.app-main {
  @apply flex-1 overflow-hidden;
}

.main-content {
  @apply h-full overflow-y-auto bg-gray-50 p-6;
}

// ===== 响应式设计 =====
@media (max-width: 768px) {
  .app-sidebar {
    @apply fixed left-0 top-16 h-full z-40;

    &.collapsed {
      @apply -translate-x-full;
    }
  }



  .header-content {
    @apply px-4;
  }

  .main-content {
    @apply p-4;
  }
}

// ===== 暗色主题支持 =====
.dark {
  .app-layout {
    @apply bg-gray-900;
  }

  .app-header {
    @apply bg-gray-800 border-gray-700;
  }

  .app-sidebar {
    @apply bg-gray-800 border-gray-700;
  }

  .nav-item {
    @apply text-gray-300 hover:bg-gray-700 hover:text-white;

    &.active {
      @apply bg-primary-900 text-primary-300;
    }
  }

  .main-content {
    @apply bg-gray-900;
  }
}
</style>
