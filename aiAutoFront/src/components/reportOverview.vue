<template>

      <div>
          <div style="margin-top: 2%">
          <span>项目名称1</span>
          <el-input
        v-model="input2"
        class="w-50 m-2"
        placeholder="请输入项目名称"
       style="width: 10%;"
      />
      <el-button type="primary" plain>查询</el-button>
  </div>
<!-- <h3>指标总览</h3>

  <el-row :gutter="20" style="margin-left: 7%;">
    <el-col :span="5">
       <el-card shadow="always">
        <div class="statistic-card">
        <el-statistic :value="1111">

        </el-statistic>
        <div class="statistic-footer">
          <div class="footer-item">
            <span>需求个数</span>
            <span class="green">
              10

            </span>
          </div>
        </div>
      </div>
       </el-card>
    </el-col>
    <el-col :span="5">
      <el-card shadow="always"> <div class="statistic-card">


        <div class="statistic-footer">
          <div class="footer-item">

            <span>用例集</span>
            <span  class="green">
              12%

            </span>
          </div>
        </div>
         <div class="statistic-footer">
          <div class="footer-item">

            <span>用例</span>
            <span  class="green">
              12%

            </span>
          </div>
        </div>
      </div> </el-card>

    </el-col>

     <el-col :span="5">
      <el-card shadow="always">  <div class="statistic-card">


        <div class="statistic-footer">
          <div class="footer-item">

            <span>用例集</span>
            <span  class="green">
              12%

            </span>
          </div>
        </div>
         <div class="statistic-footer">
          <div class="footer-item">

            <span>用例</span>
            <span  class="green">
              12%

            </span>
          </div>
        </div>
      </div> </el-card>

    </el-col>
    <el-col :span="5">
      <el-card shadow="always" style="height: 98%;">
         <div >

        <div class="statistic-footer">
          <div class="footer-item">
            <span>than yesterday</span>
            <span class="green">
              16%

            </span>
          </div>

        </div>
           <div class="statistic-footer">
          <div class="footer-item">
            <span>than yesterday</span>
            <span class="green">
              16%

            </span>
          </div>

        </div>
           <div class="statistic-footer">
          <div class="footer-item">
            <span>than yesterday</span>
            <span class="green">
              16%

            </span>
          </div>

        </div>
      </div> </el-card>


    </el-col>
  </el-row> -->
  <h3>指标总览</h3>
        <el-descriptions style="margin-top:1%; width: 80%; margin-left:5%;"  :column="3" border>
          <el-descriptions-item v-for="(value, key) in project.metrics" :key="key" :label="formatMetricName(key)">
            {{ value }}
          </el-descriptions-item>
        </el-descriptions>

        <section class="chart-section1">
          <div >
          <h3>RAG指标统计</h3>
      </div>

          <div ref="chartDom" class="chart-container" style="margin-left: 5%;"></div>

      </section>
  <section class="chart-section1">
      <div >
          <h3>RAG指标总计</h3>
      </div>

  <el-row style="height: 100%;">

  <div ref="pieDom" style="height: 100%; width: 30%;margin-left: 10%;" ></div>


     <div style="margin-left: 10%;">
      <div style="margin-top: -8%;">
          <label for="category">指标类别：</label>
        <el-select v-model="selectedCategory" placeholder="请选择" @change="updateChart">
          <el-option
            v-for="item in categories"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </div>


     </div>
      <div ref="pieChartDom" class="chart-container111" style="width: 30%; "></div>


    </el-row>
  </section>
  <div>

         <el-row :gutter="20">
      <el-col :span="16">
          <h3>执行结果对比</h3>
      </el-col>

    </el-row>

    <el-row>
      <el-col :span="6" style="margin-left: 5%;">
       <span>用例集</span>
        <el-select v-model="value" class="m-2" placeholder="Select" style=" width: 30%;">
      <el-option
        v-for="item in options"
        :key="item.value"
        :label="item.label"
        :value="item.value"
      />
    </el-select>
      </el-col>
      <el-col :span="6" style="margin-left: -10%;">
        <span>用例</span>
        <el-select v-model="value" class="m-2" placeholder="Select" style=" width: 30%;
      margin-left: 0%;
     ">
      <el-option
        v-for="item in options"
        :key="item.value"
        :label="item.label"
        :value="item.value"
      />
    </el-select>

      </el-col>
      <el-col :span="6" style="margin-left: -10%;">
        <span>执行次数对比</span>
        <el-input
        style="width: 10%;"
        v-model="input3"
        class="w-50 m-2"
        size="small"

      />
      {{ "次和" }}
      <el-input
        style="width: 10%;"
        v-model="input3"
        class="w-50 m-2"
        size="small"

      />
      {{ "次" }}
      </el-col>

      <el-col :span="6" >
        <el-button type="primary">查询</el-button>
      </el-col>

    </el-row>





         <section class="chart-section1">
          <el-button type="primary" @click="dialogTableVisible = true" style="margin-left: 87%; ">数据详情</el-button>


  <div id="lineDom1"  class="chart-container" style="margin-left: 6%;"></div>

  </section>

  <el-dialog v-model="dialogTableVisible" title="数据对比" style="height: 80%;">
      <el-row>
      <el-col :span="12">
      <p>
          数据1：xxxxxx<br/>

          数据2：xxxxxx
          <br/>
          数据3：xxxxxx
      </p>

      </el-col>
      <el-col :span="12">
          <p>
          数据1：xxxxxx<br/>

          数据2：xxxxxx
          <br/>
          数据3：xxxxxx
      </p>
      </el-col>
    </el-row>
    </el-dialog>

  </div>

      </div>

    </template>

    <script setup>
    import { ref, onMounted } from 'vue'
    import * as echarts from 'echarts'

    const dialogTableVisible = ref(false)
    const categories = [
    { label: '所有', value: 'all' },
    { label: '类别A', value: 'A' },
    { label: '类别B', value: 'B' },
    { label: '类别C', value: 'C' }
  ]
  const selectedCategory = ref('all')
  let caseNotPassData= ref([])

  const dataSources = {
    all: [
      { value: 335, name: 'a' },
      { value: 310, name: 'b' },
      { value: 234, name: 'c' },
      { value: 135, name: 'd' },
      { value: 1548, name: 'e' }
    ],
    A: [
      { value: 235, name: 'a' },
      { value: 210, name: 'a-A' }
    ],
    B: [
      { value: 134, name: 'b-B' },
      { value: 135, name: 'b-B' }
    ],
    C: [
      { value: 548, name: 'c-C' }
    ]
  }

  let pieChartInstance = null
    const project = {
        "project_name": "ycytest",
        "requirement_count": 9,
        "suite_count": 4,
        "test_case_count": 4,
        "metrics": {
          "project_name": "ycytest",
        "requirement_count": 9,
        "suite_count": 4,
        "test_case_count": 4,
          "context_precision": 33.33,
          "context_recall": 91.67,
          "context_entity_recall": 83.33,
          "faithfulness": 75,
          "answer_relevancy": 100
        }
      };

      const formatMetricName = (key) => {
        const names = {
          "project_name": "项目名称",
        "requirement_count": "需求个数",
        "suite_count": "用例集个数",
        "test_case_count": "用例个数",
          "context_precision": "上下文准确率",
          "context_recall": "上下文召回率",
          "context_entity_recall": "实体召回率",
          "faithfulness": "忠实度",
          "answer_relevancy": "答案相关性"
        };
        return names[key];
      };
    // 卡片数据
    const cards = [
      { title: '需求数量', description: '10个' },
      { title: '用例集数量', description: '10个' },
      { title: '用例数量', description: '100个' }
    ]

    const tableData = [
    {

      name: 'context_precision',
      address: '100',
    },
    {
      name: 'context_recall',
      address: '100',
    },
    {
      name: 'context_entity_recall',
      address: '100',
    },
    {
      name: "faithfulness",
      address: '100',
    },
    {
      name: 'answer_relevancy',
      address: '100',
    }
  ]
    // ECharts DOM 引用
    const chartDom = ref(null)
    let chartInstance = null

    const pieDom = ref(null)
    let chartInstance1 = null
    onMounted(() => {
      if (chartDom.value) {
        chartInstance = echarts.init(chartDom.value)

        const option = {

    tooltip: {
      trigger: 'axis'
    },

    legend: {
      data: ['小于0.6', '0.6-0.7', '0.7-0.8','0.8-0.9', '0.9以上']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },

    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: ['Name1', 'Name2', 'Name3', 'Name4', 'Name5']
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '小于0.6',
        type: 'line',
        stack: 'Total',
        data: [120, 132, 101, 134, 90, 230, 210]
      },
      {
        name: '0.6-0.7',
        type: 'line',
        stack: 'Total',
        data: [220, 182, 191, 234, 290, 330, 310]
      },
      {
        name: '0.7-0.8',
        type: 'line',
        stack: 'Total',
        data: [150, 232, 201, 154, 190, 330, 410]
      },
      {
        name:  '0.8-0.9',
        type: 'line',
        stack: 'Total',
        data: [320, 332, 301, 334, 390, 330, 320]
      },
      {
        name: '0.9以上',
        type: 'line',
        stack: 'Total',
        data: [820, 932, 901, 934, 1290, 1330, 1320]
      }
    ]
        }

        chartInstance.setOption(option)
      }


    chartInstance1 = echarts.init(pieDom.value)

    const pieoption = {

    tooltip: {
      trigger: 'item'
    },
    legend: {
      orient: 'vertical',
      left: 'left'
    },
    series: [
      {
        name: 'Access From',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 40,
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: [
          { value: 1048, name: 'A' },
          { value: 735, name: 'B' },
          { value: 580, name: 'C' },
          { value: 484, name: 'D' },
          { value: 300, name: 'E' }
        ]
      }
    ]
  };
  chartInstance1.setOption(pieoption)
  initChart()
  initChart1()

  })

  const initChart = () => {
    const chartDom = document.querySelector('.chart-container111')
    pieChartInstance = echarts.init(chartDom)

    const option = {
      title: {
      text: 'RAG指标占比添加',

      left: 'center'
    },
      series: [
        {
          name: '访问来源',
          type: 'pie',
          radius: '50%',
          data: dataSources[selectedCategory.value],
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    }

    pieChartInstance.setOption(option)
  }



  const initChart1 = () => {
   let chartDom = document.getElementById('lineDom1');
   let myChart = echarts.init(chartDom);

    let   option = {

   tooltip: {
     trigger: 'axis',
     axisPointer: {
       type: 'cross',
       label: {
         backgroundColor: '#6a7985'
       }
     }
   },
   legend: {
     data: ['第1次执行', '第2次执行']
   },
   grid: {
     left: '3%',
     right: '4%',
     bottom: '3%',
     containLabel: true
   },
   xAxis: [
     {
       type: 'category',
       boundaryGap: false,
       data: ['指标1', '指标2', '指标3', '指标4', '指标5', '指标6']
     }
   ],
   yAxis: [
     {
       type: 'value'
     }
   ],
   series: [
     {
       name: '第1次执行',
       type: 'line',
       stack: 'Total',
       areaStyle: {},
       emphasis: {
         focus: 'series'
       },
       data: [120, 132, 101, 134, 90, 230, 210]
     },
     {
       name: '第2次执行',
       type: 'line',
       stack: 'Total',
       areaStyle: {},
       emphasis: {
         focus: 'series'
       },
       data: [220, 182, 191, 234, 290, 330, 310]
     }

   ]
  };
   myChart.setOption(option)

  }

    </script>




   <style scoped>
   :global(h2#card-usage ~ .example .example-showcase) {
  background-color: var(--el-fill-color) !important;
}

.el-statistic {
  --el-statistic-content-font-size: 28px;
}

.statistic-card {
  height: 100%;
  padding: 20px;
  border-radius: 4px;
  background-color: var(--el-bg-color-overlay);
}

.statistic-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  font-size: 12px;
  color: var(--el-text-color-regular);
  margin-top: 16px;
}

.statistic-footer .footer-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.statistic-footer .footer-item span:last-child {
  display: inline-flex;
  align-items: center;
  margin-left: 4px;
}

.green {
  color: var(--el-color-success);
}
.red {
  color: var(--el-color-error);
}

  .label {
    color: #606266;
  }


    /* .container {
      max-width: 1200px;
      margin: auto;
      padding: 2rem;
      font-family: Arial, sans-serif;
    }  */



    .chart-section1 {
      margin-top: 3rem;
      height: 400px;
    }

    .chart-container {
      width: 83%;
      height: 100%;
    }
    </style>