import { fileURLToPath, URL } from 'node:url'

import { defineConfig , loadEnv} from 'vite'
import vue from '@vitejs/plugin-vue'
import vueDevTools from 'vite-plugin-vue-devtools'
import {
  createSvgIconsPlugin
} from 'vite-plugin-svg-icons'
import path from 'path'

export default defineConfig({
  plugins: [
    vue(),
    vueDevTools(),
    createSvgIconsPlugin({
      // 指定要缓存的图标文件夹
      iconDirs: [path.resolve(process.cwd(), 'src/assets')],
      // 指定symbolId格式
      symbolId: 'icon-[name]',
    }),
  ],
  server: {
    //处理跨域
    proxy: {
      '/api': {
        target: 'http://127.0.0.1:8080/', // automatrix 后端接口
        changeOrigin: true,
        rewrite: path => path.replace(/^\/api/, '/api')
      },
      '/project': {
        target: 'http://127.0.0.1:8080/', // 后端接口的域名
        changeOrigin: true,
        rewrite: path => path.replace(/^\/project/, '')
      },
    },

    proxyTable: {
      '/project': {
        target: 'http://10.35.223.53:8008/', //假的接口地址哈
        changeOrigin: true,
        rewrite: path => path.replace(/^\/project/, '')
      }}
  },


  // devServer: {
  //   proxy: {
  //     '/project': {
  //       target: 'http://127.0.0.1:8080/', // 后端 API 地址
  //       changeOrigin: true,          // 是否改变源
  //       pathRewrite: { '^/project': '' }, // 如果需要重写路径
  //     },
  //   },
  // },
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    },

  },
})
