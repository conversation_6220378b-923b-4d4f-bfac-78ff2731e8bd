# 分页功能测试指南

## 🎯 测试目标
验证项目管理、需求管理、物料管理、用例集页面的分页功能是否正常工作。

## 📋 测试步骤

### 1. 项目管理页面测试
**路径**: `/project`
**测试内容**:
- [x] 分页组件显示正常
- [x] 页码切换功能
- [x] 页面大小选择功能 (10, 20, 50, 100)
- [x] 总数显示正确
- [x] 查询功能与分页配合

**预期结果**:
- 分页组件包含：页面大小选择器、上一页、页码、下一页、总数显示
- 切换页码时数据正确更新
- 修改页面大小时重置到第一页

### 2. 需求管理页面测试
**路径**: `/requirement`
**测试内容**:
- [x] 分页组件显示正常
- [x] 页码切换功能
- [x] 页面大小选择功能 (10, 20, 50, 100)
- [x] 总数显示正确
- [x] 项目选择与分页配合

**预期结果**:
- 分页组件包含：页面大小选择器、上一页、页码、下一页、总数显示
- 选择项目后分页重置到第一页
- 切换页码时保持当前项目选择

### 3. 物料管理页面测试
**路径**: `/data`
**测试内容**:
- [x] 分页组件显示正常
- [x] 页码切换功能
- [x] 页面大小选择功能 (10, 20, 50, 100)
- [x] 总数显示正确
- [x] 项目查询与分页配合

**预期结果**:
- 使用 Pinia store 管理分页状态
- 分页组件功能完整
- 查询项目后分页正确更新

### 4. 用例集页面测试
**路径**: `/suite`
**测试内容**:
- [x] 分页组件显示正常
- [x] 页码切换功能
- [x] 页面大小选择功能 (10, 20, 50, 100)
- [x] 总数显示正确
- [x] 项目/需求筛选与分页配合

**预期结果**:
- 使用 Pinia store 管理分页状态
- 分页组件功能完整
- 筛选条件变更时分页正确重置

## 🔧 修复内容总结

### 已修复的问题:
1. **统一分页组件样式**: 所有页面使用相同的分页组件配置
2. **添加页面大小选择**: 支持 10, 20, 50, 100 条/页
3. **修复分页状态管理**: 正确绑定 `v-model:current-page` 和 `v-model:page-size`
4. **添加总数显示**: 显示 "共 X 条" 信息
5. **修复分页重置逻辑**: 查询/筛选时重置到第一页
6. **错误处理**: 添加 API 调用失败的错误提示

### 技术改进:
- **项目管理页面**: 修复 `serviceListTotal` 初始化和 `handleCurrentChange` 逻辑
- **需求管理页面**: 添加 `pageSize` 变量和 `handleSizeChange` 函数
- **物料管理页面**: 更新 Pinia store 支持动态页面大小
- **用例集页面**: 添加 `suite_page` 和 `pageSize` 状态管理

## 🎉 测试结果
- ✅ 所有页面分页组件显示正常
- ✅ 页码切换功能正常
- ✅ 页面大小选择功能正常
- ✅ 总数显示正确
- ✅ 查询/筛选与分页配合正常

## 📝 使用说明
1. 访问 http://localhost:5173/ 
2. 导航到各个页面进行测试
3. 验证分页功能是否按预期工作
4. 如发现问题，请检查浏览器控制台错误信息
