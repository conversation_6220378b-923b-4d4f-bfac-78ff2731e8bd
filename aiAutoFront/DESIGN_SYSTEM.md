# AutoMatrix 设计系统

## 概述

AutoMatrix 设计系统是一套基于科技感主题的统一设计语言，旨在为整个前端应用提供一致的视觉体验和交互规范。

## 设计原则

### 1. 科技感
- 使用现代化的蓝色调作为主色
- 采用简洁的几何形状和线条
- 运用微妙的发光效果和阴影

### 2. 一致性
- 统一的色彩系统
- 标准化的间距和尺寸
- 规范的组件样式

### 3. 可访问性
- 符合WCAG 2.1标准的对比度
- 支持键盘导航
- 清晰的视觉层次

### 4. 响应式
- 移动优先的设计理念
- 灵活的布局系统
- 适配多种屏幕尺寸

## 色彩系统

### 主色调 (Primary)
- **用途**: 主要操作按钮、链接、重要信息
- **色值**: #3b82f6 (蓝色)
- **变体**: 50-900 九个层级

### 辅助色 (Secondary)
- **用途**: 次要操作、辅助信息
- **色值**: #14b8a6 (青绿色)
- **变体**: 50-900 九个层级

### 强调色 (Accent)
- **用途**: 特殊状态、高亮显示
- **色值**: #d946ef (紫色)
- **变体**: 50-900 九个层级

### 语义化颜色
- **成功**: #10b981 (绿色)
- **警告**: #f59e0b (橙色)
- **错误**: #ef4444 (红色)
- **信息**: #3b82f6 (蓝色)

## 字体系统

### 字体族
- **无衬线**: Inter, Helvetica Neue, 微软雅黑
- **等宽**: JetBrains Mono, Fira Code

### 字体大小
- **xs**: 12px - 辅助文字
- **sm**: 14px - 正文
- **base**: 16px - 标准文字
- **lg**: 18px - 小标题
- **xl**: 20px - 中标题
- **2xl**: 24px - 大标题
- **3xl**: 30px - 页面标题
- **4xl**: 36px - 主标题
- **5xl**: 48px - 超大标题

### 字重
- **light**: 300 - 轻量文字
- **normal**: 400 - 正常文字
- **medium**: 500 - 中等文字
- **semibold**: 600 - 半粗体
- **bold**: 700 - 粗体

## 间距系统

基于 4px 网格系统：

- **1**: 4px
- **2**: 8px
- **3**: 12px
- **4**: 16px
- **5**: 20px
- **6**: 24px
- **8**: 32px
- **10**: 40px
- **12**: 48px
- **16**: 64px
- **20**: 80px
- **24**: 96px
- **32**: 128px

## 组件规范

### 按钮
- **小型**: 32px 高度
- **中型**: 40px 高度 (默认)
- **大型**: 48px 高度
- **圆角**: 8px
- **内边距**: 水平 16px

### 输入框
- **小型**: 32px 高度
- **中型**: 40px 高度 (默认)
- **大型**: 48px 高度
- **圆角**: 8px
- **内边距**: 水平 12px

### 卡片
- **圆角**: 12px
- **内边距**: 24px
- **阴影**: 基础阴影
- **边框**: 1px 浅灰色

### 表格
- **行高**: 48px (默认)
- **内边距**: 16px
- **边框**: 1px 浅灰色
- **斑马纹**: 交替背景色

## 动画系统

### 持续时间
- **快速**: 150ms - 微交互
- **正常**: 300ms - 标准动画
- **慢速**: 500ms - 复杂动画

### 缓动函数
- **ease-in**: 进入动画
- **ease-out**: 退出动画
- **ease-in-out**: 双向动画

## 响应式断点

- **xs**: 480px - 小型手机
- **sm**: 640px - 大型手机
- **md**: 768px - 平板
- **lg**: 1024px - 小型桌面
- **xl**: 1280px - 桌面
- **2xl**: 1536px - 大型桌面

## 使用指南

### CSS 类命名规范
- 使用 `am-` 前缀标识 AutoMatrix 组件
- 采用 BEM 命名方式
- 例如: `.am-card`, `.am-btn--primary`, `.am-nav__item--active`

### CSS 变量使用
```css
/* 颜色 */
color: var(--color-primary-600);
background: var(--bg-primary);

/* 间距 */
padding: var(--space-4);
margin: var(--space-2);

/* 字体 */
font-family: var(--font-sans);
font-size: var(--text-base);
```

### Tailwind CSS 集成
设计系统与 Tailwind CSS 完全兼容，可以混合使用：

```html
<div class="am-card p-6 bg-white shadow-lg">
  <h2 class="text-xl font-semibold text-gray-900">标题</h2>
  <p class="text-gray-600 mt-2">内容</p>
  <button class="am-btn am-btn--primary mt-4">操作</button>
</div>
```

## 最佳实践

1. **优先使用设计系统组件**
2. **保持视觉一致性**
3. **遵循响应式设计原则**
4. **注意可访问性要求**
5. **合理使用动画效果**
6. **定期更新和维护**
